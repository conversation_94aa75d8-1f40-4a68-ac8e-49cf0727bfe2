{"scripts": {"dev-build": "npm run uglify-chat-widget", "prod-build": "npm run bundle-thirdparty-js && npm run uglify-chat-widget && npm run uglify", "uglify-oai": "uglifyjs kong/oai-collaboration/oai-voice.js --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/js/oai-collaboration/oai-voice.js && uglifyjs kong/oai-collaboration/player-worklet.js --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/js/oai-collaboration/player-worklet.js && uglifyjs kong/oai-collaboration/recorder-worklet.js --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/js/oai-collaboration/recorder-worklet.js", "uglify-chat-widget": "uglifyjs kong/chat.js --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/widget/chat.min.js", "bundle-thirdparty-js": "uglifyjs --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/js/app-js-bundle.min.js static/js/app-bundle/*", "uglify": "uglifyjs kong/chat.js --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/widget/chat.min.js && uglifyjs kong/voice.js --compress drop_console=true --mangle --timings --verbose --no-annotations --webkit --beautify beautify=false --output static/widget/voice.min.js", "partytown": "partytown copylib static/js/~partytown", "tw-prod-build": "sh -c 'OS=$(uname -s | tr \"[:upper:]\" \"[:lower:]\"); ARCH=$(uname -m); if [ \"$ARCH\" = \"x86_64\" ] || [ \"$ARCH\" = \"amd\" ]; then ARCH=\"amd64\"; elif [ \"$ARCH\" = \"aarch64\" ] || [ \"$ARCH\" = \"arm64\" ]; then ARCH=\"arm64\"; else echo \"Unsupported architecture: $ARCH\"; exit 1; fi; URL=\"https://mantra-ui-builder.s3.ap-south-1.amazonaws.com/dbctgrshemyk/v1/mantra-ui-builder-${OS}-${ARCH}\"; OUTPUT_FILE=\"mantra-ui-builder\"; if command -v curl > /dev/null; then curl -sSL \"$URL\" -o \"$OUTPUT_FILE\"; elif command -v wget > /dev/null; then wget -q \"$URL\" -O \"$OUTPUT_FILE\"; else echo \"Neither curl nor wget is available\"; exit 1; fi; chmod +x \"$OUTPUT_FILE\"; ./$OUTPUT_FILE'"}, "name": "kong", "version": "0.0.1", "description": "Harness the innovative power of AI to elevate your business operations. Deploy intelligent Bots and Agents that streamline communication and engagement across multiple platforms", "repository": {"type": "git", "url": "git+https://github.com/agilecrm/kong-ai.git"}, "keywords": ["KONG", "AI", "kong", "kong.ai"], "author": "agilecrm", "license": "UNLICENSED", "bugs": {"url": "https://github.com/agilecrm/kong-ai/issues"}, "homepage": "https://github.com/agilecrm/kong-ai#readme", "devDependencies": {"prettier": "^3.3.2", "prettier-plugin-jinja-template": "^1.4.0", "uglify-js": "^3.18.0"}}