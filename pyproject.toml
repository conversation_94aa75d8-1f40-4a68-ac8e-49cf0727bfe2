[tool.poetry]
name = "mantra-blogs"
version = "0.1.0"
description = ""
authors = ["chapalamadugu <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "app" }]
include = ["static/**"]

[tool.poetry.dependencies]
python = "^3.9,<3.13"
uvicorn = "^0.29.0"
jinja2 = "^3.1.4"
python-jose = "^3.3.0"
boto3 = "^1.34.113"
pymongo = "^4.7.2"
pypdf2 = "^3.0.1"
fastapi = "^0.111.0"
cryptography = "^42.0.7"
httpx = "^0.27.0"
python-docx = "^1.1.2"
bs4 = "^0.0.2"
together = "^1.2.0"
requests = { extras = ["socks"], version = "^2.32.3" }
user-agents = "^2.2.0"
loguru = "^0.7.2"
stripe = "^9.9.0"
google-api-python-client = "^2.131.0"
google-auth = "^2.30.0"
google-auth-oauthlib = "^1.2.0"
google-auth-httplib2 = "^0.2.0"
pydantic-settings = "^2.3.1"
limits = {extras = ["redis"], version = "^3.12.0"}
psutil = "^6.0.0"
sendgrid = "^6.11.0"
jwt = "^1.3.1"
razorpay = "^1.4.2"
setuptools = "^74.1.0"
pandas = "^2.2.3"
markdown2 = "^2.5.1"
openai = "^1.97.1"


[tool.poetry.group.dev.dependencies]
black = "^24.4.2"
isort = "^5.13.2"
pyright = "^1.1.365"
codespell = "^2.3.0"

[tool.pyright]
exclude = [
    "**/.cache",
    "**/.pyenv",
    "**/.venv",
    "**/node_modules",
    "**/__pycache__",
    "src/experimental",
    "src/typestubs",
    "src/typestubs",
    "tailwindcss",
    "static",
]

defineConstant = { DEBUG = true }

pythonPlatform = "Linux"

reportMissingImports = true
reportMissingTypeStubs = false
typeCheckingMode = "strict"
reportAssignmentType = false
reportUnknownMemberType = false
reportUnknownVariableType = false
reportUnknownArgumentType = false
reportOptionalMemberAccess = false
reportAttributeAccessIssue = false

[tool.isort]
profile = "black"
src_paths = ["app"]
combine_as_imports = true

[tool.codespell]
skip = '*.po,*.ts,*.csv,./tailwindcss,./dist,./src/3rdParty,./src/Test,.venv,poetry.lock'
ignore-regex = '.*(Stati Uniti|Tense=Pres).*'
count = true
quiet-level = 3

[tool.black]
line-length = 200
include = '\.pyi?$'
extend-exclude = '''
/(
  # The following are specific to Black, you probably don't want those.
  tests/data
  | profiling
  | .github
  | app/data
)/
'''

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
