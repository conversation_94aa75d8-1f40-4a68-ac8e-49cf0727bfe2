name: PROD-PIPELINE
run-name: 🚀 deployed to the production environment by ${{ github.actor }} 🚀

on:
  workflow_dispatch:
    inputs:
      tag:
        type: string
        description: which tag you want to deploy to production i.e v1.0.1
        required: true
      
jobs:
  deploy:
    runs-on: ubuntu-22.04
    if: contains(from<PERSON>son('["agilecrm", "satya-500"]'), github.actor)
    env:
      TEST_TAG: 500apps/kong-ai:test
      PROD_TAG: 500apps/kong-ai:prod
      CACHE_TAG: 500apps/kong-ai:prod-cache
      CACHE_TEST_TAG: 500apps/kong-ai:prod-cache-test

    steps:
      - name: "🐌 validate tag"
        run: |
          #!/bin/bash

          if [[ ! "${{ github.event.inputs.tag }}" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "tag is not following semantic versioning (vX.Y.Z)"
            exit 143
          fi
        shell: bash
      
      - uses: actions/checkout@v4
        name: "🚶‍♂️ checkout to branch"
        with:
          ref: ${{ github.event.inputs.tag }}
      
      - name: "🌹 build tailwindcss and minify js"
        run: |
          #!/bin/bash

          make build
        shell: bash
        env:
          BUILDER_KEY: ${{ secrets.BUILDER_KEY }}
      
      - name: "🐣 update version number"
        run: |
          #!/bin/bash

          make version_update TAG=${{ github.event.inputs.tag }}

        shell: bash
      
      - name: "🔧 set up docker buildx"
        uses: docker/setup-buildx-action@v3
      
      - name: "🔑 login to docker hub"
        uses: docker/login-action@v3
        with:
          username: 500apps
          password: ${{ secrets.DOCKER_SECRET }}
      
      - name: "🐥 build amd64 image and export to docker for testing"
        uses: docker/build-push-action@v6
        with:
          context: .
          load: true
          push: false
          platforms: linux/amd64
          tags: ${{ env.TEST_TAG }}
          cache-from: |
            ${{ env.CACHE_TEST_TAG }}
          cache-to: |
            ${{ env.CACHE_TEST_TAG }}
        env:
          DOCKER_BUILD_SUMMARY: false
      
      - name: "🧪 test docker image"
        run: |
          #!/bin/bash

          docker run -d -p 8076:8000 -v $(pwd)/app-log:/opt/app-log --name kongai_test ${{ env.TEST_TAG }}
          sleep 30
          
          if curl -s -o /dev/null --fail http://127.0.0.1:8076; then
            echo "test passed!"
          else
            echo "test failed!"
            docker logs kongai_test
            docker stop kongai_test
            docker rm kongai_test
            exit 1
          fi
          docker rm -f kongai_test || true
        shell: bash
      
      - name: "⚙️ build arm64 image and upload docker image"
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ env.PROD_TAG }}
          platforms: linux/arm64
          cache-from: |
            ${{ env.CACHE_TAG }}
          cache-to: |
            ${{ env.CACHE_TAG }}
        env:
          DOCKER_BUILD_SUMMARY: false

      - name: "🔩 setup SSH key"
        run: |
          #!/bin/bash

          ssh-agent -a $SSH_AUTH_SOCK > /dev/null
          ssh-add - <<< "${{ secrets.SSH_PEM_PROD }}"
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        shell: bash

      - name: "📡 rolling update service over ssh"
        run: |
          #!/bin/bash

          ssh -o StrictHostKeyChecking=no -p 7059 root@************ <<'ENDSSH'
          echo $DOCKER_SECRET | docker login --username 500apps --password-stdin

          docker image prune -f

          docker pull 500apps/kong-ai:prod

          docker service update --image $(docker inspect 500apps/kong-ai:prod --format='{{index .Id}}') kongai_app
          ENDSSH
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        shell: bash
