
name: AUTO-DEPLOY-PIPELINE
run-name: 🚀 https://api.blogs.kong.ai 🚀

on:
  push:
    branches:
      - develop
      
jobs:
  deploy:
    runs-on: ubuntu-22.04
    env:
      SSH_AUTH_SOCK: /tmp/ssh_agent.sock

    steps:
      - uses: actions/checkout@v4
        name: "🚶‍♂️ checkout to branch"
        with:
          ref: develop

      - name: "🔩 setup SSH key"
        run: |
          #!/bin/bash

          ssh-agent -a $SSH_AUTH_SOCK > /dev/null
          ssh-add - <<< "${{ secrets.DEV_SSH_PEM }}"
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        shell: bash

      - name: "📡 SSH and deploy"
        run: |
          ssh -o StrictHostKeyChecking=no root@************** <<'ENDSSH'
          cd mantra-blogs
          git stash
          git pull
          curl -sSL https://install.python-poetry.org | python3 -
          export PATH="/root/.local/bin:$PATH"
          poetry env use python3
          poetry lock --no-update
          make build
          poetry install
          npm install
          npm run prod-build
          pkill uvicorn
          ps -ef | grep python | awk -F " " '{print $2}' | sudo xargs kill -9
          nohup poetry run uvicorn app.main:app --host 0.0.0.0 --workers=4 --port 61616 > uvicorn.log 2>&1 &
          ENDSSH
        env:
          POETRY_VERSION: 1.7.1
          APP_DEBUG: False
          REGION: "prod"
          ENABLE_GZIP: False
