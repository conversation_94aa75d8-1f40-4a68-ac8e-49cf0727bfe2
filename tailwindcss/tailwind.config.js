/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: "class",
  content: [
    "../app/templates/**/*.html",
    "!../app/templates/admin/**",
    "!../app/templates/admin.html"
  ],
  theme: {
    extend: {
        keyframes: {
        'zoom-in': {
          '0%': { opacity: 0, transform: 'scale(0.5)' },
          '100%': { opacity: 1, transform: 'scale(1)' },
        },
        scaleUp: {
          '0%': { transform: 'scale(0.8) translateY(1000px)', opacity: 0 },
          '100%': { transform: 'scale(1) translateY(0)', opacity: 1 },
        },
        fadeInLeft: {
          '0%': { opacity: 0, transform: 'translateX(-20px)' },
          '100%': { opacity: 1, transform: 'translateX(0)' },
        },
      },
      animation: {
        'zoom-in': 'zoom-in .6s ease-in-out',
        'scale-up': 'scaleUp 0.5s cubic-bezier(0.165, 0.84, 0.44, 1)',
        'fade-in-left': 'fadeInLeft 0.5s cubic-bezier(0.165, 0.84, 0.44, 1)',
      },
    },
  },
  plugins: [],
}
