.PHONY: build version_update

all: run

dev-build: # If chat.mini.js isn't working, you probably forgot to run this command, dude.
	npm install && npm run dev-build

build: build-tailwindcss
	npm install && npm run prod-build && npm run uglify-oai

build-tailwindcss:
	# cd tailwindcss && npm install && npm run build
	npm run tw-prod-build


version_update:
	@if [ -z "$(TAG)" ]; then \
	    echo "Please provide a TAG variable in the format vX.Y.Z"; \
	    exit 1; \
	fi
	perl -pi -e 's/__version__ = "v[0-9]+\.[0-9]+\.[0-9]+"/__version__ = "$(TAG)"/' app/__init__.py
	@echo "Updated version to $(TAG) in app/__init__.py"

