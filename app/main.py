from datetime import datetime
from platform import node

from fastapi import FastAP<PERSON>, Request, WebSocket, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from psutil import boot_time
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.responses import HTMLResponse, Response, JSONResponse
import secrets
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from base64 import b64decode
import traceback

from app import __version__
from app.routers import site, blogs, authors, resources, socials, blogs_resources_content_generator, social_content_generator, ai_image_generator, text_humanizer
from app.util.settings import app_settings
from app.util.templates import templates
from app.util.gharpe.projects import projects

ADMIN_USERNAME = "blog"
ADMIN_PASSWORD = "bloglist"

# You can reuse this dictionary whenever you need to respond with 401 + the "WWW-Authenticate" header.
UNAUTHORIZED_HEADERS = {"WWW-Authenticate": 'Basic realm="Restricted Access"'}


def decode_basic_auth(auth_header: str):
    """
    Given a Basic Auth header, return the (username, password) tuple.
    Raises ValueError if the header is invalid or not in the expected format.
    """
    scheme, credentials = auth_header.split()
    if scheme.lower() != "basic":
        raise ValueError("Invalid auth scheme")

    decoded = b64decode(credentials).decode("utf-8")
    username, _, password = decoded.partition(":")
    if not username or not password:
        raise ValueError("Missing username or password")

    return username, password


class BasicAuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path == "/admin":
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                # Return JSON response with "Not authenticated" detail
                return Response(
                    content='{"detail":"Not authenticated"}',
                    status_code=401,
                    media_type="application/json",
                    headers={"WWW-Authenticate": 'Basic realm="Restricted Access"'},
                )

            try:
                username, password = decode_basic_auth(auth_header)
            except (ValueError, IndexError):
                return Response(
                    content='{"detail":"Not authenticated"}',
                    status_code=401,
                    media_type="application/json",
                    headers={"WWW-Authenticate": 'Basic realm="Restricted Access"'},
                )

            if not (secrets.compare_digest(username, ADMIN_USERNAME) and secrets.compare_digest(password, ADMIN_PASSWORD)):
                return Response(
                    content='{"detail":"Not authenticated"}',
                    status_code=401,
                    media_type="application/json",
                    headers={"WWW-Authenticate": 'Basic realm="Restricted Access"'},
                )

        return await call_next(request)


app = FastAPI(**app_settings.fastapi_kwargs)

# Add the auth middleware before CORS
app.add_middleware(BasicAuthMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/admin")
async def admin_panel(request: Request, page: str = Query(default="blogs")):
    metadata = {
        "title": "Mantra Blogs",
        "description": "",
    }

    return templates.TemplateResponse("admin/index.html", {"request": request, "metadata": metadata, "projects": projects, "page": page})


@app.get("/_app/_version")
def get_system_info():
    boot_at: datetime = datetime.fromtimestamp(boot_time()).strftime("%Y-%m-%d %H:%M:%S")

    system_info = {"BOOT_TIME": boot_at, "HOSTNAME": node(), "VERSION": __version__}

    return system_info


@app.exception_handler(StarletteHTTPException)
async def custom_404_handler(request: Request, exc: StarletteHTTPException):
    if exc.status_code == 404:
        # Provide default metadata for the 404 page
        metadata = {
            "title": "AI Conversational ChatBots & AI Agents | Kong.ai",
            "description": "Grow your business like the Fortune 500 using AI Conversational Chatbots and AI Agents. Train your existing data, create WhatsApp, email, and voice bots with Kong.ai.",
            "ogImage": "https://kong.ai/static/images/404_error.png",
            "canonicalUrl": str(request.url),
        }
        # Render the 404.html template with the provided metadata
        return templates.TemplateResponse("/landing/404.html", {"request": request, "metadata": metadata}, status_code=404)
    return HTMLResponse(content=str(exc.detail), status_code=exc.status_code)


app.mount("/static", StaticFiles(directory="static"), name="static")


# Include Routers
app.include_router(authors.router)
app.include_router(resources.router)
app.include_router(socials.router)
app.include_router(site.router)
app.include_router(blogs.router)
app.include_router(blogs_resources_content_generator.router)
app.include_router(social_content_generator.router)
app.include_router(ai_image_generator.router)
app.include_router(text_humanizer.router)


# WebSocket route
@app.websocket("/ws")
async def websocket_route(websocket: WebSocket):
    await websocket_endpoint(websocket)
