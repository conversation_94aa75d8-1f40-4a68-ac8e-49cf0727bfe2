import base64
import requests
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Union
from app.util.text_utils import sanitize_filename
from app.util.settings import app_settings
from openai import OpenAI

APP_DEBUG = app_settings.APP_DEBUG


def generate_image(prompt):
    try:
        api_key = app_settings.OPENAI_API_KEY_FOR_IMAGE_GENERATION.get_secret_value()
        if not api_key:
            print("Warning: OPENAI_API_KEY not set. Image generation will fail.")
            return None

        client = OpenAI(api_key=api_key)
        print(f"[PROD MODE] Calling OpenAI API for image generation with prompt: {prompt[:50]}...")

        response = client.responses.create(model="gpt-4.1-mini", input=prompt, tools=[{"type": "image_generation"}])

        # Print the usage
        print(f"Usage: {response.usage}")

        # Extract image data from the response
        image_data = [output.result for output in response.output if output.type == "image_generation_call"]

        return image_data[0] if image_data else None
    except Exception as e:
        print(f"Error generating image: {e}")
        return None


# Upload file to S3
# Note: This is not the final prototype, we may need to upload in other bucket
async def upload_to_s3(files, type: str = "images"):
    try:
        url = f"https://api.stark.ai/s3/upload/public/{type}"
        response = requests.request("POST", url, files=files)
        return response.json().get("s3_url")
    except Exception as error:
        print("Error occurred during resume saving to S3, Error", error)
        return ""


async def process_and_upload_image(image_data: Union[str, bytes], title: str, folder: str = "images") -> Optional[str]:
    """
    Process image data and upload to S3.
    Args:
        image_data: Base64 string or binary image data
        title: Title to use for generating filename
        folder: S3 folder to upload to (default: "images")
    Returns:
        str: S3 URL if successful, None if failed
    """
    if not image_data:
        return None
    try:
        # Convert image data to binary format
        binary_image_data = convert_image_to_binary(image_data)
        if not binary_image_data:
            return None
        # Create sanitized filename from title
        file_name = sanitize_filename(title)
        # Prepare file for S3 upload
        content_type = "image/png"  # Default to PNG
        files = [("file", (file_name, binary_image_data, content_type))]
        # Upload to S3
        s3_url = await upload_to_s3(files, folder)
        return s3_url
    except Exception as e:
        print(f"Error processing and uploading image: {e}")
        return None


def convert_image_to_binary(image_data: Union[str, bytes]) -> Optional[bytes]:
    """
    Convert image data to binary format.
    Args:
        image_data: Base64 string or binary image data
    Returns:
        bytes: Binary image data, None if conversion failed
    """
    try:
        if isinstance(image_data, str):
            # Handle base64 encoded string
            return decode_base64_image(image_data)
        elif isinstance(image_data, bytes):
            # Already binary data
            return image_data
        else:
            print(f"Unsupported image data type: {type(image_data)}")
            return None
    except Exception as e:
        print(f"Error converting image to binary: {e}")
        return None


def decode_base64_image(base64_string: str) -> Optional[bytes]:
    """
    Decode base64 image string to binary data.
    Args:
        base64_string: Base64 encoded image string (with or without data URI prefix)
    Returns:
        bytes: Decoded binary image data, None if decoding failed
    """
    try:
        # Remove data URI prefix if present (e.g., "data:image/png;base64,")
        if "base64," in base64_string:
            base64_string = base64_string.split("base64,")[1]
        # Decode base64 to binary
        binary_data = base64.b64decode(base64_string)
        return binary_data
    except Exception as e:
        print(f"Error decoding base64 image: {e}")
        return None


def validate_image_data(image_data: Union[str, bytes]) -> bool:
    """
    Validate if image data is in a supported format.
    Args:
        image_data: Image data to validate
    Returns:
        bool: True if valid, False otherwise
    """
    if not image_data:
        return False
    if isinstance(image_data, str):
        # Check if it's a valid base64 string
        try:
            # Remove data URI prefix if present
            clean_data = image_data
            if "base64," in clean_data:
                clean_data = clean_data.split("base64,")[1]
            # Try to decode
            base64.b64decode(clean_data)
            return True
        except Exception:
            return False
    elif isinstance(image_data, bytes):
        # Basic validation for binary data
        return len(image_data) > 0

    return False


async def generate_and_upload_content_image(title: str, content: str, app_name: str, folder: str = "images") -> Tuple[Optional[str], bool]:
    """
    Generate an image and upload to S3.

    This is a high-level function that coordinates the entire workflow:
    1. Generate image using AI
    2. Process and upload to S3
    3. Return URL and success status
    Args:
        title: Content title for image prompt and filename
        content: Content description for image prompt
        app_name: App name for logging
        folder: S3 folder to upload to
    Returns:
        tuple: (s3_url, success) - S3 URL if successful and success boolean
    """
    try:
        # Development mode flag - set to True to use mock data instead of API calls
        # Check if we're in development mode
        # if APP_DEBUG:
        #     print(f"[DEV MODE] Generating mock image for title: {title}...")
        #     mock_url = generate_mock_image_data()
        #     return mock_url, True

        # Create prompt for image generation
        prompt = f"Generate an image for the content titled '{title}' with content: {content}"
        print(f"Generating image for {app_name}... Prompt: {prompt[:100]}...")

        # Generate image using AI
        image_data = generate_image(prompt)

        if not image_data:
            print(f"No image data returned for {app_name}.")
            return None, False

        # Validate image data
        if not validate_image_data(image_data):
            print(f"Invalid image data received for {app_name}.")
            return None, False

        # Process and upload image
        s3_url = await process_and_upload_image(image_data, title, folder)

        if s3_url:
            print(f"Image for {app_name} uploaded to S3 with URL: {s3_url}")
            return s3_url, True
        else:
            print(f"Failed to upload image to S3 for {app_name}.")
            return None, False

    except Exception as e:
        print(f"Error in generate_and_upload_content_image for {app_name}: {e}")
        return None, False


def generate_mock_image_data():
    """
    Generate a mock image URL for testing.
    """
    test_image_url = "https://stark-ai-resume.s3.amazonaws.com/public/images/159e0fcf-abd2-4b67-ab01-d99e5ba09829-unlocking-opportunities-for-fresh-graduates-your-g.png"
    return test_image_url
