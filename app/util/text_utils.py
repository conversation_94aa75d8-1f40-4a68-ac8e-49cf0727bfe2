import re
import uuid


def slugify(text: str) -> str:
    """Convert text into a slug (lowercase, hyphenated, no trailing hyphen, no spaces around hyphens)."""
    text = text.lower().strip()  # Lowercase and remove leading/trailing spaces
    text = re.sub(r"[^\w\s-]", "", text)  # Remove special characters
    text = re.sub(r"\s*-+\s*", "-", text)  # Remove spaces around hyphens
    text = re.sub(r"\s+", "-", text)  # Replace spaces with hyphens
    text = re.sub(r"-+", "-", text)  # Replace consecutive hyphens with a single hyphen
    text = re.sub(r"^-|-$", "", text)  # Remove hyphen at the beginning and end
    return text


def sanitize_filename(filename: str, max_length=50, extension=".png"):
    """
    Create a sanitized filename suitable for file systems.

    Args:
        filename (str): The original filename
        max_length (int): Maximum length for the filename (default: 50)
        extension (str): File extension to add (default: ".png")

    Returns:
        str: Sanitized filename with extension
    """
    if not filename:
        return f"file_{str(uuid.uuid4())[:8]}{extension}"

    # Use slugify for consistent text processing
    sanitized = slugify(filename)

    # Limit length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length].rstrip("-")

    return f"{sanitized}{extension}"
