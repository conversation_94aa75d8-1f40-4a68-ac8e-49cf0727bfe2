import hashlib
import json
import pickle
import sys
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

import requests
from fastapi.logger import logger
from fastapi.templating import Jinja2Templates
from jinja2 import Environment, Template as JTemplate
from markupsafe import Markup

from app.util.settings import app_settings


@dataclass
class SingletonTemplates:
    """
    Singleton class for managing Jinja2 templates with additional custom configurations.

    Attributes:
        directory (str): The directory containing the templates.
        extensions (List[str]): A list of extensions to add to the Jinja2 environment.
        template (Jinja2Templates): The Jinja2Templates instance for rendering templates.
    """

    directory: str = app_settings.TEMPLATE_DIR
    extensions: List[str] = field(default_factory=lambda: ["jinja2.ext.do"])
    _instance: Optional["SingletonTemplates"] = None
    template: Jinja2Templates = field(init=False)
    loaded_html_files_data: Dict[str, str] = field(init=False)

    def __post_init__(self) -> None:
        """
        Initialize the Jinja2 environment and update globals and filters.
        """
        url = "https://ui.mantragroup.com/get-blocks/"
        headers = {"X-Mantra-ApiKey": "MT-3ht2x4xnih3qanueou83f"}
        deployment_hash = self.calculate_md5("static/css/app.css") if app_settings.APP_DEBUG is False else None
        app_js_bundle_hash = self.calculate_md5("static/js/app-js-bundle.min.js") if app_settings.APP_DEBUG is False else None

        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()
                self.loaded_html_files_data = pickle.loads(response.content)
                break
            except (requests.RequestException, pickle.PickleError) as _e:
                logger.error(f"Attempt {attempt + 1} failed")
                if attempt < max_retries - 1:
                    time.sleep(5)
                else:
                    logger.error("Failed to load data blocks binary after 3 attempts")
                    sys.exit(143)

        logger.warning(f"total {len(self.loaded_html_files_data)} building blocks are loaded")

        templates = Jinja2Templates(directory=self.directory)
        env: Environment = templates.env
        env.globals.update(APP_DEBUG=app_settings.APP_DEBUG)  # type: ignore
        env.globals.update(APP_NAME=app_settings.APP_NAME)  # type: ignore
        env.globals.update(COPYRIGHT_YEAR=app_settings.COPYRIGHT_YEAR)  # type: ignore
        env.globals.update(DEPLOYMENT_HASH=deployment_hash)  # type: ignore
        env.globals.update(APP_JS_BUNDLE_HASH=app_js_bundle_hash)  # type: ignore
        env.globals.update(get_block=self.get_block)  # type: ignore

        # Add custom filter for loading JSON
        env.filters["json_loads"] = json.loads

        for extension in self.extensions:
            env.add_extension(extension)

        self.template = templates

    def calculate_md5(self, file_path: str) -> str:
        md5_hash = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()

    def get_block(self, block_name: str, payload: Dict[Any, Any] = {}) -> Markup:
        """
        Render a block of HTML content from a pickle-stored template with the provided payload.

        Args:
            block_name (str): The name of the block to render.
            payload (Dict[Any, Any]): The context to pass to the template for rendering.

        Returns:
            Markup: The rendered HTML content.
        """
        if not block_name.endswith(".html"):
            block_name += ".html"
        content = self.get_from_bin(block_name)
        if content is None:
            raise ValueError(f"Block name '{block_name}' not found in bin data.")
        template = JTemplate(content)
        rendered_content = template.render(payload)
        return Markup(rendered_content)

    def get_from_bin(self, block_name: str) -> Optional[str]:
        """
        Retrieve content from the bin data by block name.

        Args:
            block_name (str): The name of the block to retrieve.

        Returns:
            Optional[str]: The content associated with the block name or None if not found.
        """
        return self.loaded_html_files_data.get(block_name)

    @classmethod
    def get_instance(cls) -> "SingletonTemplates":
        """
        Get the singleton instance of SingletonTemplates.

        Returns:
            SingletonTemplates: The singleton instance of the class.
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance


templates = SingletonTemplates.get_instance().template
