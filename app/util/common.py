import re
import uuid
from datetime import datetime, timezone
from typing import List, Optional, Union

import markdown2
from bson.objectid import ObjectId
from fastapi import HTTPException

from app.util.db.database import BlogDB, safe_document


def clean_description(text: str, max_chars: int = 100) -> str:
    """Remove HTML and truncate to specified chars"""
    if not text:
        return ""
    # Remove HTML tags
    clean_text = re.sub("<.*?>", "", text)
    # Truncate
    if len(clean_text) > max_chars:
        return clean_text[:max_chars] + "..."
    return clean_text


def serialize_document(document: dict) -> dict:
    """Serialize document for JSON response"""
    if "_id" in document:
        document["_id"] = str(document["_id"])
    return document


def enrich_with_author(items: Union[List[dict], dict], app_name: str) -> Union[List[dict], dict]:
    """
    Enrich items with author information from the authors collection.
    Can handle both single item dict or list of items.
    """
    authors_collection = BlogDB.get_collection(app_name, "authors")

    # Handle single item case
    if isinstance(items, dict):
        items = [items]
        single_item = True
    else:
        single_item = False

    for item in items:
        if "author_uuid" in item and item["author_uuid"]:
            author = authors_collection.find_one({"uuid": item["author_uuid"]})
            if author:
                item["author_name"] = author.get("name", "")
                item["author_title"] = author.get("title", "")
                item["author_email"] = author.get("email", "")
                item["author_image_url"] = author.get("image_url", "")
                item["author_slug"] = author.get("slug", "")
                item["author_linkedin_url"] = author.get("linkedin_url", "")
                item["author_facebook_url"] = author.get("facebook_url", "")
                item["author_x_url"] = author.get("x_url", "")
                author_bio = author.get("bio", "")
                item["author_bio"] = convert_markdown_to_html(author_bio, "author_bio")

    return items[0] if single_item else items


def convert_markdown_to_html(markdown_text: str, field_name: str = "content") -> str:
    """Convert markdown text to HTML with error handling"""
    if not markdown_text:
        return ""

    try:
        return markdown2.markdown(markdown_text)
    except (ValueError, TypeError) as e:
        print(f"Markdown conversion error for {field_name}: {e}")
        return f"<p>Error converting Markdown to HTML for {field_name}</p>"


def handle_publish_date(data: dict) -> None:
    """Handle publish_date conversion safely"""
    if "publish_date" in data and data["publish_date"] and str(data["publish_date"]).strip():
        try:
            data["publish_date"] = datetime.fromisoformat(str(data["publish_date"]))
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid publish_date format: {e}")


def add_metadata_fields(data: dict) -> None:
    """Add UUID and created_date if not present"""
    if "uuid" not in data:
        data["uuid"] = str(uuid.uuid4())

    if "created_date" not in data:
        data["created_date"] = datetime.now(timezone.utc)


def validate_pagination(page: int, limit: int = 100) -> tuple:
    """Validate pagination parameters and return skip value"""
    if page < 1:
        raise HTTPException(status_code=400, detail="Page number must be 1 or greater.")

    if limit > 1000:
        raise HTTPException(status_code=400, detail="Limit cannot exceed 1000.")

    skip = (page - 1) * limit
    return skip, limit


def build_search_filter(base_filter: dict, query: str, search_field: str = "meta_title") -> dict:
    """Build search filter with optional query"""
    search_filter = base_filter.copy()
    if query:
        search_filter[search_field] = {"$regex": query, "$options": "i"}
    return search_filter


def get_document_by_id_or_uuid(collection, identifier: str, uuid_field: str = "uuid") -> dict:
    """Get document by either ObjectId or UUID"""
    is_uuid = len(identifier.split("-")) > 1

    if is_uuid:
        document = collection.find_one({uuid_field: identifier})
    else:
        try:
            document = collection.find_one({"_id": ObjectId(identifier)})
        except:
            raise HTTPException(status_code=400, detail="Invalid ID format")

    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    return document


def clean_and_validate_uuids(uuid_list: List[str]) -> List[str]:
    """Clean and validate a list of UUIDs"""
    valid_uuids = []
    if uuid_list and isinstance(uuid_list, list):
        for uuid_str in uuid_list:
            if uuid_str and isinstance(uuid_str, str) and uuid_str.strip():
                valid_uuids.append(uuid_str.strip())
    return valid_uuids
