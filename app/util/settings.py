from datetime import datetime
from functools import lru_cache
from typing import Any, Dict, Optional

from pydantic import SecretStr
from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    DOCS_URL: Optional[str] = None

    REDOC_URL: Optional[str] = None

    OPENAPI_URL: Optional[str] = None

    APP_NAME: str = "Kong.ai"

    COPYRIGHT_YEAR: int = datetime.now().year

    APP_DEBUG: bool = False

    REGION: str = "prod"

    TEMPLATE_DIR: str = "app/templates"

    EXTENTION_DIR: str = "kong-automation"

    ENABLE_GZIP: bool = True

    # when we go for release just set it to true in app_settings
    DISABLE_BASIC_AUTH: bool = True

    # Pickle File

    # Credentials

    # AWS
    AWS_KEY: str = "********************"
    AWS_SECRET: str = "/PKjQhzXOt5aFjWCNnrR170zwNufQF5e0t23wy+k"
    AWS_REGION: str = "ap-south-1"

    # S3 Buckets
    S3_BUCKET_ADMIN: str = "stark-v2-data"
    S3_BUCKET_PORTAL: str = "stark-v2-data"

    JWT_SECRET: SecretStr = SecretStr("KONGAI2021A!@3AGAGA!@#")

    # PROXY
    PROXY_URL: str = "socks5h://hbsatya:<EMAIL>:32325"

    # AI
    OPENAI_API_KEY: SecretStr = SecretStr("********************************************************")
    OPENAI_API_KEY_FOR_IMAGE_GENERATION: SecretStr = SecretStr(
        "********************************************************************************************************************************************************************"
    )

    HUMBOTAI_API_KEY: SecretStr = SecretStr("api_key_bdb9279f6ba8432fb3527393f200f6ba")

    CHATFIREWORKS_API_KEY: SecretStr = SecretStr("5977om3A41D8RWcDcwBHAmzBl5qzRBjeXLvuwkj3bFkAfya9")

    # get the token from
    # https://business.facebook.com/latest/settings/apps?business_id=584533418869516&bm_redirect_migration=true
    WHATSAPP_PERMANENT_TOKEN: SecretStr = SecretStr(
        "EAAQmkEVdkrEBO2eFkdLyhClif1s4JBgYPxZBYAc2ByTpC3LpZCaPMZC0Kuyksiy3YFyTPUpndmMkAxZBOQBSDAnTNcqIddyZBDPtt3bZAWLsG6c76uIuoKWyn88BL9kBVXq0SnzZA06gB24HC0WLhMQ9zTuLiBZCVs3W7Cv7AXUnZB8BeSk72MM6jhv9seZBPjZBAZDZD"
    )
    WHATSAPP_WEBHOOK_APP_TOKEN: SecretStr = SecretStr("pFobpZGWdMCg")

    # FIND EMAIL SMTP_SERVERS
    SMTP_SERVERS: str = (
        "smtp1.applet.io,smtp2.applet.io,smtp3.applet.io,smtp4.applet.io,smtp5.applet.io,smtp6.applet.io,smtp7.applet.io,smtp8.applet.io,smtp9.applet.io,smtp10.applet.io,smtp11.applet.io,smtp12.applet.io,smtp13.applet.io,smtp14.applet.io,smtp15.applet.io,smtp16.applet.io,smtp17.applet.io,smtp18.applet.io,smtp19.applet.io,smtp20.applet.io"
    )
    # SMTP_SERVERS = "smtp1.applet.io,smtp2.applet.io,smtp3.applet.io,smtp4.applet.io,smtp5.applet.io,smtp6.applet.io,smtp7.applet.io,smtp8.applet.io,smtp9.applet.io,smtp10.applet.io,smtp11.applet.io,smtp12.applet.io,smtp13.applet.io,smtp14.applet.io,smtp15.applet.io,smtp16.applet.io,smtp17.applet.io,smtp18.applet.io,smtp19.applet.io,smtp20.applet.io"

    # Mongo DB
    BLOG_MONGO_DB_CONNECTION: str = "mongodb+srv://admin:<EMAIL>/?retryWrites=true&w=majority&appName=blogs"

    # LI
    LI_CLIENT_ID: str = "78exxofxnv6q24"
    LI_CLIENT_SECRET: str = "j9DJrRMK7G3CU7Ot"
    LI_REDIRECT_URI: str = "http://localhost:8000/li"
    LI_URL: str = "http://li.stark.ai:7059/profile/raw"
    LI_API_KEY: str = "STAI_9amqZv20xkf2aNfrk4dFKyw"

    # SES
    SES_FROM: str = "Team Kong <<EMAIL>>"

    # SERVICE_ACCOUNT_FILE = "app/util/functions/credentials/spreadsheet_creds.json"

    class Config:
        env_file = ".env"
        validate_assignment = True
        str_strip_whitespace = True

    @property
    def fastapi_kwargs(self) -> Dict[str, Any]:
        return {"debug": self.APP_DEBUG, "docs_url": self.DOCS_URL, "openapi_url": self.OPENAPI_URL}

    MANTRA_AI_SERVER: str = "https://api.kong.ai"
    # MANTRA_AI_SERVER: str = "http://127.0.0.1:8080"
    MANTRA_AI_APP: str = "kong.ai"

    AI_LAMBDA_BEARER_TOKEN: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************.IevhRYBkaPGrDXQP58QhWdUBKUdnofKvouH49uGe0zk"
    BLOGS_RESOURCES_CONTENT_API_URL: str = "https://ioyratv10h.execute-api.ap-south-1.amazonaws.com/dev/ai/prompt/6871e79f74371570e7d9958d"
    SOCIAL_CONTENT_API_URL: str = "https://ioyratv10h.execute-api.ap-south-1.amazonaws.com/dev/ai/prompt/68888d7e4162673da7bfdca3"

    # Billing Keys
    BILLING: Dict[str, Dict[str, str]] = {
        "razorpay": {"key_id": "***********************", "key_secret": "Xc3cLpGnI6qjH127pUGbR0sK"},
        "stripe": {
            "api_key": "********************************",
            "publishable_key": "pk_live_tZLiKW8FVXOPz1QM69i024Xl",
            "webhook_secret": "whsec_WJH7a9jy8ltLdqMqQRhQ7OatBQWu1I51",
        },
    }

    APP_DESCRIPTIONS: Dict[str, Dict[str, str]] = {
        "kong_ai": {
            "name": "Kong.ai",
            "description": (
                "Kong.ai is an AI-powered content generation and automation platform that "
                "creates high-quality blog posts, social media content, and marketing materials. "
                "It features advanced SEO optimization, multi-language support, and automated "
                "publishing workflows to help businesses scale their content marketing efforts "
                "with AI-driven insights and analytics."
            ),
        },
        "crm_io": {
            "name": "CRM.io",
            "description": (
                "CRM.io is a comprehensive customer relationship management platform with "
                "AI-powered lead scoring, automated follow-up sequences, and intelligent "
                "sales pipeline management. It integrates with multiple communication channels, "
                "provides detailed analytics, and offers customizable workflows to optimize "
                "customer acquisition and retention strategies."
            ),
        },
        "fav_ai": {
            "name": "Fav.ai",
            "description": (
                "Fav.ai is an AI-driven personalization engine that curates content, products, "
                "and recommendations based on user behavior and preferences. It features "
                "real-time recommendation algorithms, A/B testing capabilities, and advanced "
                "user segmentation to deliver highly targeted experiences across web and mobile "
                "platforms."
            ),
        },
        "stark_ai": {
            "name": "Stark.ai",
            "description": (
                "Stark.ai is an enterprise AI automation platform that streamlines business "
                "processes through intelligent workflow automation, document processing, and "
                "predictive analytics. It offers no-code AI model deployment, integration APIs, "
                "and custom AI solutions for industries like finance, healthcare, and manufacturing."
            ),
        },
        "employers_ai": {
            "name": "Employers.ai",
            "description": (
                "Employers.ai is an AI-powered recruitment and HR management platform that "
                "automates candidate screening, conducts AI interviews, and matches talent "
                "with job requirements. It features resume parsing, skill assessment tools, "
                "and predictive hiring analytics to help companies build better teams faster "
                "while reducing recruitment costs."
            ),
        },
        "finder_io": {
            "name": "Finder.io",
            "description": (
                "Finder.io is an intelligent search and discovery platform that helps users "
                "find relevant information, products, or services using advanced AI algorithms. "
                "It features semantic search capabilities, personalized results, and multi-source "
                "data aggregation to deliver precise and contextually relevant search experiences "
                "across various domains."
            ),
        },
        "allrounder_ai": {
            "name": "AllRounder.ai",
            "description": (
                "AllRounder.ai is a comprehensive edtech companion offering interactive "
                "audio lessons, gamified practice modules (e.g., Word Explorer, Typing Ninja, "
                "Logic Lab), curriculum-aligned mock tests, and an AI Tutor agent that answers "
                "questions in real time. It's designed to boost K12 learning outcomes through "
                "adaptive quizzes, progress tracking, and parent teacher reports."
            ),
        },
        "gharpe": {
            "name": "GharPe",
            "description": (
                "GharPe is a lead-generation and marketing portal for residential real estate "
                "in India. It features curated listings of new and resale projects, targeted "
                "telecalling for builders and inbound call handling for buyers, SEO-optimized "
                "microsites, and AI-driven property discovery to connect prospects with projects "
                "seamlessly."
            ),
        },
        "jobpe": {
            "name": "JobPe",
            "description": (
                "JobPe is an AI-powered job aggregation and recruitment platform that "
                "pulls listings from 20+ job portals, enables one-click applications via "
                "our Application Bot, and conducts AI-driven mock interviews to prescreen "
                "candidates instantly. Employers also get a free branded career portal "
                "to embed on their site for direct applications and analytics."
            ),
        },
        "ai_bull": {
            "name": "TheAIBull",
            "description": (
                "TheAIBull is an AI-driven financial analytics platform that fetches real-time "
                "market data, detects candlestick patterns, computes technical indicators, and "
                "scores trade signals. It also provides backtesting, portfolio analysis and sharing, "
                "and automated strategy execution for algo trading, options, and mutual funds."
            ),
        },
    }


@lru_cache
def get_app_settings() -> AppSettings:
    return AppSettings()


app_settings = get_app_settings()
