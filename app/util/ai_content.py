import json
import time
from typing import Dict, Any
from app.util.settings import app_settings
import requests


def call_ai_lambda(payload: Dict[str, Any], api_url, max_retries: int = 2) -> dict[str, Any]:
    """
    Call the AI Lambda function with retry logic and error handling.

    Args:
        payload (dict): The complete payload to send to the AI Lambda
        max_retries (int): Maximum number of retry attempts (default: 2)

    Returns:
        dict: The JSON response from the AI API

    Raises:
        requests.exceptions.RequestException: For network-related errors
        Exception: For other unexpected errors
    """

    # Production mode - make actual API call with retry logic
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {app_settings.AI_LAMBDA_BEARER_TOKEN}", "X-Mantra-App": "stark.ai"}

    # Set timeout to 300 seconds (5 minutes) for AI API calls which can take time
    timeout = (30, 300)  # (connection timeout, read timeout)

    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                wait_time = 2**attempt  # Exponential backoff: 2s, 4s, 8s...
                app_name = payload.get("input_dict", {}).get("app_name", "Unknown")
                print(f"Retrying AI Lambda call for {app_name} (attempt {attempt + 1}/{max_retries + 1}) after {wait_time}s delay...")
                time.sleep(wait_time)
            else:
                app_name = payload.get("input_dict", {}).get("app_name", "Unknown")
                primary_keyword = payload.get("input_dict", {}).get("primary_keyword", "Unknown")
                print(f"Calling AI Lambda for {app_name} with keyword '{primary_keyword}'")
                print(f"Payload: {json.dumps(payload, indent=2)}")

            response = requests.post(api_url, headers=headers, data=json.dumps(payload), timeout=timeout)
            print(f"Response status: {response.status_code}")

            if response.status_code != 200:
                print(f"Response headers: {dict(response.headers)}")
                print(f"Response text: {response.text}")

            response.raise_for_status()
            if attempt > 0:
                print(f"SUCCESS: AI Lambda call succeeded on attempt {attempt + 1}")
            return response.json()

        except requests.exceptions.Timeout as e:
            if attempt < max_retries:
                print(f"TIMEOUT: Request timed out (attempt {attempt + 1}), will retry: {e}")
                continue
            else:
                print(f"FAILED: Request timed out after {max_retries + 1} attempts: {e}")
                raise

        except requests.exceptions.ConnectionError as e:
            if attempt < max_retries:
                print(f"RETRY: Connection error (attempt {attempt + 1}), will retry: {e}")
                continue
            else:
                print(f"FAILED: Connection error after {max_retries + 1} attempts: {e}")
                raise

        except Exception as e:
            print(f"FAILED: Unexpected error: {e}")
            raise

    # This should never be reached due to the retry logic above
    raise Exception("Maximum retry attempts exceeded without successful response")
