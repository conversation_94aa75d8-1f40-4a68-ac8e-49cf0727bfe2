from datetime import datetime

import certifi
from bson import ObjectId
from pymongo import MongoClient, collection, database

from app.util.settings import app_settings


class BlogMongoDBUtil:
    _client: MongoClient = None

    def __init__(self):
        # Ensure singleton MongoClient
        if not BlogMongoDBUtil._client:
            BlogMongoDBUtil._client = MongoClient(app_settings.BLOG_MONGO_DB_CONNECTION, tlsCAFile=certifi.where(), compressors="zlib")
            try:
                BlogMongoDBUtil._client.admin.command("ping")
                print("✅ Successfully connected to MongoDB!")
            except Exception as e:
                print("❌ MongoDB connection failed:", e)

    def get_database(self, db_name: str) -> database.Database:
        return BlogMongoDBUtil._client[db_name]

    def get_collection(self, db_name: str, collection_name: str) -> collection.Collection:
        return self.get_database(db_name)[collection_name]


from bson import ObjectId


def safe_documents(query_result):
    """
    Converts ObjectId fields to strings in the result of a MongoDB find query.
    """
    documents = list(query_result)
    for doc in documents:
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                doc[key] = str(value)
    return documents


def safe_document(data):
    """
    Recursively convert ObjectId and datetime fields in the data to strings.
    """
    if isinstance(data, dict):
        return {key: safe_document(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [safe_document(item) for item in data]
    elif isinstance(data, ObjectId):
        return str(data)
    elif isinstance(data, datetime):
        return data.isoformat()
    else:
        return data


BlogDB = BlogMongoDBUtil()
