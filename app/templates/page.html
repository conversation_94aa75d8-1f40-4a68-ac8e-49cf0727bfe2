<head>
    <title id="page-title">Blogs</title>
    <!-- Include Tailwind CSS -->
    {{ get_block("core/css" , { "APP_DEBUG" : APP_DEBUG, "DEPLOYMENT_HASH": DEPLOYMENT_HASH}) }}
</head>

<div id="page-container" class="hidden">
</div>
<div id="page-placeholder" class="hidden">
    <section class="relative py-12 lg:py-24 overflow-hidden z-1 text-center">
        <div class="wraper">
            <div class="">
                <div class="mx-auto text-center">
                    <img class="max-w-xs mx-auto" src="https://cdn.kong.ai/static/images/404_error.png" alt="">
                </div>
                <div class="mt-[50px] px-[200px] md:mt-[20px] md:px-[100px] sm:p-0 text-center">
                    <h3 class="text-neutral-900 text-4xl mb-[35px] font-base-font font-semibold heading tracking-tight">
                        We can't
                        seem to find the page you're looking for.

                    </h3>
                </div>
            </div> <!-- end row -->
        </div> <!-- end container -->
    </section>
</div>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const page = JSON.parse('{{ result | tojson }}');
        if (page) {
            preview(page.record)
        } else {
            document.getElementById('page-placeholder').classList.remove('hidden');

        }
    });

    // Function to preview the blog description by converting Markdown to HTML
    function preview(page) {
        // Get the Markdown content from the description field
        const markdownText = page.blog_description;
        document.getElementById("page-title").innerHTML = page.meta_title

        // Make a POST request to convert Markdown to HTML
        fetch('/blog/md-convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ md_text: markdownText })
        })
            .then(response => response.json())  // Parse JSON response
            .then(data => {
                // Set the converted HTML in the preview section
                document.getElementById('page-container').innerHTML = data.html;
                document.getElementById('page-container').classList.remove('hidden');
                // Show the preview column with the converted HTML
                document.getElementById('page-placeholder').classList.add('hidden');
            })
            .catch(error => console.error('Error:', error)); // Log any errors
    }
</script>