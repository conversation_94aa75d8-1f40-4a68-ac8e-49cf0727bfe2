<div id="blogs-container" class="bg-white min-h-screen">
    <div class="p-5 relative">
        <div class=" pb-1 mb-2 border-dashed border-b flex justify-between items-center">
            <h2 class="text-xl font-bold text-neutral-900 dark:text-neutral-100"> Blogs</h2>
            <div class="flex space-x-3">
                <div
                    class="border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 dark:text-neutral-200 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all">
                    <input type="text" id="search-input" placeholder="Search blogs ..."
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full" />
                </div>
                <!-- Select App Field -->
                <div class="form-group text-sm shrink-0 border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-1.5 rounded-md shadow-sm hidden"
                    id="gharpe-options">
                    <select id="gharpe-select"
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                        <option value="" disabled selected>Select Option</option>
                    </select>
                </div>
                <div
                    class="form-group text-sm shrink-0 border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-1.5 rounded-md shadow-sm">
                    <select id="app-select" name="app_name" onchange="updateDbName()"
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                        <option value="" disabled>Select App</option>
                        <option value="kong_ai" selected>Kong.ai</option>
                        <option value="crm_io">CRM.io</option>
                        <option value="fav_ai">Fav.ai</option>
                        <option value="stark_ai">Stark.ai</option>
                        <option value="employers_ai">Employers.ai</option>
                        <option value="finder_io">Finder.io</option>
                        <option value="allrounder_ai">AllRounder.ai</option>
                        <option value="gharpe">Gharpe.com</option>
                        <option value="jobpe">Jobpe.com</option>
                        <option value="ai_bull">ai bull</option>

                    </select>
                    <input type="hidden" id="db-name" name="dbName" value="">
                </div>
                <button
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="showAuthorsPage()">Authors</button>
                <button
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="showForm()">Add</button>
            </div>
        </div>

        <div id="table-container" class="lg:bg-neutral-50 dark:bg-neutral-800 border-neutral-200/70 rounded-md lg:text-wrap text-nowrap
                overflow-auto max-h-[calc(100vh-6rem)] custom-scrollbar">
            <table class="min-w-full text-sm">
                <thead class="bg-gray-100/70 dark:bg-neutral-950 sticky -top-[1px] z-10 backdrop-blur">
                    <tr
                        class="border-b border-neutral-200/70 bg-neutral-100/40 dark:bg-neutral-800/70 dark:border-neutral-700">
                        <th
                            class="px-4 py-1.5 font-normal w-[30%] text-left text-sm  text-gray-600 dark:text-neutral-400">
                            Blog Name</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[20%] text-left text-sm  text-gray-600 dark:text-neutral-400">
                            Author</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[20%] text-left text-sm  text-gray-600 dark:text-neutral-400">
                            App</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[15%] text-left text-sm  text-gray-600 dark:text-neutral-400">
                            Status</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[15%] text-left text-sm  text-gray-600 dark:text-neutral-400">
                            Actions</th>
                    </tr>
                </thead>
                <tbody id="blogs-data-table" class="divide-y divide-gray-200 mobile-card"></tbody>
            </table>
        </div>
        <div id="blogs-placeholder"
            class="border border-dashed flex flex-col h-64 items-center justify-center mt-2 rounded-2xl hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-file-box stroke-neutral-500">
                <path d="M14.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4" />
                <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                <path
                    d="M3 13.1a2 2 0 0 0-1 1.76v3.24a2 2 0 0 0 .97 1.78L6 21.7a2 2 0 0 0 2.03.01L11 19.9a2 2 0 0 0 1-1.76V14.9a2 2 0 0 0-.97-1.78L8 11.3a2 2 0 0 0-2.03-.01Z" />
                <path d="M7 17v5" />
                <path d="M11.7 14.2 7 17l-4.7-2.8" />
            </svg>
            <p class="text-gray-500">No blogs
                available.
                Please add a blog to get started.</p>
        </div>

        {% include 'admin/add-blog.html' %}

    </div>
</div>

<!-- Authors Container (hidden by default) -->
<div id="authors-container" class="hidden">
    {% include 'admin/authors.html' %}
</div>

{% include 'admin/related-blogs.html' %}
<script>

    fetchAllBlogs = []
    let currentBlogId = '';
    // Global variables
    let currentPageIndex = 1; // Tracks the current page being fetched
    let isFetching = false; // Indicates if a fetch request is in progress
    let hasMoreBlogs = true; // Tracks whether there are more blogs to load

    document.addEventListener('DOMContentLoaded', () => {
        document.getElementById('search-input').addEventListener('keypress', function (event) {
            if (event.key === 'Enter') {
                currentPageIndex = 1;  // Reset pagination
                createBlogsList();  // ✅ Call the updated function
            }
        });
        // Load the first page of blogs
        createBlogsList();
        // Set up infinite scroll
        initializeInfiniteScroll();
    });

    document.getElementById('app-select').addEventListener('change', function () {
        const gharpeOptions = document.getElementById('gharpe-options');
        const gharpeSelect = document.getElementById('gharpe-select');
        const searchInput = document.getElementById('search-input');

        // Reset search input when changing app
        searchInput.value = "";

        // Reset pagination state
        currentPageIndex = 1;
        isFetching = false;
        hasMoreBlogs = true;

        // Predefined options for Gharpe.com
        const options = {{ projects | tojson
    }};

    // Check if Gharpe.com is selected
    if (this.value === 'gharpe') {
        gharpeOptions.classList.remove('hidden');

        // Clear previous options and add new ones
        gharpeSelect.innerHTML = '<option value="" disabled selected>Select Option</option>';
        options.forEach(option => {
            const opt = document.createElement('option');
            opt.value = option.name.toLowerCase().split(" ").join("-");
            opt.textContent = option.name;
            gharpeSelect.appendChild(opt);
        });
    } else {
        gharpeOptions.classList.add('hidden');
        gharpeSelect.innerHTML = ''; // Clear options if not Gharpe.com
    }

    // Reset pagination state
    currentPageIndex = 1;
    isFetching = false;
    hasMoreBlogs = true;
    // ✅ Ensure createBlogsList() runs only once
    if (!isFetching) {
        isFetching = true;
        createBlogsList().finally(() => isFetching = false);
    }
        });

    document.getElementById('gharpe-select').addEventListener('change', function () {

        // Reset pagination state
        currentPageIndex = 1;
        isFetching = false;
        hasMoreBlogs = true;

        createBlogsList()
    });

    const saveRelatedBlogsButton = document.getElementById('saveRelatedBlogsButton');


    function updateDbName() {
        const appSelect = document.getElementById("app-select");
        const dbNameInput = document.getElementById("db-name");

        // Update the hidden input with the selected database name
        dbNameInput.value = appSelect.value;
    }

    // Show the loading screen with a message
    function showLoading(message = "Loading...") {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-screen').classList.remove('hidden');
    }

    // Hide the loading screen
    function hideLoading() {
        document.getElementById('loading-screen').classList.add('hidden');
    }
    // Save Selected Blogs
    saveRelatedBlogsButton.addEventListener('click', async () => {
        const appSelect = document.getElementById("app-select");
        const dbNameInput = document.getElementById("db-name");
        saveRelatedBlogs(currentBlogId, appSelect.value)

    });

    async function showRelatedBlogs(currentBlog) {
        relatedBlogsModal.classList.remove('hidden');
        relatedBlogsList.innerHTML = '';

        prefillRelatedBlogs(currentBlog.related_blogs || [], currentBlog.meta_title || '');
        currentBlogId = currentBlog.uuid;

        if (fetchAllBlogs.length === 0) {
            console.warn("No blogs found, fetching again...");
            await createBlogsList(1, false);
        }

        try {
            allBlogs = fetchAllBlogs;
            renderBlogs(allBlogs, currentBlogId); // Pass current blog ID
        } catch (error) {
            console.error('Error fetching blogs:', error);
        }
    }

    function initializeInfiniteScroll() {
        const container = document.getElementById("table-container");

        // Remove existing scroll event listener
        container.removeEventListener("scroll", handleScroll);

        function handleScroll() {
            const scrollPosition = container.scrollTop + container.clientHeight;
            const threshold = container.scrollHeight - 50;

            if (scrollPosition >= threshold && !isFetching && hasMoreBlogs) {
                isFetching = true;
                currentPageIndex++;
                createBlogsList(currentPageIndex, true).finally(() => (isFetching = false));
            }
        }

        container.addEventListener("scroll", handleScroll);
    }

    // Fetch Blogs List to support infinite scroll
    async function createBlogsList(page = 1, append = false) {
        const app = document.getElementById("app-select").value; // Get selected app
        const project = document.getElementById("gharpe-select")?.value || ""; // Get selected project
        const query = document.getElementById('search-input').value.trim(); // Get search query

        let apiUrl = `/blogs?app_name=${app}&page=${page}&limit=50`; // Base API URL

        if (app === "gharpe" && project) {
            apiUrl += `&project_name=${encodeURIComponent(project)}`; // Include project filter if applicable
        }

        if (query.length >= 1) {
            apiUrl += `&query=${encodeURIComponent(query)}`;
        } else {
            document.getElementById("search-input").value = ""; // Ensure input is cleared
        }

        if (!append) {
            // Clear the existing table when not appending
            document.getElementById("blogs-data-table").innerHTML = "";
        }

        showLoading("Fetching blogs...");

        try {
            const response = await fetch(apiUrl);
            const result = await response.json();

            if (result.status === "success") {
                const blogsList = result.data;
                if (append) {
                    fetchAllBlogs = [...fetchAllBlogs, ...blogsList];
                } else {
                    fetchAllBlogs = blogsList;
                }

                if (blogsList.length > 0) {
                    document.getElementById("table-container").classList.remove("hidden")
                    const blogsDataTable = document.getElementById("blogs-data-table");
                    const blogsPlaceholder = document.getElementById("blogs-placeholder");

                    blogsPlaceholder.classList.add("hidden"); // Hide placeholder

                    // Append blogs to the table
                    for (const blog of blogsList) {
                        const row = document.createElement("tr");
                        row.classList.add("border-b", "last:border-0", "dark:bg-neutral-800", "bg-white", "border-gray-200/70", "hover:bg-gray-50", "dark:hover:bg-neutral-800", "even:bg-gray-100/30", "dark:even:bg-neutral-700");

                        const metaTitleTd = document.createElement("td");
                        metaTitleTd.className = "px-4 py-0.5 text-sm border-neutral-200/70 dark:border-neutral-700 border-b first-letter:capitalize";
                        metaTitleTd.textContent = blog.meta_title;
                        row.appendChild(metaTitleTd);

                        // Author column
                        const authorTd = document.createElement("td");
                        authorTd.className = "px-4 py-0.5 text-sm border-neutral-200/70 dark:border-neutral-700 border-b";

                        if (blog.author_name) {
                            const authorDiv = document.createElement("div");
                            authorDiv.className = "flex items-center space-x-2";

                            if (blog.author_image_url) {
                                const authorImg = document.createElement("img");
                                authorImg.src = blog.author_image_url;
                                authorImg.alt = blog.author_name;
                                authorImg.className = "w-6 h-6 rounded-full object-cover";
                                authorImg.onerror = function () {
                                    this.style.display = 'none';
                                    const placeholder = document.createElement("div");
                                    placeholder.className = "w-6 h-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-xs font-medium";
                                    placeholder.textContent = blog.author_name.charAt(0).toUpperCase();
                                    this.parentNode.appendChild(placeholder);
                                };
                                authorDiv.appendChild(authorImg);
                            } else {
                                const placeholder = document.createElement("div");
                                placeholder.className = "w-6 h-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-xs font-medium";
                                placeholder.textContent = blog.author_name.charAt(0).toUpperCase();
                                authorDiv.appendChild(placeholder);
                            }

                            const authorName = document.createElement("span");
                            authorName.className = "text-sm text-gray-900 dark:text-gray-100 truncate max-w-[100px]";
                            authorName.textContent = blog.author_name;
                            authorDiv.appendChild(authorName);

                            authorTd.appendChild(authorDiv);
                        } else {
                            const noAuthor = document.createElement("span");
                            noAuthor.className = "text-sm text-gray-400 dark:text-gray-500 italic";
                            noAuthor.textContent = "No author";
                            authorTd.appendChild(noAuthor);
                        }
                        row.appendChild(authorTd);

                        const appNameTd = document.createElement("td");
                        appNameTd.className = "relative px-4 py-0.5 text-sm border-b border-neutral-200/70 dark:border-neutral-700";
                        appNameTd.textContent = blog.blog_app;
                        row.appendChild(appNameTd);

                        const statusTd = document.createElement("td");
                        statusTd.className = "relative px-4 py-0.5 text-sm border-b border-neutral-200/70 dark:border-neutral-700 first-letter:capitalize";
                        statusTd.textContent = blog.status;
                        row.appendChild(statusTd);

                        // Third td element containing the button
                        const buttonTd = document.createElement("td");
                        buttonTd.className = "relative px-4 py-0.5 text-sm border-b border-neutral-200/70 dark:border-neutral-700";

                        // Container div for the button
                        const buttonDiv = document.createElement("div");
                        buttonDiv.className = "flex space-x-2";

                        // Button element
                        const button = document.createElement("button");
                        button.className = "relative group stroke-neutral-700 dark:stroke-neutral-400 flex items-center space-x-2 border rounded-md p-1.5 transition-all hover:shadow-sm border-neutral-300 dark:border-neutral-600";
                        button.onclick = () => showForm(blog, 'edit');

                        // SVG innerHTML
                        button.innerHTML = `
								<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil">
									<path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
									<path d="m15 5 4 4"/>
								</svg>
							`;

                        const relatedbutton = document.createElement("button");
                        relatedbutton.className = "relative group stroke-neutral-700 dark:stroke-neutral-400 flex items-center space-x-2 border rounded-md p-1.5 transition-all hover:shadow-sm border-neutral-300 dark:border-neutral-600";
                        relatedbutton.onclick = () => showRelatedBlogs(blog);

                        // SVG innerHTML
                        relatedbutton.innerHTML = `
							<svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-pen-line"><path d="m18 5-2.414-2.414A2 2 0 0 0 14.172 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2"/><path d="M21.378 12.626a1 1 0 0 0-3.004-3.004l-4.01 4.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z"/><path d="M8 18h1"/></svg>
                  			`;

                        // Append relatedbutton to div, div to td, and td to row
                        buttonDiv.appendChild(button);
                        buttonDiv.appendChild(relatedbutton);
                        buttonTd.appendChild(buttonDiv);
                        row.appendChild(buttonTd);
                        blogsDataTable.appendChild(row);

                    }
                } else if (append) {
                    console.log("No more blogs to load.");
                    hasMoreBlogs = false; // Stop fetching when no more blogs
                }
                else {
                    const blogsPlaceholder = document.getElementById("blogs-placeholder");
                    blogsPlaceholder.classList.remove("hidden"); // Hide placeholder
                    document.getElementById("table-container").classList.add("hidden")
                }
            }
        } catch (error) {
            console.error("Error fetching blogs:", error);
        } finally {
            hideLoading();
            isFetching = false; // Allow further fetching
        }
    }

    // Add a new keyword input field
    function addKeywordField() {
        const container = document.getElementById('keyword-fields');
        const div = document.createElement('div');
        div.className = 'keyword-field grid grid-cols-4 gap-1';
        div.innerHTML = `
            <input type="text" placeholder="Enter keyword" class="keyword-input col-span-1 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-[#0068ff99] rounded-md bg-white min-h-[42px]  text-[13px] w-full px-2 py-2 mb-2" />
            <input type="url" placeholder="Enter link" class="link-input col-span-1 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-[#0068ff99] rounded-md bg-white min-h-[42px]  text-[13px] w-full px-2 py-2 mb-2" />
            <select class="type-select col-span-1 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-[#0068ff99] rounded-md bg-white min-h-[42px]  text-[13px] w-full px-2 py-2 mb-2">
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
            </select>
            <button onclick="removeKeywordField(this)" class="col-span-1 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-[#0068ff99] rounded-md bg-white min-h-[42px]  w-full px-2 py-2 mb-2 text-slate-700 font-medium text-[14px]">Remove</button>
        `;
        container.appendChild(div); // Add new field to container
    }

    // Remove a keyword input field
    function removeKeywordField(button) {
        button.parentNode.remove(); // Remove parent div
    }


    // Reset the form fields and UI
    function resetForm() {
        document.getElementById('meta_title').value = '';
        document.getElementById('meta_description').value = '';
        document.getElementById('slug').value = '';
        document.getElementById('keywords').value = '';
        document.getElementById('status').value = 'draft';
        document.getElementById('image1_url').value = '';
        document.getElementById('image2_url').value = '';
        document.getElementById('image3_url').value = '';
        document.getElementById('blog_description').value = '';
        document.getElementById('is_featured').checked = false;
        document.getElementById('blog_id').value = '';
        document.getElementById('publish-date-container').classList.add('hidden');


    }

    // Show the form with prefilled data or as a blank form
    function showForm(option, mode = 'add') {
        document.getElementById("blogModal").classList.remove("hidden");
        // If mode is 'add', reset the form
        if (mode === 'add') {
            document.getElementById("blogModalTitle").textContent = "Add Blog"
            document.getElementById("blogSaveButton").textContent = "Save"
            resetForm();
            loadAuthors();
        }
        else {
            document.getElementById("blogModalTitle").textContent = "Edit Blog"
            document.getElementById("blogSaveButton").textContent = "Update"
            // Prefill the form with selected blog's data
            document.getElementById('meta_title').value = option?.meta_title || ''; // Meta title
            document.getElementById('meta_description').value = option?.meta_description || ''; // Meta description
            document.getElementById('slug').value = option?.slug || ''; // Slug
            document.getElementById('keywords').value = option?.keywords || ''; // Keywords
            document.getElementById('status').value = option?.status || 'draft'; // Prefill status dropdown
            document.getElementById('publish_date').value = option?.publish_date || ''; // Prefill publish date
            document.getElementById('image1_url').value = option?.image1_url || ''; // Image 1 URL
            document.getElementById('image2_url').value = option?.image2_url || ''; // Image 2 URL
            document.getElementById('image3_url').value = option?.image3_url || ''; // Image 3 URL
            document.getElementById('blog_description').value = option?.blog_description || ''; // Blog description
            document.getElementById('is_featured').checked = option?.is_featured || false;
            document.getElementById('blog_id').value = option?.uuid || ''; // Blog ID

            // Show or hide publish date field based on status
            const publishDateContainer = document.getElementById('publish-date-container');
            if (option?.status === 'publish') {
                publishDateContainer.classList.remove('hidden');
            } else {
                publishDateContainer.classList.add('hidden');
            }
            loadAuthors(option?.author_uuid);
        }

        // Reset pagination after editing a blog
        currentPageIndex = 1;
        isFetching = false;
        hasMoreBlogs = true;


        preview();
    }

    // Function to show authors page
    function showAuthorsPage() {
        const authorsContainer = document.getElementById('authors-container');
        const blogsContainer = document.getElementById('blogs-container');

        if (authorsContainer && blogsContainer) {
            authorsContainer.classList.remove('hidden');
            blogsContainer.classList.add('hidden');
        }
    }

</script>