<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% if page == "resources" %}Resources{% elif page == "socials" %}Socials{% else %}Blogs{% endif %} - Admin Panel</title>
    <!-- Include Tailwind CSS -->
    {{ get_block("core/css" , { "APP_DEBUG" : APP_DEBUG, "DEPLOYMENT_HASH": DEPLOYMENT_HASH}) }}
    <script src="https://cdn.jsdelivr.net/npm/handlebars@4.7.7/dist/handlebars.min.js"></script>
    <script src="/static/js/crud.js"></script>
    <script src="/static/js/mantra.js"></script>
</head>

<body>
    <!-- Loading overlay -->
    <div id="loading-screen"
        class="fixed inset-0 flex items-center gap-5 items-center justify-center bg-gray-800 bg-opacity-75 z-50 hidden">
        <div class="spinner">
            <div
                class="w-5 h-5 border-4 border-t-4 border-white border-solid rounded-full border-t-transparent animate-spin">
            </div>
        </div>
        <div id="loading-message" class="text-white text-xl"></div>
    </div>

    <!-- Tab Navigation -->
    <div class="bg-white border-b border-gray-200 dark:bg-neutral-900 dark:border-neutral-700">
        <div class="px-5 py-4">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button onclick="switchTab('blogs')"
                    class="tab-button {% if page == 'blogs' %}border-blue-500 text-blue-600 dark:text-blue-400{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300{% endif %} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                    Blogs
                </button>
                <button onclick="switchTab('resources')"
                    class="tab-button {% if page == 'resources' %}border-blue-500 text-blue-600 dark:text-blue-400{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300{% endif %} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                    Resources
                </button>
                <button onclick="switchTab('socials')"
                    class="tab-button {% if page == 'socials' %}border-blue-500 text-blue-600 dark:text-blue-400{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300{% endif %} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200">
                    Socials
                </button>
            </nav>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        {% if page == "resources" %}
        {% include "admin/resources.html" %}
        {% elif page == "socials" %}
        {% include "admin/socials/main.html" %}
        {% else %}
        {% include "admin/blogs.html" %}
        {% endif %}
    </div>

    <!-- Tab Switching JavaScript -->
    <script>
        function switchTab(tabName) {
            // Show loading screen
            const loadingScreen = document.getElementById('loading-screen');
            const loadingMessage = document.getElementById('loading-message');

            loadingMessage.textContent = `Loading ${tabName}...`;
            loadingScreen.classList.remove('hidden');

            // Get current URL and update the page parameter
            const url = new URL(window.location);
            url.searchParams.set('page', tabName);

            // Reload the page with the new parameter
            window.location.href = url.toString();
        }

        // Get current page from URL parameter
        function getCurrentPage() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('page') || 'blogs';
        }

        // Update tab appearance based on current page
        document.addEventListener('DOMContentLoaded', function () {
            const currentPage = getCurrentPage();
            const tabButtons = document.querySelectorAll('.tab-button');

            tabButtons.forEach(button => {
                const tabName = button.getAttribute('onclick').match(/'(\w+)'/)[1];
                if (tabName === currentPage) {
                    button.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'dark:text-gray-400', 'dark:hover:text-gray-300');
                    button.classList.add('border-blue-500', 'text-blue-600', 'dark:text-blue-400');
                } else {
                    button.classList.remove('border-blue-500', 'text-blue-600', 'dark:text-blue-400');
                    button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'dark:text-gray-400', 'dark:hover:text-gray-300');
                }
            });
        });


        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-[60] px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

            // Set colors based on type
            const colors = {
                success: 'bg-green-500 text-white',
                error: 'bg-red-500 text-white',
                warning: 'bg-yellow-500 text-black',
                info: 'bg-blue-500 text-white'
            };

            notification.className += ` ${colors[type] || colors.info}`;
            notification.textContent = message;

            // Add to DOM
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
    </script>
</body>

</html>