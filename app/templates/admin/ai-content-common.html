<script>

    async function generateAIImage(imageFieldId, imageNumber) {
        console.log(`Generating AI image for ${imageFieldId}, image ${imageNumber}...`);

        // Show loading state
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin w-3.5 h-3.5" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        `;

        try {
            // Get form data to create context for image generation
            const titleField = document.getElementById('meta_title') || document.getElementById('resource_meta_title');
            const descriptionField = document.getElementById('meta_description') || document.getElementById('resource_meta_description');

            if (!titleField?.value || !descriptionField?.value) {
                showNotification('Title and Description is requried for generating image', 'warning');
                return;
            }

            // Determine app name based on current form
            const appKey = document.getElementById('resources-app-select')?.value || document.getElementById('app-select')?.value;
            if (!appKey) {
                showNotification('Select a valid app before creating content', 'warning');
                return;
            }

            // Call the AI image generation API
            const response = await fetch('/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: titleField?.value,
                    content_description: descriptionField?.value,
                    app_key: appKey,
                    folder: 'images'
                })
            });

            const result = await response.json();

            if (result.success && result.image_url) {
                // Set the generated image URL in the field
                const imageField = document.getElementById(imageFieldId);
                if (imageField) {
                    imageField.value = result.image_url;
                }
                console.log(`AI image generated for ${imageFieldId}: ${result.image_url}`);
            } else {
                throw new Error(result.error || 'Failed to generate image');
            }

        } catch (error) {
            console.error('Error generating AI image:', error);
            showNotification('Failed to generate AI image. Please try again.', 'error');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalHTML;
        }
    }

    async function loadAIContent(type) {
        console.log(`Loading AI content for ${type}...`);

        // Show loading state
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Generating...</span>
        `;

        try {
            // Gather form data based on content type
            const formData = gatherFormData(type);

            // Validate required fields
            if (!validateFormData(formData, type)) {
                return;
            }

            // Prepare API request payload
            const payload = {
                app_key: formData.appKey,
                primary_keyword: formData.primaryKeyword,
                secondary_keyword: formData.secondaryKeyword || "",
                other_keywords: formData.otherKeywords || "",
                type: type === 'blog' ? 'website_blog' : 'medium_content',
                word_count: 1500
            };

            console.log('Sending AI content request:', payload);

            // Make API call
            const response = await fetch('/generate-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('AI content response:', result);

            if (result.success && result.data) {
                // Populate form fields with AI-generated content
                populateFormFields(result.data, type);

                // Show success message
                showNotification('AI content generated successfully!', 'success');
            } else {
                throw new Error(result.error || 'Failed to generate AI content');
            }

        } catch (error) {
            console.error('Error generating AI content:', error);
            showNotification(`Error generating AI content: ${error.message}`, 'error');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    function gatherFormData(type) {
        const isResource = type === 'resource';
        const prefix = isResource ? 'resource_' : '';

        // Get keywords field - this is the primary source for keywords
        const keywordsElement = document.getElementById(`${prefix}keywords`);
        const keywords = keywordsElement ? keywordsElement.value.trim() : '';

        // Get app key from the appropriate app-select element
        const appSelectId = isResource ? 'resources-app-select' : 'app-select';
        const appKey = document.getElementById(appSelectId)?.value;

        // Split keywords by comma and trim whitespace
        const keywordArray = keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);

        // Assign keywords: first as primary, second as secondary, rest as other
        const primaryKeyword = keywordArray.length > 0 ? keywordArray[0] : '';
        const secondaryKeyword = keywordArray.length > 1 ? keywordArray[1] : '';
        const otherKeywords = keywordArray.length > 2 ? keywordArray.slice(2).join(', ') : '';

        console.log('Keywords processing:', {
            original: keywords,
            split: keywordArray,
            primary: primaryKeyword,
            secondary: secondaryKeyword,
            other: otherKeywords
        });

        return {
            appKey: appKey,
            primaryKeyword: primaryKeyword,
            secondaryKeyword: secondaryKeyword,
            otherKeywords: otherKeywords,
        };
    }

    function validateFormData(formData, type) {
        if (!formData.primaryKeyword) {
            showNotification(`Please enter at least one keyword before generating AI content for ${type}`, 'warning');
            return false;
        }
        if (!formData.appKey) {
            showNotification(`Please select an App ${type}`, 'warning');
            return false;
        }
        return true;
    }

    function populateFormFields(aiData, type) {
        const isResource = type === 'resource';
        const prefix = isResource ? 'resource_' : '';

        try {
            // Populate meta title if empty
            const metaTitleField = document.getElementById(`${prefix}meta_title`);
            if (metaTitleField && !metaTitleField.value && aiData.title) {
                metaTitleField.value = aiData.title;
            }

            // Populate meta description if available
            const metaDescField = document.getElementById(`${prefix}meta_description`);
            if (metaDescField && aiData.meta_description) {
                metaDescField.value = aiData.meta_description;
            }

            // Populate main content
            const contentField = document.getElementById(isResource ? 'resource_description' : 'blog_description');
            if (contentField && aiData.content) {
                contentField.value = aiData.content;

                // Trigger any change events for preview updates
                const changeEvent = new Event('input', { bubbles: true });
                contentField.dispatchEvent(changeEvent);
            }

        } catch (error) {
            console.error('Error populating form fields:', error);
            showNotification('Content generated but there was an error populating some fields', 'warning');
        }
    }
    // AI Info Tooltip functionality
    let currentTooltip = null;

    function createAIInfoTooltip() {
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute right-0 top-8 w-80 bg-blue-200 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-600 rounded-lg shadow-xl p-4 text-sm z-50 before:content-[""] before:absolute before:-top-1.5 before:right-3 before:w-3 before:h-3 before:bg-blue-200 dark:before:bg-neutral-800 before:border-l before:border-t before:border-neutral-200 dark:before:border-neutral-600 before:rotate-45 before:transform';
        tooltip.innerHTML = `
            <h4 class="font-semibold text-neutral-900 dark:text-neutral-100 mb-2">AI Content Generation</h4>
            <div class="space-y-2 text-neutral-700 dark:text-neutral-300">
                <div>
                    <strong>Keywords Required:</strong> Enter keywords separated by commas
                </div>
                <div>
                    <strong>Keyword Processing:</strong>
                    <ul class="ml-4 mt-1 space-y-1">
                        <li>• 1st keyword → Primary keyword</li>
                        <li>• 2nd keyword → Secondary keyword</li>
                        <li>• 3rd+ keywords → Other keywords</li>
                    </ul>
                </div>
                <div>
                    <strong>Fields Updated:</strong>
                    <ul class="ml-4 mt-1 space-y-1">
                        <li>• Meta Title</li>
                        <li>• Meta Description</li>
                        <li>• Page Content</li>
                    </ul>
                </div>
            </div>
        `;

        return tooltip;
    }

    function toggleAIInfoTooltip(button) {
        const container = button.parentElement;

        // If clicking the same button that has an open tooltip, close it
        if (currentTooltip && currentTooltip.parentElement === container) {
            currentTooltip.remove();
            currentTooltip = null;
            return;
        }

        // Remove any existing tooltip
        if (currentTooltip) {
            currentTooltip.remove();
            currentTooltip = null;
        }

        // Create and show new tooltip
        currentTooltip = createAIInfoTooltip();
        container.appendChild(currentTooltip);
    }

    // Close tooltip when clicking outside
    document.addEventListener('DOMContentLoaded', function () {
        document.addEventListener('click', function (event) {
            // If clicking outside any tooltip or info button, close tooltip
            if (!event.target.closest('.ai-info-tooltip') && !event.target.closest('.ai-info-trigger')) {
                if (currentTooltip) {
                    currentTooltip.remove();
                    currentTooltip = null;
                }
            }
        });
    });

    async function humanizeContent(type) {
        console.log(`Humanizing content for ${type}...`);

        // Show loading state
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin w-3.5 h-3.5" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Humanizing...</span>
        `;

        try {
            // Get the content field based on type
            let contentField;
            if (type === 'blog') {
                contentField = document.getElementById('blog_description');
            } else if (type === 'resource') {
                contentField = document.getElementById('resource_description');
            }

            if (!contentField || !contentField.value.trim()) {
                throw new Error(`No content available to humanize for ${type}. Please generate or enter content first.`);
            }

            const content = contentField.value.trim();

            // Check minimum word count (50 words required by Humbot API)
            const wordCount = content.split(/\s+/).length;
            if (wordCount < 50) {
                throw new Error(`Content must be at least 50 words to humanize. Current content has ${wordCount} words.`);
            }

            console.log(`Humanizing ${wordCount} words of content...`);

            // Call the humanization API
            const response = await fetch('/humanize-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: content,
                    model_type: 'Quick'  // You can make this configurable
                })
            });

            const result = await response.json();

            if (result.success && result.humanized_text) {
                // Replace the content with humanized version
                contentField.value = result.humanized_text;

                // Show success notification with detection info
                let message = 'Content humanized successfully!';
                if (result.detection_result && result.detection_score !== null) {
                    message += ` Detection: ${result.detection_result} (${result.detection_score}% human)`;
                }
                if (result.words_used) {
                    message += ` Words used: ${result.words_used}`;
                }

                showNotification(message, 'success');

                // Trigger preview update if it's a blog
                if (type === 'blog' && typeof preview === 'function') {
                    preview();
                }

                console.log(`Content humanized successfully. Task ID: ${result.task_id}`);
            } else {
                throw new Error(result.error || 'Failed to humanize content');
            }

        } catch (error) {
            console.error('Error humanizing content:', error);
            showNotification(`Error humanizing content: ${error.message}`, 'error');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalHTML;
        }
    }
</script>