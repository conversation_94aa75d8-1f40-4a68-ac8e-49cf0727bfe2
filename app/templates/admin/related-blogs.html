<!-- Related Blogs Modal -->
<div id="relatedBlogsModal" data-clarity-unmask="true"
    class="modal fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-neutral-900 rounded-[20px] max-w-[60rem] max-h-[75vh] flex flex-col">
        <!-- Modal Header -->
        <div class="flex justify-between px-6 py-4 border-b border-neutral-200/70 dark:border-neutral-700 items-center">
            <h2 id="relatedBlogsModalTitle" class="font-bold text-md text-neutral-900 dark:text-neutral-100">Related
                Blogs</h2>
            <span id="relatedBlogsClose"
                class="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 cursor-pointer cursor-pointer text-2xl"><svg
                    xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg></span>
        </div>
        <!-- Search Field -->
        <div class="px-6 py-4">
            <input id="searchBlogsInput" type="text" placeholder="Search blogs..."
                class="w-full px-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-neutral-800 text-sm" />
        </div>
        <!-- Modal Content -->
        <div class="overflow-y-auto px-6 py-6 flex-1">
            <ul id="relatedBlogsList" class="space-y-2">
                <!-- Dynamic list of blogs will be appended here -->
            </ul>
        </div>
        <!-- Modal Footer -->
        <div
            class="flex items-center justify-end gap-4 px-6 py-4 border-t border-neutral-200/70 dark:border-neutral-700">
            <button id="saveRelatedBlogsButton"
                class="w-auto min-h-[38px] px-6 py-1 text-white bg-neutral-600 hover:bg-neutral-700 rounded-md">Save</button>
        </div>
    </div>
</div>

<script>
    let selectedBlogs = [];
    const relatedBlogsButton = document.getElementById('relatedBlogsButton');
    const relatedBlogsModal = document.getElementById('relatedBlogsModal');
    const relatedBlogsClose = document.getElementById('relatedBlogsClose');
    const relatedBlogsList = document.getElementById('relatedBlogsList');
    const searchBlogsInput = document.getElementById('searchBlogsInput'); // Use existing input element
    const modalTitle = document.getElementById('relatedBlogsModalTitle'); // Assuming modal title element has id="blogModalTitle"

    let allBlogs = []; // Cache all blogs for filtering
    let currentBlogName = '';
    let currentPage = 1; // Track the current page
    const blogsPerPage = 10; // Number of blogs per page

    // Prefill selected blogs when editing
    function prefillRelatedBlogs(relatedBlogIds, blogName) {
        currentPage = 1
        currentBlogName = blogName;
        selectedBlogs = relatedBlogIds || [];
        if (currentBlogName) {
            modalTitle.textContent = `${currentBlogName} - Related Blogs (${selectedBlogs.length})`;
        } else {
            modalTitle.textContent = `Related Blogs (${selectedBlogs.length})`;
        }
    }

    function computeRelatedCounts(blogs, blogId) {
        const relatedCounts = {};

        // Initialize counts for all blogs
        blogs.forEach(blog => {
            relatedCounts[blog.uuid] = 0;
        });

        // Count occurrences of each blog's UUID in other blogs' related_blogs
        blogs.forEach(blog => {
            // Skip the current blog from contributing to counts
            if (blog.uuid === blogId) return;

            if (Array.isArray(blog.related_blogs)) {
                blog.related_blogs.forEach(relatedId => {
                    if (relatedCounts[relatedId] !== undefined) {
                        relatedCounts[relatedId]++;
                    }
                });
            }
        });

        return relatedCounts;
    }

    async function saveRelatedBlogs(blogId, app) {
        let data = {}
        data.uuid = blogId
        data.related_blogs = selectedBlogs
        const response = await fetch('/blogs', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data: data,
                dbName: app, // Send selected app name
            }),
        });
        relatedBlogsModal.classList.add('hidden');
    }

    document.addEventListener('DOMContentLoaded', () => {

        // Filter Blogs Based on Search Input
        searchBlogsInput.addEventListener('input', (e) => {
            const searchText = e.target.value.trim().toLowerCase();
            const filteredBlogs = allBlogs.filter(blog => blog.meta_title.toLowerCase().includes(searchText));
            if (searchText) {
                renderBlogs(filteredBlogs, currentBlogId, true); // Render filtered blogs
            } else {
                renderBlogs(filteredBlogs, currentBlogId)
            }
        });

        // Hide Related Blogs Modal
        relatedBlogsClose.addEventListener('click', () => {
            searchBlogsInput.value = ''
            relatedBlogsModal.classList.add('hidden');
        });
    });

    function renderBlogs(blogs, blogId, isSearchResults = false) {
		relatedBlogsList.innerHTML = ''; 

		if (blogs.length === 0) {
			relatedBlogsList.innerHTML = `<p class="text-gray-500">No related blogs found.</p>`;
			return;
		}
        const relatedCounts = isSearchResults ? {} : computeRelatedCounts(blogs, blogId); // Compute counts only for related blogs


        if (!isSearchResults) {
            const totalPages = Math.ceil(blogs.length / blogsPerPage);
            const start = (currentPage - 1) * blogsPerPage;
            const end = start + blogsPerPage;
            blogs = blogs.slice(start, end); // Paginate blogs only for related blogs
        }

        blogs.forEach(blog => {
            // Exclude the current blog from the list
            if (blog.uuid === blogId) return;

            const count = relatedCounts[blog.uuid] || 0; // Get count or default to 0
            const isChecked = selectedBlogs.includes(blog.uuid) ? 'checked' : '';

            const listItem = document.createElement('li');
            listItem.className = 'flex items-center space-x-4';

            listItem.innerHTML = `
            <input type="checkbox" id="blog-${blog.uuid}" value="${blog.uuid}" class="accent-current related-blog-checkbox" ${isChecked}>
            <label for="blog-${blog.uuid}" class="text-sm text-neutral-800 dark:text-neutral-200">${blog.meta_title}</label>
            ${isSearchResults ? '' : `<span class="text-sm text-gray-500">${count} blogs</span>`}
        `;

            const checkbox = listItem.querySelector(`#blog-${blog.uuid}`);
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    if (!selectedBlogs.includes(blog.uuid)) {
                        selectedBlogs.push(blog.uuid);
                    }
                } else {
                    selectedBlogs = selectedBlogs.filter(id => id !== blog.uuid);
                }
                updateModalTitle();
            });

            relatedBlogsList.appendChild(listItem);
        });

        // Only render pagination for related blogs
        if (!isSearchResults) {
            renderPaginationControls(Math.ceil(allBlogs.length / blogsPerPage));
        }
    }

    function renderPaginationControls(totalPages) {
        const paginationControls = document.createElement('div');
        paginationControls.className = 'flex justify-between items-center mt-4';

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.disabled = currentPage === 1;
        prevButton.className = prevButton.disabled
            ? 'flex items-center bg-gray-200 dark:bg-gray-600 text-gray-400 px-2 py-2 rounded-md cursor-not-allowed'
            : 'flex items-center bg-black dark:bg-gray-700 text-white px-2 py-2 rounded-md hover:bg-gray-400 dark:hover:bg-gray-800';
        prevButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="lucide lucide-chevron-left w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <polyline points="15 18 9 12 15 6"></polyline>
        </svg>
    `;
        prevButton.addEventListener('click', () => {
            if (!prevButton.disabled) {
                currentPage--;
                renderBlogs(allBlogs, currentBlogId, false);
            }
        });

        // Next button
        const nextButton = document.createElement('button');
        nextButton.disabled = currentPage === totalPages;
        nextButton.className = nextButton.disabled
            ? 'flex items-center bg-gray-200 dark:bg-gray-600 text-gray-400 px-2 py-2 rounded-md cursor-not-allowed'
            : 'flex items-center bg-black dark:bg-gray-700 text-white px-2 py-2 rounded-md hover:bg-gray-400 dark:hover:bg-gray-800';
        nextButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="lucide lucide-chevron-right w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
            <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
    `;
        nextButton.addEventListener('click', () => {
            if (!nextButton.disabled) {
                currentPage++;
                renderBlogs(allBlogs, currentBlogId, false);
            }
        });

        // Page Info
        const pageInfo = document.createElement('span');
        pageInfo.className = 'text-gray-600 dark:text-gray-300';
        pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

        if (totalPages > 1) {
            paginationControls.appendChild(prevButton);
            paginationControls.appendChild(pageInfo);
            paginationControls.appendChild(nextButton);
            relatedBlogsList.appendChild(paginationControls);
        }
    }

    function updateModalTitle() {
        if (currentBlogName) {
            modalTitle.textContent = `${currentBlogName} - Related Blogs (${selectedBlogs.length})`;
        } else {
            modalTitle.textContent = `Related Blogs (${selectedBlogs.length})`;
        }
    }

</script>