<!-- Authors Management Interface -->
<div class="bg-white min-h-screen">
    <div class="p-5 relative">
        <div class="pb-1 mb-2 border-dashed border-b flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <button onclick="location.search.includes('page=resources') ? goBackToResources() : goBackToBlogs()"
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-3 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="mr-2">
                        <path d="m12 19-7-7 7-7" />
                        <path d="M19 12H5" />
                    </svg>
                    Back to Blogs
                </button>
                <h2 class="text-xl font-bold text-neutral-900 dark:text-neutral-100">Authors</h2>
            </div>
            <div class="flex space-x-3">
                <div
                    class="border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 dark:text-neutral-200 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all">
                    <input type="text" id="search-authors-input" placeholder="Search authors ..."
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full" />
                </div>
                <div
                    class="form-group text-sm shrink-0 border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-1.5 rounded-md shadow-sm">
                    <select id="authors-app-select" name="app_name"
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                        <option value="" disabled>Select App</option>
                        <option value="kong_ai" selected>Kong.ai</option>
                        <option value="crm_io">CRM.io</option>
                        <option value="fav_ai">Fav.ai</option>
                        <option value="stark_ai">Stark.ai</option>
                        <option value="employers_ai">Employers.ai</option>
                        <option value="finder_io">Finder.io</option>
                        <option value="allrounder_ai">AllRounder.ai</option>
                        <option value="gharpe">Gharpe.com</option>
                        <option value="jobpe">Jobpe.com</option>
                        <option value="ai_bull">ai bull</option>
                    </select>
                </div>
                <button
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="showAuthorForm()">Add Author</button>
            </div>
        </div>

        <div id="authors-table-container"
            class="lg:bg-neutral-50 dark:bg-neutral-800 border-neutral-200/70 rounded-md lg:text-wrap text-nowrap overflow-auto max-h-[calc(100vh-6rem)] custom-scrollbar">
            <table class="min-w-full text-sm">
                <thead class="bg-gray-100/70 dark:bg-neutral-950 sticky -top-[1px] z-10 backdrop-blur">
                    <tr
                        class="border-b border-neutral-200/70 bg-neutral-100/40 dark:bg-neutral-800/70 dark:border-neutral-700">
                        <th
                            class="px-4 py-1.5 font-normal w-[12%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Image</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[20%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Name</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[25%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Email</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[25%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            LinkedIn</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[18%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Actions</th>
                    </tr>
                </thead>
                <tbody id="authors-data-table" class="divide-y divide-gray-200 mobile-card"></tbody>
            </table>
        </div>

        <!-- Load More Button -->
        <div id="load-more-container" class="flex justify-center mt-4 hidden">
            <button id="load-more-btn" onclick="loadMoreAuthors()"
                class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <span id="load-more-text">Load More Authors</span>
                <span id="load-more-spinner" class="hidden ml-2">
                    <svg class="animate-spin h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                </span>
            </button>
        </div>

        <div id="authors-placeholder"
            class="border border-dashed flex flex-col h-64 items-center justify-center mt-2 rounded-2xl hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-users stroke-neutral-500">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="m22 21-3-3m0 0a5.5 5.5 0 1 0-7.8-7.8 5.5 5.5 0 0 0 7.8 7.8Z" />
            </svg>
            <p class="text-gray-500">No authors available. Please add an author to get started.</p>
        </div>

        <!-- Author Modal -->
        <div id="authorModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div
                class="bg-white dark:bg-neutral-900 rounded-lg shadow-lg w-full max-w-2xl mx-4 max-h-[96vh] overflow-y-auto">

                <!-- Modal Header -->
                <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-neutral-700">
                    <h3 id="authorModalTitle" class="text-lg font-semibold text-gray-900 dark:text-white">Add Author
                    </h3>
                    <button id="authorModalClose" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="p-6">
                    <form id="author-form" onsubmit="return submitAuthorForm(event)">
                        <input type="hidden" id="uuid" name="uuid" value="">

                        <!-- Name Field -->
                        <div class="mb-4">
                            <label for="author_name"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name</label>
                            <input type="text" id="author_name" name="name" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Title Field -->
                        <div class="mb-4">
                            <label for="author_title"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title</label>
                            <input type="text" id="author_title" name="title" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Email Field -->
                        <div class="mb-4">
                            <label for="author_email"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                            <input type="email" id="author_email" name="email" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>

                        <!-- Image Upload Section -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Profile
                                Image</label>

                            <!-- Upload Options Tabs -->
                            <div class="flex mb-3 border-b border-gray-200 dark:border-gray-600">
                                <button type="button" id="url-tab" onclick="switchImageTab('url')"
                                    class="px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 bg-transparent">
                                    Image URL
                                </button>
                                <button type="button" id="upload-tab" onclick="switchImageTab('upload')"
                                    class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 bg-transparent border-b-2 border-transparent">
                                    Upload File
                                </button>
                            </div>

                            <!-- URL Input Tab -->
                            <div id="url-input-section">
                                <input type="url" id="author_image" name="image_url"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                    placeholder="https://example.com/author-image.jpg" oninput="previewAuthorImage()">
                            </div>

                            <!-- File Upload Tab -->
                            <div id="file-upload-section" class="hidden">
                                <div class="flex items-center justify-center w-full">
                                    <label for="author_image_file"
                                        class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                                                aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 20 16">
                                                <path stroke="currentColor" stroke-linecap="round"
                                                    stroke-linejoin="round" stroke-width="2"
                                                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                            </svg>
                                            <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span
                                                    class="font-semibold">Click to upload</span> or drag and drop</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">PNG, JPG or JPEG (MAX.
                                                2MB)</p>
                                        </div>
                                        <input id="author_image_file" type="file" class="hidden" accept="image/*"
                                            onchange="handleImageUpload(event)">
                                    </label>
                                </div>
                            </div>

                            <!-- Image Preview -->
                            <div id="author-image-preview-container" class="mt-3 hidden">
                                <img id="author-preview-img" src="#" alt="Author preview"
                                    class="w-20 h-20 rounded-full object-cover border-2 border-gray-300 dark:border-gray-600">
                            </div>
                        </div>

                        <!-- LinkedIn URL Field -->
                        <div class="mb-6">
                            <label for="author_linkedin"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">LinkedIn
                                URL</label>
                            <input type="url" id="author_linkedin" name="linkedin_url"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter LinkedIn Profile URL">
                        </div>

                        <!-- Facebook URL Field -->
                        <div class="mb-6">
                            <label for="author_facebook"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Facebook
                                URL</label>
                            <input type="url" id="author_facebook" name="facebook_url"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter Facebook Profile URL">
                        </div>

                        <!-- X URL Field -->
                        <div class="mb-6">
                            <label for="author_x"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">X
                                URL</label>
                            <input type="url" id="author_x" name="x_url"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter X Profile URL">
                        </div>

                        <!-- Bio Field -->
                        <div class="mb-4">
                            <label for="author_bio"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio</label>
                            <textarea id="author_bio" name="bio" rows="6"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Brief author biography..."></textarea>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" id="authorCancelButton" onclick="closeAuthorModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                Cancel
                            </button>
                            <button type="submit" id="authorSaveButton"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let currentAuthorPageIndex = 1;
    let isFetchingAuthors = false;
    let hasMoreAuthors = true;
    let fetchAllAuthors = [];
    const authorsLimitPerPage = 50;

    // Function to go back to blogs
    function goBackToBlogs() {
        document.getElementById('authors-container').classList.add('hidden');
        document.getElementById('blogs-container').classList.remove('hidden');
    }

    // Initialize authors page
    document.addEventListener('DOMContentLoaded', () => {
        if (document.getElementById('authors-container')) {
            // Set up search functionality
            document.getElementById('search-authors-input')?.addEventListener('keypress', function (event) {
                if (event.key === 'Enter') {
                    currentAuthorPageIndex = 1;
                    isFetchingAuthors = false;
                    hasMoreAuthors = true;
                    createAuthorsList();
                }
            });

            // Set up app selection change
            document.getElementById('authors-app-select')?.addEventListener('change', function () {
                currentAuthorPageIndex = 1;
                isFetchingAuthors = false;
                hasMoreAuthors = true;
                createAuthorsList();
            });

            // Load initial authors list
            createAuthorsList();
        }
    });

    // Show loading screen
    function showAuthorsLoading(message = "Loading...") {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-screen').classList.remove('hidden');
    }

    // Hide loading screen
    function hideAuthorsLoading() {
        document.getElementById('loading-screen').classList.add('hidden');
    }

    // Show author form
    function showAuthorForm(author = null, mode = 'add') {
        document.getElementById("authorModal").classList.remove("hidden");
        console.log("author, mode", author, mode)
        resetAuthorForm();


        if (mode === 'add') {
            document.getElementById("authorModalTitle").textContent = "Add Author";
            document.getElementById("authorSaveButton").textContent = "Save";
        } else {
            document.getElementById("authorModalTitle").textContent = "Edit Author";
            document.getElementById("authorSaveButton").textContent = "Update";
            // Prefill form with author data
            document.getElementById('author_name').value = author?.name || '';
            document.getElementById('author_title').value = author?.title || '';
            document.getElementById('author_email').value = author?.email || '';
            document.getElementById('author_image').value = author?.image_url || '';
            document.getElementById('author_bio').value = author?.bio || '';
            document.getElementById('author_linkedin').value = author?.linkedin_url || '';
            document.getElementById('author_facebook').value = author?.facebook_url || '';
            document.getElementById('author_x').value = author?.x_url || '';
            document.getElementById('uuid').value = author?.uuid || '';
            // Trigger image preview if image URL exists
            if (author?.image_url) {
                previewAuthorImage();
            }
        }
    }

    // Close author modal
    function closeAuthorModal() {
        document.getElementById("authorModal").classList.add("hidden");
    }

    // Reset author form
    function resetAuthorForm() {
        document.getElementById('author_name').value = '';
        document.getElementById('author_title').value = '';
        document.getElementById('author_email').value = '';
        document.getElementById('author_image').value = '';
        document.getElementById('author_bio').value = '';
        document.getElementById('author_linkedin').value = '';
        document.getElementById('author_facebook').value = '';
        document.getElementById('author_x').value = '';
        document.getElementById('uuid').value = '';
        document.getElementById('author_image_file').value = '';
        // Hide image preview
        document.getElementById('author-preview-img').src = '';
        // Reset to URL tab
        switchImageTab('url');
    }

    // Preview author image
    function previewAuthorImage() {
        const imageUrl = document.getElementById('author_image').value;
        const preview = document.getElementById('author-image-preview-container');
        const previewImg = document.getElementById('author-preview-img');

        if (imageUrl && imageUrl.trim() !== '') {
            previewImg.src = imageUrl;
            previewImg.onerror = function () {
                preview.classList.add('hidden');
            };
            previewImg.onload = function () {
                preview.classList.remove('hidden');
            };
        } else {
            previewImg.src = "";
            preview.classList.add('hidden');
        }
    }

    // Switch between image upload tabs
    function switchImageTab(tab) {
        const urlTab = document.getElementById('url-tab');
        const uploadTab = document.getElementById('upload-tab');
        const urlSection = document.getElementById('url-input-section');
        const uploadSection = document.getElementById('file-upload-section');

        if (tab === 'url') {
            // Activate URL tab
            urlTab.className = "px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 bg-transparent";
            uploadTab.className = "px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 bg-transparent border-b-2 border-transparent";
            urlSection.classList.remove('hidden');
            uploadSection.classList.add('hidden');
        } else {
            // Activate Upload tab
            uploadTab.className = "px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 bg-transparent";
            urlTab.className = "px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 bg-transparent border-b-2 border-transparent";
            uploadSection.classList.remove('hidden');
            urlSection.classList.add('hidden');
        }
    }

    // Handle file upload
    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            // Check file size (2MB limit)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                return;
            }

            // Check file type
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file');
                return;
            }

            // Create a FileReader to read the file
            const reader = new FileReader();
            reader.onload = function (e) {
                const preview = document.getElementById('author-image-preview-container');
                const previewImg = document.getElementById('author-preview-img');

                previewImg.src = e.target.result;
                preview.classList.remove('hidden');

                // Set the image URL field to the data URL for now
                // In a real implementation, you would upload this to a server
                document.getElementById('author_image').value = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    // Load more authors function
    async function loadMoreAuthors() {
        if (isFetchingAuthors || !hasMoreAuthors) {
            return;
        }

        const loadMoreBtn = document.getElementById('load-more-btn');
        const loadMoreText = document.getElementById('load-more-text');
        const loadMoreSpinner = document.getElementById('load-more-spinner');

        // Show loading state
        loadMoreBtn.disabled = true;
        loadMoreText.textContent = 'Loading...';
        loadMoreSpinner.classList.remove('hidden');

        try {
            currentAuthorPageIndex++;
            await createAuthorsList(currentAuthorPageIndex, true);
        } catch (error) {
            console.error('Error loading more authors:', error);
            currentAuthorPageIndex--; // Revert page index on error
        } finally {
            // Reset button state
            loadMoreBtn.disabled = false;
            loadMoreText.textContent = 'Load More Authors';
            loadMoreSpinner.classList.add('hidden');
        }
    }

    // Modal close event
    document.getElementById("authorModalClose")?.addEventListener("click", closeAuthorModal);

    // Fetch and display authors list
    async function createAuthorsList(page = 1, append = false) {
        const app = document.getElementById("authors-app-select").value;
        const query = document.getElementById('search-authors-input').value.trim();

        let apiUrl = `/authors?app_name=${app}&page=${page}&limit=${authorsLimitPerPage}`;

        if (query.length >= 1) {
            apiUrl += `&query=${encodeURIComponent(query)}`;
        }

        if (!append) {
            document.getElementById("authors-data-table").innerHTML = "";
        }

        showAuthorsLoading("Fetching authors...");

        try {
            const response = await fetch(apiUrl);
            const result = await response.json();

            if (result.status === "success") {
                const authorsList = result.data;
                if (append) {
                    fetchAllAuthors = [...fetchAllAuthors, ...authorsList];
                } else {
                    fetchAllAuthors = authorsList;
                }

                if (authorsList.length > 0) {
                    document.getElementById("authors-table-container").classList.remove("hidden");
                    const authorsDataTable = document.getElementById("authors-data-table");
                    const authorsPlaceholder = document.getElementById("authors-placeholder");
                    const loadMoreContainer = document.getElementById("load-more-container");

                    authorsPlaceholder.classList.add("hidden");

                    // Show load more button if we have more authors (assuming 50 is the limit per page)
                    if (authorsList.length === authorsLimitPerPage) {
                        loadMoreContainer.classList.remove("hidden");
                    } else {
                        loadMoreContainer.classList.add("hidden");
                        hasMoreAuthors = false;
                    }

                    // Append authors to the table
                    for (const author of authorsList) {
                        const row = document.createElement("tr");
                        row.classList.add("border-b", "last:border-0", "dark:bg-neutral-800", "bg-white", "border-gray-200/70", "hover:bg-gray-50", "dark:hover:bg-neutral-800", "even:bg-gray-100/30", "dark:even:bg-neutral-700");

                        // Image column
                        const imageTd = document.createElement("td");
                        imageTd.className = "px-4 py-0.5 text-sm border-neutral-200/70 dark:border-neutral-700 border-b";
                        if (author.image_url) {
                            const img = document.createElement("img");
                            img.src = author.image_url;
                            img.alt = author.name;
                            img.className = "w-10 h-10 rounded-full object-cover";
                            img.onerror = function () {
                                this.style.display = 'none';
                                const placeholder = document.createElement("div");
                                placeholder.className = "w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-sm font-medium";
                                placeholder.textContent = author.name.charAt(0).toUpperCase();
                                this.parentNode.appendChild(placeholder);
                            };
                            imageTd.appendChild(img);
                        } else {
                            const placeholder = document.createElement("div");
                            placeholder.className = "w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-sm font-medium";
                            placeholder.textContent = author.name.charAt(0).toUpperCase();
                            imageTd.appendChild(placeholder);
                        }
                        row.appendChild(imageTd);

                        // Name column
                        const nameTd = document.createElement("td");
                        nameTd.className = "px-4 py-0.5 text-sm border-neutral-200/70 dark:border-neutral-700 border-b first-letter:capitalize";
                        nameTd.textContent = author.name;
                        row.appendChild(nameTd);

                        // Email column
                        const emailTd = document.createElement("td");
                        emailTd.className = "relative px-4 py-0.5 text-sm border-b border-neutral-200/70 dark:border-neutral-700";
                        emailTd.textContent = author.email;
                        row.appendChild(emailTd);

                        // LinkedIn column
                        const linkedinTd = document.createElement("td");
                        linkedinTd.className = "relative px-4 py-0.5 text-sm border-b border-neutral-200/70 dark:border-neutral-700";
                        if (author.linkedin_url) {
                            const linkedinLink = document.createElement("a");
                            linkedinLink.href = author.linkedin_url;
                            linkedinLink.target = "_blank";
                            linkedinLink.rel = "noopener noreferrer";
                            linkedinLink.className = "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1";
                            linkedinLink.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="text-blue-600">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                <span class="text-xs">LinkedIn</span>
                            `;
                            linkedinTd.appendChild(linkedinLink);
                        } else {
                            linkedinTd.innerHTML = '<span class="text-gray-400 text-xs">-</span>';
                        }
                        row.appendChild(linkedinTd);

                        // Actions column
                        const buttonTd = document.createElement("td");
                        buttonTd.className = "relative px-4 py-0.5 text-sm border-b border-neutral-200/70 dark:border-neutral-700";

                        const buttonDiv = document.createElement("div");
                        buttonDiv.className = "flex space-x-2";

                        // Edit button
                        const editButton = document.createElement("button");
                        editButton.className = "relative group stroke-neutral-700 dark:stroke-neutral-400 flex items-center space-x-2 border rounded-md p-1.5 transition-all hover:shadow-sm border-neutral-300 dark:border-neutral-600";
                        editButton.onclick = () => showAuthorForm(author, 'edit');
                        editButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil">
                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                                <path d="m15 5 4 4"/>
                            </svg>
                        `;

                        // Delete button
                        const deleteButton = document.createElement("button");
                        deleteButton.className = "relative group stroke-red-600 dark:stroke-red-400 flex items-center space-x-2 border rounded-md p-1.5 transition-all hover:shadow-sm border-red-300 dark:border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20";
                        deleteButton.onclick = () => deleteAuthor(author.uuid);
                        deleteButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-2">
                                <path d="M3 6h18"/>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                                <line x1="10" x2="10" y1="11" y2="17"/>
                                <line x1="14" x2="14" y1="11" y2="17"/>
                            </svg>
                        `;

                        buttonDiv.appendChild(editButton);
                        buttonDiv.appendChild(deleteButton);
                        buttonTd.appendChild(buttonDiv);
                        row.appendChild(buttonTd);
                        authorsDataTable.appendChild(row);
                    }
                } else if (append) {
                    console.log("No more authors to load.");
                    hasMoreAuthors = false;
                    document.getElementById("load-more-container").classList.add("hidden");
                } else {
                    const authorsPlaceholder = document.getElementById("authors-placeholder");
                    const loadMoreContainer = document.getElementById("load-more-container");
                    authorsPlaceholder.classList.remove("hidden");
                    document.getElementById("authors-table-container").classList.add("hidden");
                    loadMoreContainer.classList.add("hidden");
                }
            }
        } catch (error) {
            console.error("Error fetching authors:", error);
        } finally {
            hideAuthorsLoading();
            isFetchingAuthors = false;
        }
    }

    // Submit author form
    async function submitAuthorForm(event) {
        event.preventDefault();

        const formData = new FormData(document.getElementById("author-form"));
        const data = Object.fromEntries(formData);

        const app = document.getElementById("authors-app-select").value;
        data.app_name = app;

        const authorUUID = document.getElementById("uuid").value;
        showAuthorsLoading(authorUUID.length ? 'Updating author...' : 'Saving author...');

        try {
            let response;
            if (authorUUID) {
                data.uuid = authorUUID;
                response = await fetch('/authors', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: data,
                        dbName: app,
                    }),
                });
            } else {
                response = await fetch('/authors', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: data,
                        dbName: app,
                    }),
                });
            }

            const result = await response.json();
            if (result.status === "success") {
                // Reset pagination and reload authors list
                currentAuthorPageIndex = 1;
                isFetchingAuthors = false;
                hasMoreAuthors = true;
                createAuthorsList();
                closeAuthorModal();
            } else {
                alert("Error saving author. Please try again.");
            }
        } catch (error) {
            console.error("Error saving author:", error);
            alert("Error saving author. Please try again.");
        } finally {
            hideAuthorsLoading();
        }
    }

    // Delete author
    async function deleteAuthor(authorUUID) {
        if (!confirm("Are you sure you want to delete this author?")) {
            return;
        }

        const app = document.getElementById("authors-app-select").value;
        showAuthorsLoading('Deleting author...');

        try {
            const response = await fetch(`/authors/${authorUUID}?app_name=${app}`, {
                method: 'DELETE',
            });

            const result = await response.json();
            if (result.status === "success") {
                // Reset pagination and reload authors list
                currentAuthorPageIndex = 1;
                isFetchingAuthors = false;
                hasMoreAuthors = true;
                createAuthorsList();
            } else {
                alert("Error deleting author. Please try again.");
            }
        } catch (error) {
            console.error("Error deleting author:", error);
            alert("Error deleting author. Please try again.");
        } finally {
            hideAuthorsLoading();
        }
    }


    async function populateAuthors(containerId, appName, isDropdown = false, selectedAuthorUUID = null) {
        if (!containerId || !appName) {
            console.error('Missing required parameters for populateAuthors');
            return;
        }

        let selectElement, loadingDiv, noAuthorsDiv, authorOptions, authorLoading, authorEmpty;

        if (isDropdown) {
            // For custom dropdown
            authorOptions = document.getElementById(`${containerId}_options`);
            authorLoading = document.getElementById(`${containerId}_loading`);
            authorEmpty = document.getElementById(`${containerId}_empty`);
            selectElement = document.getElementById(containerId); // Hidden input for dropdown
        } else {
            // For traditional select
            selectElement = document.getElementById(containerId);
            loadingDiv = document.getElementById('loading-authors');
            noAuthorsDiv = document.getElementById('no-authors');
        }

        if (!selectElement || (!isDropdown && (!loadingDiv || !noAuthorsDiv)) || (isDropdown && (!authorOptions || !authorLoading || !authorEmpty))) {
            console.error('Required elements for populateAuthors not found');
            return;
        }

        try {
            const response = await fetch(`/authors?app_name=${appName}`);
            const data = await response.json();

            if (isDropdown) {
                authorOptions.innerHTML = '';
                authorLoading.classList.remove('hidden');
                authorEmpty.classList.add('hidden');
            } else {
                selectElement.innerHTML = '<option value="">Select Author</option>';
            }

            if (data.status === 'success' && data.data.length > 0) {
                if (isDropdown) {
                    data.data.forEach(author => {
                        const option = document.createElement('div');
                        option.className = 'p-3 hover:bg-gray-100 dark:hover:bg-neutral-700 cursor-pointer border-b border-gray-100 dark:border-neutral-700 last:border-b-0 flex items-center space-x-3';
                        option.onclick = () => selectResourceAuthor(author, containerId);

                        let imageHtml = '';
                        if (author.image_url) {
                            imageHtml = `
                                <img src="${author.image_url}" alt="${author.name}" class="w-8 h-8 rounded-full object-cover"
                                     onerror="this.style.display='none'; this.nextElementSibling.classList.remove('hidden');">
                                <div class="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-sm font-medium hidden">
                                    ${author.name.charAt(0).toUpperCase()}
                                </div>
                            `;
                        } else {
                            imageHtml = `
                                <div class="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-sm font-medium">
                                    ${author.name.charAt(0).toUpperCase()}
                                </div>
                            `;
                        }

                        option.innerHTML = `
                            ${imageHtml}
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${author.name}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">${author.email}</div>
                            </div>
                        `;

                        authorOptions.appendChild(option);

                        if (selectedAuthorUUID && selectedAuthorUUID === author.uuid) {
                            selectResourceAuthor(author, containerId);
                        }
                    });
                    authorLoading.classList.add('hidden');
                    authorEmpty.classList.add('hidden');
                } else {
                    data.data.forEach(author => {
                        const option = document.createElement('option');
                        option.value = author.uuid;
                        option.textContent = author.name;
                        selectElement.appendChild(option);
                    });
                    selectElement.style.display = 'block';
                    loadingDiv.style.display = 'none';
                    noAuthorsDiv.style.display = 'none';
                }
            } else {
                if (isDropdown) {
                    authorLoading.classList.add('hidden');
                    authorEmpty.classList.remove('hidden');
                } else {
                    selectElement.style.display = 'none';
                    loadingDiv.style.display = 'none';
                    noAuthorsDiv.style.display = 'block';
                }
            }
        } catch (error) {
            console.error('Error fetching authors:', error);
            if (isDropdown) {
                authorLoading.classList.add('hidden');
                authorEmpty.classList.remove('hidden');
                authorEmpty.textContent = 'Error loading authors - 2';
            } else {
                selectElement.style.display = 'none';
                loadingDiv.style.display = 'none';
                noAuthorsDiv.style.display = 'block';
            }
        }
    }

    function showAuthorsModal() {
        const authorsContainer = document.getElementById('authors-container');
        const blogsContainer = document.getElementById('blogs-container');
        const resourcesContainer = document.getElementById('resources-container');

        if (authorsContainer) {
            authorsContainer.classList.remove('hidden');
            if (blogsContainer) blogsContainer.classList.add('hidden');
            if (resourcesContainer) resourcesContainer.classList.add('hidden');
        }
    }

    function goBackToResources() {
        const authorsContainer = document.getElementById('authors-container');
        const resourcesContainer = document.getElementById('resources-container');

        if (authorsContainer && resourcesContainer) {
            authorsContainer.classList.add('hidden');
            resourcesContainer.classList.remove('hidden');
        }
    }
</script>