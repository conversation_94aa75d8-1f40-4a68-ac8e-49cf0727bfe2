<div>
    <div id="resourceModal" data-clarity-unmask="true"
        class="modal fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[50] hidden">
        <div class="bg-white alert-modal dark:bg-neutral-900 rounded-[20px] w-[90vw] max-h-[85vh] flex flex-col">

            <!-- Mo<PERSON> Header (Fixed at top) -->
            <div
                class="flex justify-between px-6 py-4 border-b border-neutral-200/70 dark:border-neutral-700 items-center">
                <h2 id="resourceModalTitle" class="font-bold text-md text-neutral-900 dark:text-neutral-100">
                    Add Pillar Page
                </h2>
                <span id="resourceModalClose"
                    class="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 cursor-pointer cursor-pointer text-2xl">
                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </span>
            </div>

            <!-- Modal Body (Scrollable content) -->
            <div class="px-6 py-6 overflow-auto lg:overflow-hidden">
                <!-- Hidden fields -->
                <input type="hidden" id="resource_id" name="resource_id" value="">
                <input type="hidden" id="resource_type" name="resource_type" value="pillar">
                <input type="hidden" id="pillar_id" name="pillar_id" value="">

                <form id="resource-form" class="flex flex-wrap lg:flex-nowrap gap-5 h-full"
                    onsubmit="return submitResourceForm(event)">
                    <!-- Column 1: Form Fields -->
                    <div
                        class="w-full lg:w-1/3 p-2 flex flex-col gap-4 h-[calc(100vh-317px)] overflow-auto custom-scrollbar">

                        <!-- Meta Title -->
                        <div class="form-group flex-1">
                            <label for="resource_meta_title"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Meta
                                Title</label>
                            <textarea name="meta_title" id="resource_meta_title" rows="1" placeholder="Enter meta title"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required></textarea>
                        </div>

                        <!-- Meta Description -->
                        <div class="form-group flex-1">
                            <label for="resource_meta_description"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Meta
                                Description</label>
                            <textarea name="meta_description" id="resource_meta_description" rows="3"
                                placeholder="Enter meta description"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required></textarea>
                        </div>

                        <!-- Pillar Page Selection (only for subpages) -->
                        <div class="form-group flex-1 hidden" id="pillar-selection-field">
                            <label for="resource_pillar_select"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Pillar
                                Page</label>
                            <div class="relative">
                                <div id="resource_pillar_dropdown_trigger"
                                    class="text-sm border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 hover:border-neutral-400 focus:border-neutral-400 focus:outline-0 px-3 py-2.5 rounded-md shadow-sm flex items-center justify-between"
                                    onclick="togglePillarDropdown()">
                                    <div id="selected_pillar_display" class="flex items-center space-x-2">
                                        <span id="selected_pillar_text"
                                            class="text-neutral-700 dark:text-neutral-300">Select Pillar Page</span>
                                    </div>
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>

                                <!-- Custom Dropdown -->
                                <div id="pillar_dropdown_list"
                                    class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-neutral-800 border border-gray-300 dark:border-neutral-600 rounded-md shadow-lg max-h-60 overflow-auto">
                                    <div id="pillar_options">
                                        <!-- Pillar pages will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Author Selection - SIMPLIFIED -->
                        <div class="form-group flex-1">
                            <label for="author_uuid"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Author</label>
                            <select id="author_uuid" name="author_uuid"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800">
                                <option value="">Select Author</option>
                            </select>
                        </div>

                        <!-- URL Field -->
                        <div class="form-group flex-1">
                            <label for="resource_slug"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">URL</label>
                            <input name="slug" id="resource_slug" type="text"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required placeholder="Enter resource URL (e.g., valid-url-slug)" />
                        </div>

                        <!-- Image URLs -->
                        <div class="form-group flex-1">
                            <label class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Image
                                URLs</label>
                            <div class="flex items-center mt-2">
                                <input id="resource_image1_url" type="url"
                                    class="flex-1 border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                    name="image1_url" required placeholder="Image 1 URL" />
                                <button type="button" 
                                        onclick="generateAIImage('resource_image1_url', 1)"
                                        class="ml-2 px-3 py-2 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md transition-colors duration-200 flex items-center gap-1.5 whitespace-nowrap">
                                    <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000" alt="AI" class="w-4 h-4 opacity-70">
                                </button>
                            </div>
                            <div class="flex items-center mt-2">
                                <input id="resource_image2_url" type="url"
                                    class="flex-1 border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                    name="image2_url" placeholder="Image 2 URL" />
                                <button type="button" 
                                        onclick="generateAIImage('resource_image2_url', 2)"
                                        class="ml-2 px-3 py-2 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md transition-colors duration-200 flex items-center gap-1.5 whitespace-nowrap">
                                    <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000" alt="AI" class="w-4 h-4 opacity-70">
                                </button>
                            </div>
                            <div class="flex items-center mt-2">
                                <input id="resource_image3_url" type="url"
                                    class="flex-1 border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                    name="image3_url" placeholder="Image 3 URL" />
                                <button type="button" 
                                        onclick="generateAIImage('resource_image3_url', 3)"
                                        class="ml-2 px-3 py-2 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md transition-colors duration-200 flex items-center gap-1.5 whitespace-nowrap">
                                    <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000" alt="AI" class="w-4 h-4 opacity-70">
                                </button>
                            </div>
                        </div>

                        <!-- Keywords Field -->
                        <div class="form-group flex-1">
                            <label for="resource_keywords"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Keywords</label>
                            <input name="keywords" id="resource_keywords" type="text"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                placeholder="Enter keywords separated by commas (e.g., primary, secondary, other)" />
                        </div>

                    </div>

                    <!-- Column 2: Resource Description -->
                    <div class="w-full lg:w-1/3 flex flex-col gap-4 h-full">
                        <!-- Page Content Field -->
                        <div class="form-group flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <label for="resource_description"
                                    class="block text-sm font-medium text-neutral-800 dark:text-neutral-200">Page
                                    Content</label>
                                <div class="flex items-center gap-2">
                                    <button type="button" onclick="loadAIContent('resource')"
                                        class="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 border border-neutral-300 dark:border-neutral-600 hover:border-neutral-400 dark:hover:border-neutral-500 rounded-md transition-colors"
                                        title="Load AI Content">
                                        <img src="https://img.icons8.com/?size=100&id=MTnnE7FNiELB&format=png&color=000000"
                                            alt="AI" class="w-4 h-4 opacity-70">
                                        <span>AI Content</span>
                                    </button>
                                    <button type="button" onclick="humanizeContent('resource')"
                                        class="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 border border-neutral-300 dark:border-neutral-600 hover:border-neutral-400 dark:hover:border-neutral-500 rounded-md transition-colors"
                                        title="Humanize Content">
                                        <svg class="w-4 h-4 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <span>Humanize</span>
                                    </button>
                                    <div class="relative">
                                        <button type="button"
                                            class="ai-info-trigger p-1 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 transition-colors rounded hover:bg-neutral-100 dark:hover:bg-neutral-700"
                                            title="AI Content Information" onclick="toggleAIInfoTooltip(this)">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9,9h0a3,3,0,0,1,6,0c0,2-3,3-3,3"></path>
                                                <path d="M12,17h.01"></path>
                                            </svg>
                                        </button>

                                    </div>
                                </div>
                            </div>
                            <textarea id="resource_description" name="resource_description"
                                class="text-sm w-full h-[calc(100vh-350px)] custom-scrollbar border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required></textarea>
                        </div>
                    </div>

                    <!-- Column 3: Preview Container -->
                    <div class="w-full lg:w-1/3 flex flex-col gap-4">
                        <!-- Flex Container for Publish and Publish Date -->
                        <div class="flex gap-4">
                            <!-- Status Field -->
                            <div class="form-group flex-1">
                                <label for="resource_status"
                                    class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Status</label>
                                <div
                                    class="text-sm border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-2.5 rounded-md shadow-sm">
                                    <select id="resource_status" name="status"
                                        class="w-full bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm"
                                        required>
                                        <option value="draft" selected>Draft</option>
                                        <option value="publish">Publish</option>
                                    </select>
                                </div>
                            </div>
                            <!-- Publish Date Field -->
                            <div id="resource-publish-date-container" class="form-group flex-1 hidden">
                                <label for="resource_publish_date"
                                    class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Publish
                                    Date & Time</label>
                                <input type="datetime-local" id="resource_publish_date" name="publish_date"
                                    class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800" />
                            </div>
                        </div>

                        <!-- Markdown Preview Section -->
                        <div id="resource-preview-column" class="bg-white rounded-md flex-1">
                            <label id="resource-heading"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Preview
                            </label>
                            <div id="resource-preview-html"
                                class="text-sm border border-neutral-300 dark:border-neutral-600 p-2 rounded shadow-md mt-2 text-sm text-gray-500 overflow-auto pr-1 h-[calc(100vh-438px)] custom-scrollbar">
                                <!-- Preview content will be displayed here -->
                            </div>
                        </div>
                    </div>
                </form>

            </div>

            <!-- Modal Footer (Fixed at bottom) -->
            <div
                class="flex items-center justify-end gap-4 px-6 py-4 border-t border-neutral-200/70 dark:border-neutral-700">
                <button type="submit" id="resourceSaveButton" form="resource-form"
                    class="w-auto min-h-[38px] px-6 py-1 flex items-center justify-center text-white text-[14px] font-medium bg-slate-900 rounded-md">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        let typingTimer; // Timer identifier
        const typingInterval = 1000; // Interval in milliseconds (1 second)
        const inputField = document.getElementById("resource_description"); // Input field

        inputField.addEventListener("input", () => {
            clearTimeout(typingTimer); // Clear the previous timer
            typingTimer = setTimeout(() => {
                previewResource()
            }, typingInterval);
        });

        const statusField = document.getElementById('resource_status');
        const publishDateContainer = document.getElementById('resource-publish-date-container');
        const publishDateInput = document.getElementById('resource_publish_date');

        // Listen for changes in the status dropdown
        statusField.addEventListener('change', () => {
            if (statusField.value === 'publish') {
                publishDateContainer.classList.remove('hidden'); // Show the date field
            } else {
                publishDateContainer.classList.add('hidden'); // Hide the date field
                publishDateInput.value = ''; // Clear the date field
            }
        });

        // Load authors when page loads
        loadResourceAuthors();
    });

    document.getElementById("resourceModalClose").addEventListener("click", () => {
        document.getElementById("resourceModal").classList.add("hidden");
    });

    // SIMPLIFIED AUTHOR LOADING FUNCTION
    async function loadResourceAuthors(selectedAuthorId = null) {
        const authorSelect = document.getElementById('author_uuid');

        // Show loading option
        authorSelect.innerHTML = '<option value="">Loading authors...</option>';

        try {
            const app = document.getElementById("resources-app-select")?.value || 'kong_ai';
            const response = await fetch(`/authors?app_name=${app}&limit=50`);
            const result = await response.json();

            // Clear loading and add default option
            authorSelect.innerHTML = '<option value="">Select Author</option>';

            if (result.status === "success" && result.data.length > 0) {
                result.data.forEach(author => {
                    const option = document.createElement('option');
                    option.value = author.uuid;
                    option.textContent = `${author.name} (${author.email})`;

                    // Set selected if this matches the current author
                    if (selectedAuthorId && selectedAuthorId === author.uuid) {
                        option.selected = true;
                    }

                    authorSelect.appendChild(option);
                });
            } else {
                authorSelect.innerHTML = '<option value="">No authors available</option>';
            }
        } catch (error) {
            console.error('Error loading authors:', error);
            authorSelect.innerHTML = '<option value="">Error loading authors</option>';
        }
    }

    function openNewResourceModal() {
        document.getElementById('resource-form').reset();
        document.getElementById('resource_id').value = '';
        document.getElementById('resourceModalTitle').textContent = 'Add New Resource';

        // Load authors
        loadResourceAuthors();

        // Show modal
        document.getElementById("resourceModal").classList.remove("hidden");
    }

    function openEditResourceModal(resourceData) {
        // Populate form fields
        document.getElementById('resource_id').value = resourceData.uuid || '';
        document.getElementById('resource_meta_title').value = resourceData.meta_title || '';
        document.getElementById('resource_meta_description').value = resourceData.meta_description || '';
        document.getElementById('resource_slug').value = resourceData.slug || '';
        document.getElementById('resource_image1_url').value = resourceData.image1_url || '';
        document.getElementById('resource_image2_url').value = resourceData.image2_url || '';
        document.getElementById('resource_image3_url').value = resourceData.image3_url || '';
        document.getElementById('resource_keywords').value = resourceData.keywords || '';
        document.getElementById('resource_description').value = resourceData.resource_description || '';
        document.getElementById('resource_status').value = resourceData.status || 'draft';
        document.getElementById('resource_publish_date').value = resourceData.publish_date || '';

        document.getElementById('resourceModalTitle').textContent = 'Edit Resource';

        // Load authors and select the current one
        loadResourceAuthors(resourceData.author_uuid);

        // Show modal
        document.getElementById("resourceModal").classList.remove("hidden");
    }

    // Handle form submission asynchronously
    async function submitResourceForm(event) {
        event.preventDefault();

        const formData = new FormData(document.getElementById("resource-form"));
        const data = Object.fromEntries(formData);

        // Validate publish status and publish date
        if (data.status === "publish" && !data.publish_date) {
            alert("Please select a publish date before publishing.");
            return;
        }

        // Validate author selection
        // if (!data.author_uuid) {
        //     alert("Please select an author.");
        //     return;
        // }

        const urlRegex = new RegExp('^[a-z0-9]+(?:-[a-z0-9]+)*$');
        if (!urlRegex.test(data.slug)) {
            alert("Enter valid URL slug, 'Ex: valid-url-slug'");
            return;
        }

        const app = document.getElementById("resources-app-select")?.value || 'kong_ai';
        data.resource_app = app;

        const resourceId = document.getElementById("resource_id").value;
        const resourceType = document.getElementById("resource_type").value;

        // Add pillar_id for subpages
        if (resourceType === 'subpage') {
            data.pillar_id = document.getElementById("pillar_id").value;
        }

        data.type = resourceType; // Add type to distinguish pillar vs subpage

        showLoading(resourceId.length ? 'Updating resource...' : 'Saving resource...');

        try {
            let response;
            if (resourceId) {
                data.uuid = resourceId;
                response = await fetch('/resources', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: data,
                        dbName: app,
                    }),
                });
            } else {
                response = await fetch('/resources', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: data,
                        dbName: app,
                    }),
                });
            }

            hideLoading();

            const result = await response.json();
            if (result.status === "success") {
                if (typeof loadPillarPages === 'function') {
                    loadPillarPages(); // Refresh the pillar pages list
                }
                if (typeof currentSelectedPillar !== 'undefined' && currentSelectedPillar) {
                    if (typeof loadSubpages === 'function') {
                        loadSubpages(currentSelectedPillar.uuid); // Refresh subpages if a pillar is selected
                    }
                }
                document.getElementById("resourceModal").classList.add("hidden");
                alert(resourceId ? 'Resource updated successfully!' : 'Resource created successfully!');
            } else {
                alert(`Error ${resourceId ? 'updating' : 'saving'} resource: ${result.message || 'Please try again.'}`);
            }
        } catch (error) {
            hideLoading();
            console.error("Error saving resource:", error);
            alert(`Error ${resourceId ? 'updating' : 'saving'} resource. Please try again.`);
        }
    }

    // Function to preview the resource description by converting Markdown to HTML
    function previewResource() {
        // Get the Markdown content from the description field
        const markdownText = document.getElementById('resource_description').value;

        // Make a POST request to convert Markdown to HTML
        fetch('/blog/md-convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ md_text: markdownText })
        })
            .then(response => response.json())  // Parse JSON response
            .then(data => {
                // Set the converted HTML in the preview section
                document.getElementById('resource-preview-html').innerHTML = data.html;
                // Show the preview column with the converted HTML
                document.getElementById('resource-preview-column').classList.remove('hidden');
            })
            .catch(error => console.error('Error:', error)); // Log any errors
    }

    // Load pillar pages for subpage selection
    function loadPillarPagesForSelection() {
        const pillarOptions = document.getElementById('pillar_options');
        pillarOptions.innerHTML = '';

        if (typeof allPillarPages !== 'undefined') {
            allPillarPages.forEach(pillar => {
                const option = document.createElement('div');
                option.className = 'p-3 hover:bg-gray-100 dark:hover:bg-neutral-700 cursor-pointer border-b border-gray-100 dark:border-neutral-700 last:border-b-0';
                option.onclick = () => selectPillarForSubpage(pillar);

                option.innerHTML = `
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${pillar.meta_title}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">${pillar.meta_description || 'No description'}</div>
                `;

                pillarOptions.appendChild(option);
            });
        }
    }

    // Toggle pillar dropdown visibility
    function togglePillarDropdown() {
        const dropdown = document.getElementById('pillar_dropdown_list');
        dropdown.classList.toggle('hidden');
    }

    // Select a pillar for subpage
    function selectPillarForSubpage(pillar) {
        document.getElementById('pillar_id').value = pillar.uuid;
        document.getElementById('selected_pillar_text').textContent = pillar.meta_title;
        document.getElementById('pillar_dropdown_list').classList.add('hidden');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function (event) {
        const pillarDropdown = document.getElementById('pillar_dropdown_list');
        const pillarTrigger = document.getElementById('resource_pillar_dropdown_trigger');

        if (pillarDropdown && pillarTrigger && !pillarTrigger.contains(event.target) && !pillarDropdown.contains(event.target)) {
            pillarDropdown.classList.add('hidden');
        }
    });

</script>

{% include 'admin/ai-content-common.html' %}

<style>
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #cccccc #f5f5f5;
    }
</style>