<div id="resources-container" class="bg-white min-h-screen">
    <div class="p-5 relative">
        <div class="pb-1 mb-2 border-dashed border-b flex justify-between items-center">
            <h2 class="text-xl font-bold text-neutral-900 dark:text-neutral-100">Resources</h2>
            <div class="flex space-x-3">
                <div
                    class="border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 dark:text-neutral-200 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all">
                    <input type="text" id="resources-search-input" placeholder="Search resources ..."
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full" />
                </div>
                <!-- Select App Field -->
                <div
                    class="form-group text-sm shrink-0 border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-1.5 rounded-md shadow-sm">
                    <select id="resources-app-select" name="app_name" onchange="handleAppSelectionChange()"
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                        <option value="" disabled>Select App</option>
                        <option value="kong_ai" selected>Kong.ai</option>
                        <option value="crm_io">CRM.io</option>
                        <option value="fav_ai">Fav.ai</option>
                        <option value="stark_ai">Stark.ai</option>
                        <option value="employers_ai">Employers.ai</option>
                        <option value="finder_io">Finder.io</option>
                        <option value="allrounder_ai">AllRounder.ai</option>
                        <option value="gharpe">Gharpe.com</option>
                        <option value="jobpe">Jobpe.com</option>
                        <option value="ai_bull">ai bull</option>
                    </select>
                </div>
                <!-- Authors Button -->
                <button
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="showAuthorsModal()">
                    Authors
                </button>
                <button
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="showResourceForm('pillar')">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Pillar Page
                </button>
                <button
                    class="dark:text-neutral-200 border opacity-50 border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="showResourceForm('subpage')" id="add-subpage-btn" disabled>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Subpage
                </button>
            </div>
        </div>

        <!-- Two Column Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-8rem)]">
            <!-- Left Column: Pillar Pages -->
            <div class="flex flex-col">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">Pillar Pages</h3>
                    <span id="pillar-count" class="text-sm text-gray-500 dark:text-gray-400">0 pages</span>
                </div>

                <div id="pillar-pages-container"
                    class="bg-neutral-50 dark:bg-neutral-800 border border-neutral-200/70 dark:border-neutral-700 rounded-md overflow-auto flex-1 custom-scrollbar">
                    <div id="pillar-pages-list" class="divide-y divide-gray-200 dark:divide-neutral-700">
                        <!-- Pillar pages will be populated here -->
                    </div>
                    <div id="pillar-placeholder" class="flex flex-col items-center justify-center h-full p-8">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-layers stroke-neutral-400 mb-3">
                            <path
                                d="m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z" />
                            <path d="m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65" />
                            <path d="m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65" />
                        </svg>
                        <p class="text-gray-500 dark:text-gray-400 text-center">No pillar pages yet.<br>Create your
                            first pillar page to get started.</p>
                    </div>
                </div>
            </div>

            <!-- Right Column: Subpages -->
            <div class="flex flex-col">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                        <span id="subpages-title">Subpages</span>
                        <span id="selected-pillar-name"
                            class="text-sm font-normal text-gray-500 dark:text-gray-400"></span>
                    </h3>
                    <span id="subpage-count" class="text-sm text-gray-500 dark:text-gray-400">0 pages</span>
                </div>

                <div id="subpages-container"
                    class="bg-neutral-50 dark:bg-neutral-800 border border-neutral-200/70 dark:border-neutral-700 rounded-md overflow-auto flex-1 custom-scrollbar">
                    <div id="subpages-list" class="divide-y divide-gray-200 dark:divide-neutral-700">
                        <!-- Subpages will be populated here -->
                    </div>
                    <div id="subpages-placeholder" class="flex flex-col items-center justify-center h-full p-8">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-file-text stroke-neutral-400 mb-3">
                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                            <polyline points="14,2 14,8 20,8" />
                            <line x1="16" y1="13" x2="8" y2="13" />
                            <line x1="16" y1="17" x2="8" y2="17" />
                            <polyline points="10,9 9,9 8,9" />
                        </svg>
                        <p class="text-gray-500 dark:text-gray-400 text-center">
                            <span id="subpages-placeholder-text">Select a pillar page to view its subpages.</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        {% include 'admin/add-resource.html' %}
    </div>
</div>

<!-- Authors Container (hidden by default) -->
<div id="authors-container" class="hidden">
    {% include 'admin/authors.html' %}
</div>

<script>
    // Global variables for resources
    let currentSelectedPillar = null;
    let allPillarPages = [];
    let allSubpages = [];

    document.addEventListener('DOMContentLoaded', async () => {
        // Initialize resources functionality
        await loadPillarPages();

        // Select the first pillar page if there are any
        if (allPillarPages.length > 0) {
            selectPillar(allPillarPages[0]);
        }

        // Search functionality
        document.getElementById('resources-search-input').addEventListener('keypress', function (event) {
            if (event.key === 'Enter') {
                searchResources();
            }
        });
    });

    function handleAppSelectionChange() {
        currentSelectedPillar = null;
        clearSubpages();
        loadPillarPages();
        loadResourceAuthors();
    }


    async function loadPillarPages() {
        const app = document.getElementById("resources-app-select").value;
        if (!app) return;

        showLoading("Loading pillar pages...");

        try {
            const response = await fetch(`/resources/pillars?app_name=${app}`);
            const result = await response.json();

            if (result.status === "success") {
                allPillarPages = result.data;
                renderPillarPages(result.data);
            }
        } catch (error) {
            console.error("Error loading pillar pages:", error);
            renderPillarPages([]);
        } finally {
            hideLoading();
        }
    }

    function renderPillarPages(pillarPages) {
        const pillarList = document.getElementById('pillar-pages-list');
        const pillarPlaceholder = document.getElementById('pillar-placeholder');
        const pillarCount = document.getElementById('pillar-count');

        pillarList.innerHTML = '';
        pillarCount.textContent = `${pillarPages.length} pages`;

        if (pillarPages.length === 0) {
            pillarPlaceholder.classList.remove('hidden');
            return;
        }

        pillarPlaceholder.classList.add('hidden');

        pillarPages.forEach(pillar => {
            const pillarItem = document.createElement('div');
            const isSelected = currentSelectedPillar?.uuid === pillar.uuid;
            pillarItem.className = `p-4 hover:bg-gray-50 dark:hover:bg-neutral-700 cursor-pointer transition-colors relative ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20 pillar-selected' : ''
                }`;

            pillarItem.onclick = () => selectPillar(pillar);

            pillarItem.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">${pillar.meta_title || 'Untitled'}</h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">${pillar.meta_description || 'No description'}</p>
                        <div class="flex items-center mt-2 space-x-4 text-xs text-gray-400">
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                ${pillar.created_date ? new Date(pillar.created_date).toLocaleDateString() : 'No date'}
                            </span>
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                ${pillar.author_name || 'No author'}
                            </span>
                            <span class="px-2 py-1 rounded-full text-xs ${pillar.status === 'publish' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}">${pillar.status || 'draft'}</span>
                        </div>
                    </div>
                    <div class="flex space-x-1 ml-2">
                        <button onclick="event.stopPropagation(); editPillar('${pillar.uuid}')"
                                class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;

            pillarList.appendChild(pillarItem);
        });
    }

    function selectPillar(pillar) {
        currentSelectedPillar = pillar;

        // Update UI to show selected state
        renderPillarPages(allPillarPages);

        // Enable add subpage button
        document.getElementById('add-subpage-btn').disabled = false;
        document.getElementById('add-subpage-btn').classList.remove('opacity-50');

        // Update subpages section
        document.getElementById('selected-pillar-name').textContent = `for "${pillar.meta_title}"`;

        // Load subpages for this pillar
        loadSubpages(pillar.uuid);
    }

    async function loadSubpages(pillarId) {
        const app = document.getElementById("resources-app-select").value;

        showLoading("Loading subpages...");

        try {
            const response = await fetch(`/resources/subpages?app_name=${app}&pillar_id=${pillarId}`);
            const result = await response.json();

            if (result.status === "success") {
                allSubpages = result.data;
                renderSubpages(result.data);
            }
        } catch (error) {
            console.error("Error loading subpages:", error);
            renderSubpages([]);
        } finally {
            hideLoading();
        }
    }

    function renderSubpages(subpages) {
        const subpagesList = document.getElementById('subpages-list');
        const subpagesPlaceholder = document.getElementById('subpages-placeholder');
        const subpageCount = document.getElementById('subpage-count');

        subpagesList.innerHTML = '';
        subpageCount.textContent = `${subpages.length} pages`;

        if (subpages.length === 0) {
            subpagesPlaceholder.classList.remove('hidden');
            document.getElementById('subpages-placeholder-text').textContent =
                currentSelectedPillar ? 'No subpages yet. Add the first subpage for this pillar.' : 'Select a pillar page to view its subpages.';
            return;
        }

        subpagesPlaceholder.classList.add('hidden');

        subpages.forEach(subpage => {
            const subpageItem = document.createElement('div');
            subpageItem.className = 'p-4 hover:bg-gray-50 dark:hover:bg-neutral-700 transition-colors';

            subpageItem.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">${subpage.meta_title || 'Untitled'}</h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">${subpage.meta_description || 'No description'}</p>
                        <div class="flex items-center mt-2 space-x-4 text-xs text-gray-400">
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                ${subpage.created_date ? new Date(subpage.created_date).toLocaleDateString() : 'No date'}
                            </span>
                            <span class="flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                ${subpage.author_name || 'No author'}
                            </span>
                            <span class="px-2 py-1 rounded-full text-xs ${subpage.status === 'publish' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'}">${subpage.status || 'draft'}</span>
                        </div>
                    </div>
                    <div class="flex space-x-1 ml-2">
                        <button onclick="editSubpage('${subpage.uuid}')"
                                class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            `;

            subpagesList.appendChild(subpageItem);
        });
    }

    function clearSubpages() {
        document.getElementById('selected-pillar-name').textContent = '';
        document.getElementById('add-subpage-btn').disabled = true;
        document.getElementById('add-subpage-btn').classList.add('opacity-50');
        renderSubpages([]);
    }

    function searchResources() {
        const query = document.getElementById('resources-search-input').value.trim();
        currentSelectedPillar = null;
        if (query.length >= 1) {
            // Filter pillar pages based on search query
            const filteredPillars = allPillarPages.filter(pillar =>
                pillar.meta_title?.toLowerCase().includes(query.toLowerCase()) ||
                pillar.meta_description?.toLowerCase().includes(query.toLowerCase())
            );
            renderPillarPages(filteredPillars);
        } else {
            renderPillarPages(allPillarPages);
        }
    }

    function editPillar(pillarUuid) {
        const pillar = allPillarPages.find(p => p.uuid === pillarUuid);
        if (pillar) {
            showResourceForm('pillar', pillar);
        }
    }

    function editSubpage(subpageUuid) {
        const subpage = allSubpages.find(s => s.uuid === subpageUuid);
        if (subpage) {
            showResourceForm('subpage', subpage);
        }
    }

    function showResourceForm(type, resource = null) {
        document.getElementById("resourceModal").classList.remove("hidden");

        // Set resource type
        document.getElementById("resource_type").value = type;

        // Update modal title and form based on type
        const modalTitle = document.getElementById("resourceModalTitle");
        const pillarSelectionField = document.getElementById("pillar-selection-field");
        const saveButton = document.getElementById("resourceSaveButton");

        if (type === 'pillar') {
            modalTitle.textContent = resource ? "Edit Pillar Page" : "Add Pillar Page";
            pillarSelectionField.classList.add('hidden');
        } else if (type === 'subpage') {
            modalTitle.textContent = resource ? "Edit Subpage" : "Add Subpage";
            pillarSelectionField.classList.remove('hidden');
            loadPillarPagesForSelection();

            // If we have a selected pillar and we're adding a new subpage, pre-select it
            if (!resource && currentSelectedPillar) {
                document.getElementById('pillar_id').value = currentSelectedPillar.uuid;
                document.getElementById('selected_pillar_text').textContent = currentSelectedPillar.meta_title;
            }
        }

        // If editing, populate form with existing data
        if (resource) {
            saveButton.textContent = "Update";
            document.getElementById('resource_id').value = resource.uuid || '';
            document.getElementById('resource_meta_title').value = resource.meta_title || '';
            document.getElementById('resource_meta_description').value = resource.meta_description || '';
            document.getElementById('resource_slug').value = resource.slug || '';
            document.getElementById('resource_keywords').value = resource.keywords || '';
            document.getElementById('resource_status').value = resource.status || 'draft';
            document.getElementById('resource_publish_date').value = resource.publish_date || '';
            document.getElementById('resource_image1_url').value = resource.image1_url || '';
            document.getElementById('resource_image2_url').value = resource.image2_url || '';
            document.getElementById('resource_image3_url').value = resource.image3_url || '';
            document.getElementById('resource_description').value = resource.resource_description || '';
            document.getElementById('author_uuid').value = resource.author_uuid || '';

            // Show or hide publish date field based on status
            const publishDateContainer = document.getElementById('resource-publish-date-container');
            if (resource.status === 'publish') {
                publishDateContainer.classList.remove('hidden');
            } else {
                publishDateContainer.classList.add('hidden');
            }

            // Set pillar for subpages
            if (type === 'subpage' && resource.pillar_id) {
                document.getElementById('pillar_id').value = resource.pillar_id;
                const pillar = allPillarPages.find(p => p.uuid === resource.pillar_id);
                if (pillar) {
                    document.getElementById('selected_pillar_text').textContent = pillar.meta_title;
                }
            }
        } else {
            // Reset form for new resource
            saveButton.textContent = "Save";
            resetResourceForm();
        }

        // Preview the content
        previewResource();
    }

    function resetResourceForm() {
        document.getElementById('resource_id').value = '';
        document.getElementById('resource_meta_title').value = '';
        document.getElementById('resource_meta_description').value = '';
        document.getElementById('resource_slug').value = '';
        document.getElementById('resource_keywords').value = '';
        document.getElementById('resource_status').value = 'draft';
        document.getElementById('resource_publish_date').value = '';
        document.getElementById('resource_image1_url').value = '';
        document.getElementById('resource_image2_url').value = '';
        document.getElementById('resource_image3_url').value = '';
        document.getElementById('resource_description').value = '';
        document.getElementById('resource-publish-date-container').classList.add('hidden');
        document.getElementById('pillar_id').value = '';
        document.getElementById('selected_pillar_text').textContent = 'Select Pillar Page';
        document.getElementById('author_uuid').value = '';
    }

    // Show the loading screen with a message
    function showLoading(message = "Loading...") {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-screen').classList.remove('hidden');
    }

    // Hide the loading screen
    function hideLoading() {
        document.getElementById('loading-screen').classList.add('hidden');
    }
</script>

<style>
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #cccccc #f5f5f5;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .opacity-50 {
        opacity: 0.5;
    }

    /* Ensure selected pillar border takes precedence over divide borders */
    .pillar-selected {
        border-left: 4px solid rgb(59 130 246) !important;
        border-top: none !important;
        border-bottom: none !important;
    }
</style>