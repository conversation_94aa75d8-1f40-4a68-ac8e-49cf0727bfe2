<div>
    <div id="blogModal" data-clarity-unmask="true"
        class="modal fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-[50] hidden">
        <div class="bg-white alert-modal dark:bg-neutral-900 rounded-[20px] w-[90vw] max-h-[85vh] flex flex-col">

            <!-- Mo<PERSON> Header (Fixed at top) -->
            <div
                class="flex justify-between px-6 py-4 border-b border-neutral-200/70 dark:border-neutral-700 items-center">
                <h2 id="blogModalTitle" class="font-bold text-md text-neutral-900 dark:text-neutral-100">
                    Add Blog
                </h2>
                <span id="blogModalClose"
                    class="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 cursor-pointer cursor-pointer text-2xl"><svg
                        xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-x">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg></span>
            </div>

            <!-- Modal Body (Scrollable content) -->
            <div class="px-6 py-6 overflow-auto lg:overflow-hidden">
                <input type="hidden" id="blog_id" name="blog_id" value="">
                <form id="blog-form" class="flex flex-wrap lg:flex-nowrap gap-5 h-full"
                    onsubmit="return submitBlogForm(event)">
                    <!-- Column 1: Form Fields -->
                    <div
                        class="w-full lg:w-1/3 p-2 flex flex-col gap-4 h-[calc(100vh-317px)] overflow-auto custom-scrollbar">

                        <!-- Meta Title -->
                        <div class="form-group flex-1">
                            <label for="meta_title"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Meta
                                Title</label>
                            <textarea name="meta_title" id="meta_title" rows="1" placeholder="Enter meta title"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required></textarea>
                        </div>

                        <!-- Meta Description -->
                        <div class="form-group flex-1">
                            <label for="meta_description"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Meta
                                Description</label>
                            <textarea name="meta_description" id="meta_description" rows="3"
                                placeholder="Enter meta description"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required></textarea>
                        </div>

                        <!-- Is Featured Checkbox -->
                        <div class="form-group flex-1">
                            <div class="flex items-center gap-2">
                                <input type="checkbox" id="is_featured" name="is_featured"
                                    class="accent-current w-4 h-4 text-slate-900 border-neutral-300 dark:border-neutral-600 rounded-md">
                                <label for="is_featured" class="text-sm text-neutral-900 dark:text-neutral-400">
                                    Is Featured
                                </label>
                            </div>
                        </div>

                        <!-- Author Selection Field - SIMPLIFIED -->
                        <div class="form-group flex-1">
                            <label for="author_uuid"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Author</label>
                            <select id="author_uuid" name="author_uuid"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800">
                                <option value="">Select Author</option>
                            </select>
                        </div>

                        <!-- URL Field -->
                        <div class="form-group flex-1">
                            <label for="slug"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">URL</label>
                            <input name="slug" id="slug" type="text"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required placeholder="Enter blog URL (e.g., valid-url-slug)" />
                        </div>

                        <!-- Image URLs -->
                        <div class="form-group flex-1">
                            <label class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Image
                                URLs</label>
                            <div class="flex items-center mt-2">
                                <input id="image1_url" type="url"
                                    class="flex-1 border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                    name="image1_url" required placeholder="Image 1 URL" />
                                <button type="button" onclick="generateAIImage('image1_url', 1)"
                                    class="ml-2 px-3 py-2 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md transition-colors duration-200 flex items-center gap-1.5 whitespace-nowrap">
                                    <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000"
                                        alt="AI" class="w-4 h-4 opacity-70">
                                </button>
                            </div>
                            <div class="flex items-center mt-2">
                                <input id="image2_url" type="url"
                                    class="flex-1 border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                    name="image2_url" placeholder="Image 2 URL" />
                                <button type="button" onclick="generateAIImage('image2_url', 2)"
                                    class="ml-2 px-3 py-2 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md transition-colors duration-200 flex items-center gap-1.5 whitespace-nowrap">
                                    <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000"
                                        alt="AI" class="w-4 h-4 opacity-70">
                                </button>
                            </div>
                            <div class="flex items-center mt-2">
                                <input id="image3_url" type="url"
                                    class="flex-1 border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                    name="image3_url" placeholder="Image 3 URL" />
                                <button type="button" onclick="generateAIImage('image3_url', 3)"
                                    class="ml-2 px-3 py-2 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs rounded-md transition-colors duration-200 flex items-center gap-1.5 whitespace-nowrap">
                                    <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000"
                                        alt="AI" class="w-4 h-4 opacity-70">
                                </button>
                            </div>
                        </div>

                        <!-- Keywords Field -->
                        <div class="form-group flex-1">
                            <label for="keywords"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Keywords</label>
                            <input name="keywords" id="keywords" type="text"
                                class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                placeholder="Enter keywords separated by commas (e.g., primary, secondary, other)" />
                        </div>

                    </div>

                    <!-- Column 2: Blog Description -->
                    <div class="w-full lg:w-1/3 flex flex-col gap-4 h-full">
                        <!-- Page Content Field -->
                        <div class="form-group flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <label for="blog_description"
                                    class="block text-sm font-medium text-neutral-800 dark:text-neutral-200">Page
                                    Content</label>
                                <div class="flex items-center gap-2">
                                    <button type="button" onclick="loadAIContent('blog')"
                                        class="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 border border-neutral-300 dark:border-neutral-600 hover:border-neutral-400 dark:hover:border-neutral-500 rounded-md transition-colors"
                                        title="Load AI Content">
                                        <img src="https://img.icons8.com/?size=150&id=MTnnE7FNiELB&format=png&color=000000"
                                            alt="AI" class="w-4 h-4 opacity-70">
                                        <span>AI Content</span>
                                    </button>
                                    <button type="button" onclick="humanizeContent('blog')"
                                        class="flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 border border-neutral-300 dark:border-neutral-600 hover:border-neutral-400 dark:hover:border-neutral-500 rounded-md transition-colors"
                                        title="Humanize Content">
                                        <svg class="w-4 h-4 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <span>Humanize</span>
                                    </button>
                                    <div class="relative">
                                        <button type="button"
                                            class="ai-info-trigger p-1 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 transition-colors rounded hover:bg-neutral-100 dark:hover:bg-neutral-700"
                                            title="AI Content Information" onclick="toggleAIInfoTooltip(this)">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <path d="M9,9h0a3,3,0,0,1,6,0c0,2-3,3-3,3"></path>
                                                <path d="M12,17h.01"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <textarea id="blog_description" name="blog_description"
                                class="text-sm w-full h-[calc(100vh-350px)] custom-scrollbar border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800"
                                required></textarea>
                        </div>
                    </div>

                    <!-- Column 3: Preview Container -->
                    <div class="w-full lg:w-1/3 flex flex-col gap-4">
                        <!-- Flex Container for Publish and Publish Date -->
                        <div class="flex gap-4">
                            <!-- Status Field -->
                            <div class="form-group flex-1">
                                <label for="status"
                                    class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Status</label>
                                <div
                                    class="text-sm border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-2.5 rounded-md shadow-sm">
                                    <select id="status" name="status"
                                        class="w-full bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm"
                                        required>
                                        <option value="draft" selected>Draft</option>
                                        <option value="publish">Publish</option>
                                    </select>
                                </div>
                            </div>
                            <!-- Publish Date Field -->
                            <div id="publish-date-container" class="form-group flex-1 hidden">
                                <label for="publish_date"
                                    class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Publish
                                    Date & Time</label>
                                <input type="datetime-local" id="publish_date" name="publish_date"
                                    class="text-sm w-full border px-4 py-2.5 border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-white dark:bg-neutral-800" />
                            </div>
                        </div>

                        <!-- Markdown Preview Section -->
                        <div id="preview-column" class="bg-white rounded-md flex-1">
                            <label id="heading"
                                class="block text-sm font-medium text-neutral-800 dark:text-neutral-200 mb-2">Preview
                            </label>
                            <div id="preview-html"
                                class="text-sm border border-neutral-300 dark:border-neutral-600 p-2 rounded shadow-md mt-2 text-sm text-gray-500 overflow-auto pr-1 h-[calc(100vh-438px)] custom-scrollbar">
                                <!-- Preview content will be displayed here -->
                            </div>
                        </div>
                    </div>
                </form>

            </div>

            <!-- Modal Footer (Fixed at bottom) -->
            <div
                class="flex items-center justify-end gap-4 px-6 py-4 border-t border-neutral-200/70 dark:border-neutral-700">
                <button type="submit" id="blogSaveButton" form="blog-form"
                    class="w-auto min-h-[38px] px-6 py-1 flex items-center justify-center text-white text-[14px] font-medium bg-slate-900 rounded-md">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        let typingTimer;
        const typingInterval = 1000;
        const inputField = document.getElementById("blog_description");

        inputField.addEventListener("input", () => {
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                preview()
            }, typingInterval);
        });

        const statusField = document.getElementById('status');
        const publishDateContainer = document.getElementById('publish-date-container');
        const publishDateInput = document.getElementById('publish_date');

        statusField.addEventListener('change', () => {
            if (statusField.value === 'publish') {
                publishDateContainer.classList.remove('hidden');
            } else {
                publishDateContainer.classList.add('hidden');
                publishDateInput.value = '';
            }
        });

        // Load authors when page loads
        loadAuthors();
    });

    document.getElementById("blogModalClose").addEventListener("click", () => {
        document.getElementById("blogModal").classList.add("hidden");
    });

    // SIMPLIFIED AUTHOR LOADING FUNCTION
    async function loadAuthors(selectedAuthorId = null) {
        const authorSelect = document.getElementById('author_uuid');

        // Show loading option
        authorSelect.innerHTML = '<option value="">Loading authors...</option>';

        try {
            const app = document.getElementById("app-select").value;
            const response = await fetch(`/authors?app_name=${app}&limit=50`);
            const result = await response.json();

            // Clear loading and add default option
            authorSelect.innerHTML = '<option value="">Select Author</option>';

            if (result.status === "success" && result.data.length > 0) {
                result.data.forEach(author => {
                    const option = document.createElement('option');
                    option.value = author.uuid;
                    option.textContent = `${author.name} (${author.email})`;

                    // Set selected if this matches the current author
                    if (selectedAuthorId && selectedAuthorId === author.uuid) {
                        option.selected = true;
                    }

                    authorSelect.appendChild(option);
                });
            } else {
                authorSelect.innerHTML = '<option value="">No authors available</option>';
            }
        } catch (error) {
            console.error('Error loading authors:', error);
            authorSelect.innerHTML = '<option value="">Error loading authors</option>';
        }
    }

    // FUNCTIONS TO CALL WHEN OPENING MODAL
    function openNewBlogModal() {
        // Reset form
        document.getElementById('blog-form').reset();
        document.getElementById('blog_id').value = '';
        document.getElementById('blogModalTitle').textContent = 'Add New Blog';

        // Load authors
        loadAuthors();

        // Show modal
        document.getElementById("blogModal").classList.remove("hidden");
    }

    function openEditBlogModal(blogData) {
        // Populate form fields
        document.getElementById('blog_id').value = blogData.uuid || '';
        document.getElementById('meta_title').value = blogData.meta_title || '';
        document.getElementById('meta_description').value = blogData.meta_description || '';
        document.getElementById('is_featured').checked = blogData.is_featured || false;
        document.getElementById('slug').value = blogData.slug || '';
        document.getElementById('image1_url').value = blogData.image1_url || '';
        document.getElementById('image2_url').value = blogData.image2_url || '';
        document.getElementById('image3_url').value = blogData.image3_url || '';
        document.getElementById('keywords').value = blogData.keywords || '';
        document.getElementById('blog_description').value = blogData.blog_description || '';
        document.getElementById('status').value = blogData.status || 'draft';
        document.getElementById('publish_date').value = blogData.publish_date || '';

        document.getElementById('blogModalTitle').textContent = 'Edit Blog';

        // Load authors and select the current one
        loadAuthors(blogData.author_uuid);

        // Show modal
        document.getElementById("blogModal").classList.remove("hidden");
    }

    // Handle form submission asynchronously
    async function submitBlogForm(event) {
        event.preventDefault();

        const formData = new FormData(document.getElementById("blog-form"));
        const data = Object.fromEntries(formData);

        // Validate publish status and publish date
        if (data.status === "publish" && !data.publish_date) {
            alert("Please select a publish date before publishing.");
            return;
        }

        const urlRegex = new RegExp('^[a-z0-9]+(?:-[a-z0-9]+)*$');
        if (!urlRegex.test(data.slug)) {
            alert("Enter valid URL slug, 'Ex: valid-url-slug'");
            return;
        }

        if (data.is_featured) {
            data.is_featured = true;
        }

        // Add selected related blogs to form data (if you have this feature)
        if (typeof selectedBlogs !== 'undefined') {
            data.related_blogs = selectedBlogs;
        }

        const app = document.getElementById("app-select").value;
        data.blog_app = app;

        if (app === 'gharpe') {
            const project = document.getElementById("gharpe-select").value;
            if (!project) {
                alert("Please select a project.");
                return;
            }
            data.project_name = project;
        }

        const blogId = document.getElementById("blog_id").value;


        // showLoading(blogId ? 'Updating blog...' : 'Saving blog...');

        // try {
        let response;

        if (blogId) {
            data.uuid = blogId;
            console.log("Submitting data:", data, blogId);
            // Update existing blog
            response = await fetch('/blogs', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    uuid: blogId,
                    data: data,
                    dbName: app,
                }),
            });
        } else {
            // Create new blog
            response = await fetch('/blogs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data: data,
                    dbName: app,
                }),
            });
        }

        hideLoading();
        const result = await response.json();
        if (result.status === "success") {
            if (typeof createBlogsList === 'function') {
                createBlogsList();
            }
            document.getElementById("blogModal").classList.add("hidden");
            // alert(blogId ? 'Blog updated successfully!' : 'Blog created successfully!');
        } else {
            alert(`Error ${blogId ? 'updating' : 'saving'} blog: ${result.message || 'Please try again.'}`);
        }
        // } catch (error) {
        //     hideLoading();
        //     console.error('Error submitting blog:', error);
        //     alert(`Error ${blogId ? 'updating' : 'saving'} blog. Please try again.`);
        // }
    }

    // Function to preview the blog description by converting Markdown to HTML
    function preview() {
        const markdownText = document.getElementById('blog_description').value;

        fetch('/blog/md-convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ md_text: markdownText })
        })
            .then(response => response.json())
            .then(data => {
                document.getElementById('preview-html').innerHTML = data.html;
                document.getElementById('preview-column').classList.remove('hidden');
            })
            .catch(error => console.error('Error:', error));
    }

</script>

{% include 'admin/ai-content-common.html' %}

<style>
    .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #cccccc #f5f5f5;
    }
</style>