<div id="socials-container" class="bg-white min-h-screen">
    <div class="p-5 relative">
        <div class="pb-1 mb-2 border-dashed border-b flex justify-between items-center">
            <h2 class="text-xl font-bold text-neutral-900 dark:text-neutral-100">Socials</h2>
            <div class="flex space-x-3">
                <div
                    class="border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 dark:text-neutral-200 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all">
                    <input type="text" id="search-input" placeholder="Search socials ..."
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full" />
                </div>
                <div
                    class="form-group text-sm shrink-0 border border-gray-300 cursor-pointer dark:bg-neutral-800 dark:border-neutral-600 focus:border focus:border-neutral-400 focus:outline-0 px-3 py-1.5 rounded-md shadow-sm">
                    <select id="app-select" name="app_name"
                        class="bg-transparent block cursor-pointer dark:bg-neutral-800 dark:text-neutral-100 focus:outline-0 text-sm w-full">
                        <option value="" disabled>Select App</option>
                        <option value="kong_ai" selected>Kong.ai</option>
                        <option value="crm_io">CRM.io</option>
                        <option value="fav_ai">Fav.ai</option>
                        <option value="stark_ai">Stark.ai</option>
                        <option value="employers_ai">Employers.ai</option>
                        <option value="finder_io">Finder.io</option>
                        <option value="allrounder_ai">AllRounder.ai</option>
                        <option value="gharpe">Gharpe.com</option>
                        <option value="jobpe">Jobpe.com</option>
                        <option value="ai_bull">ai bull</option>
                    </select>
                </div>
                <button
                    class="dark:text-neutral-200 border border-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-900 flex font-medium hover:bg-gray-100 hover:shadow-sm items-center px-4 py-1.5 relative rounded-md text-neutral-900 text-sm transition-all"
                    onclick="openAddSocialModal()">Add</button>
            </div>
        </div>

        <div id="table-container" class="lg:bg-neutral-50 dark:bg-neutral-800 border-neutral-200/70 rounded-md lg:text-wrap text-nowrap
                overflow-auto max-h-[calc(100vh-6rem)] custom-scrollbar">
            <table class="min-w-full text-sm">
                <thead class="bg-gray-100/70 dark:bg-neutral-950 sticky -top-[1px] z-10 backdrop-blur">
                    <tr
                        class="border-b border-neutral-200/70 bg-neutral-100/40 dark:bg-neutral-800/70 dark:border-neutral-700">
                        <th
                            class="px-4 py-1.5 font-normal w-[30%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Title</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[25%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Primary Keyword</th>
                        <th
                            class="px-4 py-1.5 font-normal w-[15%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Platform</th>
                        <th class="px-4 py-1.5 font-normal w-[15%] text-left text-sm text-gray-600 dark:text-neutral-400 cursor-pointer hover:bg-gray-200 dark:hover:bg-neutral-700 transition-colors"
                            onclick="toggleSort('created_date')">
                            <div class="flex items-center space-x-1">
                                <span>Created Date</span>
                                <svg id="sort-arrow-created_date" class="w-4 h-4 transition-transform duration-200"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </th>
                        <th
                            class="px-4 py-1.5 font-normal w-[15%] text-left text-sm text-gray-600 dark:text-neutral-400">
                            Actions</th>
                    </tr>
                </thead>
                <tbody id="socials-data-table" class="divide-y divide-gray-200 mobile-card"></tbody>
            </table>
        </div>

        <!-- Load More Button -->
        <div id="load-more-container" class="flex justify-center mt-4 hidden">
            <button id="load-more-btn" onclick="loadMoreSocials()"
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200 flex items-center space-x-2">
                <span id="load-more-text">Load More</span>
                <svg id="load-more-spinner" class="animate-spin h-4 w-4 text-white hidden"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                </svg>
            </button>
        </div>

        <div id="socials-placeholder"
            class="border border-dashed flex flex-col h-64 items-center justify-center mt-2 rounded-2xl hidden">
            <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-share-2 stroke-neutral-500">
                <circle cx="18" cy="5" r="3" />
                <circle cx="6" cy="12" r="3" />
                <circle cx="18" cy="19" r="3" />
                <line x1="8.59" x2="15.42" y1="13.51" y2="17.49" />
                <line x1="15.41" x2="8.59" y1="6.51" y2="10.49" />
            </svg>
            <p class="text-gray-500">No socials available. Please add a social to get started.</p>
        </div>

        {% include 'admin/socials/add-socials.html' %}
        {% include 'admin/socials/view-social.html' %}
    </div>
</div>

<script>
    let currentPage = 1;
    let currentQuery = '';
    let hasMoreSocials = true;
    let fetchAllSocials = [];
    let currentSortBy = 'created_date';
    let currentSortOrder = 'desc';

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function () {
        restoreLastSelectedApp();
        updateSortArrow('created_date'); // Initialize sort arrow
        loadSocials();
    });

    // Save selected app when changed
    function onAppChange() {
        const appSelect = document.getElementById('app-select');

        if (appSelect && appSelect.value) {
            // Save the selected app to localStorage
            saveLastSelectedApp(appSelect.value);
        }
    }

    // Save last selected app to localStorage
    function saveLastSelectedApp(appValue) {
        if (appValue) {
            localStorage.setItem('lastSelectedApp_socials', appValue);
        }
    }

    // Restore last selected app from localStorage
    function restoreLastSelectedApp() {
        const appSelect = document.getElementById('app-select');
        const lastSelectedApp = localStorage.getItem('lastSelectedApp_socials');

        if (lastSelectedApp && appSelect) {
            // Check if the saved app value exists in the dropdown
            const optionExists = Array.from(appSelect.options).some(option => option.value === lastSelectedApp);

            if (optionExists) {
                appSelect.value = lastSelectedApp;
            } else {
                // If saved app doesn't exist, select the first available option
                const firstOption = appSelect.querySelector('option:not([disabled])');
                if (firstOption) {
                    appSelect.value = firstOption.value;
                }
            }
        } else if (appSelect) {
            // If no saved app, select the first available option
            const firstOption = appSelect.querySelector('option:not([disabled])');
            if (firstOption) {
                appSelect.value = firstOption.value;
            }
        }
    }

    // Load socials data
    async function loadSocials(page = 1, query = '', reset = true) {
        try {
            const appSelect = document.getElementById('app-select');
            const app = appSelect ? appSelect.value : 'kong_ai';

            if (!app) {
                showNotification('Please select an app first', 'warning');
                return;
            }

            if (reset) {
                currentPage = 1;
                hasMoreSocials = true;
                // Clear the table when resetting
                document.getElementById("socials-data-table").innerHTML = "";
                fetchAllSocials = [];
            }

            let apiUrl = `/socials?app_name=${app}&page=${page}&limit=50&sort_by=${currentSortBy}&sort_order=${currentSortOrder}`;

            if (query) {
                apiUrl += `&query=${encodeURIComponent(query)}`;
            }

            const response = await fetch(apiUrl);
            const result = await response.json();

            if (result.status === 'success') {
                const socialsList = result.data;

                fetchAllSocials = [...fetchAllSocials, ...socialsList];

                if (socialsList.length > 0) {
                    const socialsDataTable = document.getElementById("socials-data-table");
                    const socialsPlaceholder = document.getElementById("socials-placeholder");
                    const loadMoreContainer = document.getElementById("load-more-container");

                    socialsPlaceholder.classList.add("hidden");

                    // Append socials to the table
                    for (const social of socialsList) {
                        const row = document.createElement("tr");
                        row.className = "hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer";

                        const platform = social.content_type ||
                            (social.social_posts && social.social_posts.length > 0 ? social.social_posts[0].platform : 'N/A');

                        const title = social.meta_title || social.app_name || 'Untitled';

                        const createdDate = social.created_date ? new Date(social.created_date).toLocaleDateString() : 'N/A';

                        row.innerHTML = `
                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">
                            <div class="font-medium">${title}</div>
                        </td>
                        <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                            ${social.primary_keyword || 'N/A'}
                        </td>
                        <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlatformColorClasses(platform)}">
                                ${platform.charAt(0).toUpperCase() + platform.slice(1)}
                            </span>
                        </td>
                        <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                            ${createdDate}
                        </td>
                        <td class="px-4 py-2 text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <button onclick="viewSocial('${social.uuid}')" 
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
                                        title="View Social">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                <button onclick="deleteSocial('${social.uuid}')" 
                                        class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900/20"
                                        title="Delete Social">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    `;

                        socialsDataTable.appendChild(row);
                    }

                    // Show/hide load more button based on backend response
                    hasMoreSocials = result.has_more;

                    if (hasMoreSocials) {
                        loadMoreContainer.classList.remove("hidden");
                    } else {
                        loadMoreContainer.classList.add("hidden");
                    }
                } else {
                    // No socials found - clear table and show placeholder
                    document.getElementById("socials-data-table").innerHTML = "";
                    const socialsPlaceholder = document.getElementById("socials-placeholder");
                    const loadMoreContainer = document.getElementById("load-more-container");
                    socialsPlaceholder.classList.remove("hidden");
                    loadMoreContainer.classList.add("hidden");
                    hasMoreSocials = false;
                }
            } else {
                // API returned error - clear table and show placeholder
                document.getElementById("socials-data-table").innerHTML = "";
                const socialsPlaceholder = document.getElementById("socials-placeholder");
                const loadMoreContainer = document.getElementById("load-more-container");
                socialsPlaceholder.classList.remove("hidden");
                loadMoreContainer.classList.add("hidden");
                showNotification('Failed to load socials', 'error');
            }
        } catch (error) {
            console.error('Error loading socials:', error);
            // Clear table on error and show placeholder
            document.getElementById("socials-data-table").innerHTML = "";
            const socialsPlaceholder = document.getElementById("socials-placeholder");
            const loadMoreContainer = document.getElementById("load-more-container");
            socialsPlaceholder.classList.remove("hidden");
            loadMoreContainer.classList.add("hidden");
            showNotification('Error loading socials', 'error');
        }
    }

    // Load more socials function
    async function loadMoreSocials() {
        if (!hasMoreSocials) {
            return;
        }

        // Show loading state
        const loadMoreBtn = document.getElementById("load-more-btn");
        const loadMoreText = document.getElementById("load-more-text");
        const loadMoreSpinner = document.getElementById("load-more-spinner");

        loadMoreBtn.disabled = true;
        loadMoreText.textContent = "Loading...";
        loadMoreSpinner.classList.remove("hidden");

        try {
            currentPage += 1;
            await loadSocials(currentPage, currentQuery, false);
        } catch (error) {
            console.error('Error loading more socials:', error);
            showNotification('Error loading more socials', 'error');
        } finally {
            // Reset loading state
            loadMoreBtn.disabled = false;
            loadMoreText.textContent = "Load More";
            loadMoreSpinner.classList.add("hidden");
        }
    }


    // Get platform color classes for badges
    function getPlatformColorClasses(platform) {
        const colors = {
            'twitter': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
            'linkedin': 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100',
            'medium': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100',
            'instagram': 'bg-pink-100 text-pink-800 dark:bg-pink-800 dark:text-pink-100',
            'meta': 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
        };
        return colors[platform.toLowerCase()] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100';
    }



    // Search functionality
    document.getElementById('search-input').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            const query = this.value.trim();
            currentQuery = query;
            loadSocials(1, query, true); // Reset to true for search to clear previous results
        }
    });


    async function deleteSocial(uuid) {
        if (!confirm('Are you sure you want to delete this social?')) {
            return;
        }

        try {
            const appSelect = document.getElementById('app-select');
            const app = appSelect ? appSelect.value : 'kong_ai';

            const response = await fetch(`/socials/${uuid}?app_name=${app}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.status === 'success') {
                showNotification('Social deleted successfully', 'success');
                loadSocials(1, currentQuery, true);
            } else {
                showNotification('Failed to delete social', 'error');
            }
        } catch (error) {
            console.error('Error deleting social:', error);
            showNotification('Error deleting social', 'error');
        }
    }

    // Toggle sort functionality
    function toggleSort(field) {
        if (currentSortBy === field) {
            // Toggle sort order if same field
            currentSortOrder = currentSortOrder === 'desc' ? 'asc' : 'desc';
        } else {
            // Set new field and default to desc
            currentSortBy = field;
            currentSortOrder = 'desc';
        }

        updateSortArrow(field);
        loadSocials(1, currentQuery, true);
    }

    // Update sort arrow direction
    function updateSortArrow(field) {
        const arrow = document.getElementById(`sort-arrow-${field}`);
        if (arrow) {
            if (currentSortOrder === 'desc') {
                arrow.style.transform = 'rotate(0deg)'; // Down arrow
            } else {
                arrow.style.transform = 'rotate(180deg)'; // Up arrow
            }
        }
    }

    // App selection change handler
    document.getElementById('app-select').addEventListener('change', function () {
        onAppChange();
        loadSocials(1, currentQuery, true); // Reset to true when changing apps
    });
</script>