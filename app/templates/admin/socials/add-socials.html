<!-- Add Social Modal -->
<div id="add-social-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div id="add-social-modal-content"
            class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Add New Social Content</h3>
                    <button onclick="closeAddSocialModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Form -->
                <form id="social-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Primary Keywords -->
                        <div>
                            <label for="primary-keyword"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Primary Keywords <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="primary-keyword" name="primary_keyword" required
                                placeholder="AI chatbot, automation, etc."
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <p class="text-xs text-gray-500 mt-1">Comma-separated keywords</p>
                        </div>

                        <!-- Secondary Keywords -->
                        <div>
                            <label for="secondary-keyword"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Secondary Keywords <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="secondary-keyword" name="secondary_keyword" required
                                placeholder="machine learning, customer service, etc."
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <p class="text-xs text-gray-500 mt-1">Comma-separated keywords</p>
                        </div>

                        <!-- Social Type -->
                        <div>
                            <label for="social-type"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Social Platform <span class="text-red-500">*</span>
                            </label>
                            <select id="social-type" name="content_type" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">Select Platform</option>
                                <option value="twitter">Twitter</option>
                                <option value="linkedin">LinkedIn</option>
                                <option value="medium">Medium</option>
                                <option value="instagram">Instagram</option>
                                <option value="meta">Meta (Facebook)</option>
                            </select>
                        </div>

                        <!-- Count Words -->
                        <div>
                            <label for="count-words"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Word Count
                            </label>
                            <input type="number" id="count-words" name="count_words" min="0" max="1000" value="100"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <p class="text-xs text-gray-500 mt-1">Target word count (0 for auto)</p>
                        </div>

                        <!-- Count to Create -->
                        <div>
                            <label for="count-to-create"
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Number of Posts <span class="text-red-500">*</span>
                            </label>
                            <input type="number" id="count-to-create" name="count_to_create" min="1" max="5" value="3"
                                required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <p class="text-xs text-gray-500 mt-1">How many posts to generate (1-5)</p>
                        </div>

                        <!-- URL -->
                        <div>
                            <label for="url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                URL (Optional)
                            </label>
                            <input type="url" id="url" name="url" placeholder="https://example.com"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <p class="text-xs text-gray-500 mt-1">URL to include in the content</p>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div>
                        <label for="instructions"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Instructions (Optional)
                        </label>
                        <textarea id="instructions" name="instructions" rows="4"
                            placeholder="Provide specific instructions for content generation..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"></textarea>
                    </div>

                    <!-- URL Content Description -->
                    <div>
                        <label for="url-content"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            URL Content Description (Optional)
                        </label>
                        <textarea id="url-content" name="url_content" rows="3"
                            placeholder="Describe what the URL contains..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"></textarea>
                    </div>

                    <!-- Image URL (Optional) -->
                    <div>
                        <label for="image-url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Image URL (Optional)
                        </label>
                        <div class="flex space-x-2">
                            <input type="url" id="image-url" name="image_url"
                                placeholder="https://example.com/image.jpg"
                                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <button type="button" id="ai-image-btn" onclick="generateAIImageForSocial(event)"
                                class="px-3 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors duration-200 flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>AI Image</span>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Enter URL or generate AI image based on keywords</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-600">
                        <button type="button" onclick="closeAddSocialModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500">
                            Cancel
                        </button>
                        <div class="flex space-x-3">
                            <button type="button" id="generate-ai-btn" onclick="generateAIContent(event)"
                                class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors duration-200 flex items-center space-x-2">
                                <span id="generate-ai-text">Generate AI Content</span>
                                <svg id="generate-ai-spinner" class="hidden animate-spin w-4 h-4" fill="none"
                                    viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                            </button>
                            <button type="submit" id="save-social-btn" disabled
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors duration-200">
                                Save Social
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Generated Content Section -->
                <div id="generated-content-section"
                    class="hidden mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Generated Social Posts</h4>
                    <div id="generated-posts-container" class="space-y-4">
                        <!-- Generated posts will be displayed here -->
                    </div>
                </div>

                <!-- Loading State -->
                <div id="ai-loading" class="hidden mt-6 text-center">
                    <div class="inline-flex items-center">
                        <div
                            class="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin mr-2">
                        </div>
                        <span class="text-gray-600 dark:text-gray-400">Generating AI content...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let generatedSocialData = null;

    // Open modal
    function openAddSocialModal() {
        document.getElementById('add-social-modal').classList.remove('hidden');
        resetForm();
    }

    // Close modal
    function closeAddSocialModal() {
        document.getElementById('add-social-modal').classList.add('hidden');
        resetForm();
    }

    // Reset form
    function resetForm() {
        document.getElementById('social-form').reset();
        document.getElementById('generated-content-section').classList.add('hidden');
        document.getElementById('save-social-btn').disabled = true;

        // Reset generate button state
        const generateBtn = document.getElementById('generate-ai-btn');
        generateBtn.disabled = false;
        generateBtn.innerHTML = `
            <span>Generate AI Content</span>
            <svg class="hidden animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        `;

        generatedSocialData = null;
    }

    // Generate AI Content
    async function generateAIContent(event) {
        // Prevent any default behavior and stop propagation
        event.preventDefault();
        event.stopPropagation();

        const generateBtn = document.getElementById('generate-ai-btn');
        const aiLoading = document.getElementById('ai-loading');
        const originalHTML = generateBtn.innerHTML;

        try {
            // Validate required fields
            const form = document.getElementById('social-form');
            const formData = new FormData(form);

            const requiredFields = ['primary_keyword', 'secondary_keyword', 'content_type'];
            for (const field of requiredFields) {
                if (!formData.get(field)) {
                    showNotification(`Please fill in the ${field.replace('_', ' ')} field`, 'warning');
                    return;
                }
            }

            // Get selected app
            const appSelect = document.getElementById('app-select');
            const appKey = appSelect ? appSelect.value : 'kong_ai';

            if (!appKey) {
                showNotification('Please select an app first', 'warning');
                return;
            }

            // Show loading state
            generateBtn.disabled = true;
            generateBtn.innerHTML = `
                <span>Generating...</span>
                <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            `;
            aiLoading.classList.remove('hidden');

            // Prepare request data
            const requestData = {
                app_key: appKey,
                primary_keyword: formData.get('primary_keyword'),
                secondary_keyword: formData.get('secondary_keyword'),
                instructions: formData.get('instructions') || '',
                content_type: formData.get('content_type'),
                count_words: parseInt(formData.get('count_words')) || 0,
                count_to_create: parseInt(formData.get('count_to_create')) || 3,
                seed_token: new Date().toISOString().split('T')[0], // Use current date as seed
                url: formData.get('url') || '',
                url_content: formData.get('url_content') || ''
            };

            // Call AI API
            const response = await fetch('/generate-social-content', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.success && result.data) {
                generatedSocialData = result.data;
                displayGeneratedContent(result.data);
                document.getElementById('save-social-btn').disabled = false;
                showNotification('AI content generated successfully!', 'success');
            } else {
                showNotification('Failed to generate AI content. Please try again.', 'error');
            }

        } catch (error) {
            console.error('Error generating AI content:', error);
            showNotification('Error generating AI content. Please try again.', 'error');
        } finally {
            // Always restore button state and hide loading
            aiLoading.classList.add('hidden');
            generateBtn.disabled = false;
            generateBtn.innerHTML = originalHTML;
        }
    }

    // Display generated content
    function displayGeneratedContent(data) {
        const container = document.getElementById('generated-posts-container');
        const section = document.getElementById('generated-content-section');

        if (!data.social_posts || !Array.isArray(data.social_posts)) {
            showNotification('Invalid AI response format', 'error');
            return;
        }

        container.innerHTML = '';

        data.social_posts.forEach((post, index) => {
            const postElement = document.createElement('div');
            postElement.className = 'bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600';

            const platformColor = getPlatformColorClasses(post.platform);

            postElement.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${platformColor}">
                        ${post.platform.charAt(0).toUpperCase() + post.platform.slice(1)}
                    </span>
                </div>
                <span class="text-xs text-gray-500">Post ${index + 1}</span>
            </div>
            <div class="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap">${post.text}</div>
        `;

            container.appendChild(postElement);
        });

        section.classList.remove('hidden');
    }

    // Save social content
    document.getElementById('social-form').addEventListener('submit', async function (e) {
        e.preventDefault();

        if (!generatedSocialData) {
            showNotification('Please generate AI content first', 'warning');
            return;
        }

        try {
            const formData = new FormData(this);
            const appSelect = document.getElementById('app-select');
            const appKey = appSelect ? appSelect.value : 'kong_ai';

            // Prepare social data
            const socialData = {
                app_name: appKey,
                primary_keyword: formData.get('primary_keyword'),
                secondary_keyword: formData.get('secondary_keyword'),
                instructions: formData.get('instructions') || '',
                content_type: formData.get('content_type'),
                count_words: parseInt(formData.get('count_words')) || 0,
                count_to_create: parseInt(formData.get('count_to_create')) || 3,
                url: formData.get('url') || '',
                url_content: formData.get('url_content') || '',
                image_url: formData.get('image_url') || '',
                social_posts: generatedSocialData.social_posts,
                meta_title: `${formData.get('content_type')} content for ${formData.get('primary_keyword')}`
            };

            // Save to database
            const response = await fetch('/socials', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    data: socialData,
                    dbName: appKey
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                showNotification('Social content saved successfully!', 'success');
                closeAddSocialModal();
                // Reload socials list with proper sorting
                if (typeof loadSocials === 'function') {
                    loadSocials(1, currentQuery, true);
                }
            } else {
                showNotification('Failed to save social content', 'error');
            }

        } catch (error) {
            console.error('Error saving social:', error);
            showNotification('Error saving social content', 'error');
        }
    });

    // Close modal when clicking outside
    document.getElementById('add-social-modal').addEventListener('click', function (e) {
        // Check if the clicked element is inside the modal content
        if (!e.target.closest('#add-social-modal-content')) {
            closeAddSocialModal();
        }
    });

    // Close modal when pressing ESC key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' || e.keyCode === 27) {
            const modal = document.getElementById('add-social-modal');
            if (!modal.classList.contains('hidden')) {
                closeAddSocialModal();
            }
        }
    });

    // Generate AI Image for Social Content
    async function generateAIImageForSocial(event) {
        console.log('Generating AI image for social content...');

        // Prevent any default behavior and stop propagation
        event.preventDefault();
        event.stopPropagation();

        // Show loading state
        const button = event.currentTarget;
        const originalHTML = button.innerHTML;
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Generating...</span>
        `;

        try {
            // Get form data to create context for image generation
            const primaryKeyword = document.getElementById('primary-keyword').value.trim();
            const secondaryKeyword = document.getElementById('secondary-keyword').value.trim();
            const contentType = document.getElementById('social-type').value;
            const instructions = document.getElementById('instructions').value.trim();

            if (!primaryKeyword || !secondaryKeyword) {
                showNotification('Primary and Secondary keywords are required for generating image', 'warning');
                // Restore button state immediately for validation errors
                button.disabled = false;
                button.innerHTML = originalHTML;
                return;
            }

            if (!contentType) {
                showNotification('Please select a social platform first', 'warning');
                // Restore button state immediately for validation errors
                button.disabled = false;
                button.innerHTML = originalHTML;
                return;
            }

            // Get selected app
            const appSelect = document.getElementById('app-select');
            const appKey = appSelect ? appSelect.value : '';

            if (!appKey) {
                showNotification('Please select an app first', 'warning');
                // Restore button state immediately for validation errors
                button.disabled = false;
                button.innerHTML = originalHTML;
                return;
            }

            // Create title and description for image generation
            const title = `${contentType} content for ${primaryKeyword}`;
            const description = `Social media content about ${primaryKeyword} and ${secondaryKeyword} for ${contentType} platform. ${instructions}`.trim();

            // Call the AI image generation API
            const response = await fetch('/generate-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title,
                    content_description: description,
                    app_key: appKey,
                    folder: 'social-images'
                })
            });

            const result = await response.json();

            if (result.success && result.image_url) {
                // Set the generated image URL in the field
                const imageField = document.getElementById('image-url');
                if (imageField) {
                    imageField.value = result.image_url;
                }
                showNotification('AI image generated successfully!', 'success');
                console.log(`AI image generated: ${result.image_url}`);
            } else {
                throw new Error(result.error || 'Failed to generate image');
            }

        } catch (error) {
            console.error('Error generating AI image:', error);
            showNotification('Failed to generate AI image. Please try again.', 'error');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalHTML;
        }
    }
</script>