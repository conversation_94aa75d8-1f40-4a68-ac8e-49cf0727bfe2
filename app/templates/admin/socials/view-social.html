<!-- View Social Modal -->
<div id="view-social-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div id="view-social-modal-content"
            class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="view-modal-title">Social Content
                    </h3>
                    <button onclick="closeViewSocialModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Social Details -->
                <div id="social-details" class="mb-6">
                    <!-- Details will be populated by JavaScript -->
                </div>

                <!-- Social Posts -->
                <div id="social-posts-container">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Generated Posts</h4>
                    <div id="view-posts-list" class="space-y-4">
                        <!-- Posts will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Loading State -->
                <div id="view-loading" class="hidden text-center py-8">
                    <div class="inline-flex items-center">
                        <div
                            class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2">
                        </div>
                        <span class="text-gray-600 dark:text-gray-400">Loading social content...</span>
                    </div>
                </div>

                <!-- Error State -->
                <div id="view-error" class="hidden text-center py-8">
                    <div class="text-red-500 dark:text-red-400">
                        <svg class="mx-auto h-12 w-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                            </path>
                        </svg>
                        <p class="text-sm">Failed to load social content</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // View Social Functions

    // Open view modal and load social data
    async function viewSocial(uuid) {
        try {
            // Open modal and show loading
            document.getElementById('view-social-modal').classList.remove('hidden');
            document.getElementById('view-loading').classList.remove('hidden');
            document.getElementById('social-details').classList.add('hidden');
            document.getElementById('social-posts-container').classList.add('hidden');
            document.getElementById('view-error').classList.add('hidden');

            const appSelect = document.getElementById('app-select');
            const app = appSelect ? appSelect.value : 'kong_ai';

            const response = await fetch(`/socials/${uuid}?app_name=${app}`);
            const result = await response.json();

            // Hide loading
            document.getElementById('view-loading').classList.add('hidden');

            if (result.status === 'success' && result.data) {
                displaySocialDetails(result.data);
            } else {
                document.getElementById('view-error').classList.remove('hidden');
                showNotification('Failed to load social content', 'error');
            }

        } catch (error) {
            console.error('Error loading social:', error);
            document.getElementById('view-loading').classList.add('hidden');
            document.getElementById('view-error').classList.remove('hidden');
            showNotification('Error loading social content', 'error');
        }
    }

    // Display social details in modal
    function displaySocialDetails(social) {
        // Update modal title
        const title = social.meta_title || `${social.content_type} content` || 'Social Content';
        document.getElementById('view-modal-title').textContent = title;

        // Display social details
        const detailsContainer = document.getElementById('social-details');
        detailsContainer.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Primary Keywords</label>
                <p class="text-sm text-gray-900 dark:text-gray-100">${social.primary_keyword || 'N/A'}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Secondary Keywords</label>
                <p class="text-sm text-gray-900 dark:text-gray-100">${social.secondary_keyword || 'N/A'}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Platform</label>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlatformColorClasses(social.platform || social.content_type || 'N/A')}">
                    ${(social.platform || social.content_type || 'N/A').charAt(0).toUpperCase() + (social.platform || social.content_type || 'N/A').slice(1)}
                </span>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Created Date</label>
                <p class="text-sm text-gray-900 dark:text-gray-100">${social.created_date ? new Date(social.created_date).toLocaleDateString() : 'N/A'}</p>
            </div>
            ${social.url ? `
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">URL</label>
                    <a href="${social.url}" target="_blank" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">${social.url}</a>
                </div>
            ` : ''}
            ${social.instructions ? `
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Instructions</label>
                    <p class="text-sm text-gray-900 dark:text-gray-100">${social.instructions}</p>
                </div>
            ` : ''}
            ${social.url_content ? `
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">URL Content Description</label>
                    <p class="text-sm text-gray-900 dark:text-gray-100">${social.url_content}</p>
                </div>
            ` : ''}
            ${social.image_url ? `
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Image URL</label>
                    <a href="${social.image_url}" target="_blank" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">${social.image_url}</a>
                </div>
            ` : ''}
        </div>
    `;

        // Display social posts
        if (social.social_posts && Array.isArray(social.social_posts) && social.social_posts.length > 0) {
            displaySocialPosts(social.social_posts);
            document.getElementById('social-posts-container').classList.remove('hidden');
        } else {
            document.getElementById('view-posts-list').innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <p>No social posts found for this content.</p>
            </div>
        `;
            document.getElementById('social-posts-container').classList.remove('hidden');
        }

        document.getElementById('social-details').classList.remove('hidden');
    }

    // Display social posts with copy buttons
    function displaySocialPosts(posts) {
        const container = document.getElementById('view-posts-list');

        // Get platform from the social document (stored at document level)
        const socialDetailsContainer = document.getElementById('social-details');
        const platformElement = socialDetailsContainer.querySelector('span[class*="rounded-full"]');
        const platform = platformElement ? platformElement.textContent.toLowerCase() : 'N/A';
        const platformColor = getPlatformColorClasses(platform);

        container.innerHTML = posts.map((post, index) => {
            const postId = `post-${index}`;

            return `
            <div class="bg-white dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600 shadow-sm">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${platformColor}">
                            ${platform.charAt(0).toUpperCase() + platform.slice(1)}
                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">Post ${index + 1}</span>
                    </div>
                    <button onclick="copyPostText('${postId}')" 
                            class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"
                            title="Copy to clipboard">
                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy
                    </button>
                </div>
                <div id="${postId}" class="text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap leading-relaxed p-4 bg-gray-50 dark:bg-gray-800 rounded-md border">${post.text}</div>
            </div>
        `;
        }).join('');
    }

    // Copy post text to clipboard
    async function copyPostText(postId) {
        try {
            const postElement = document.getElementById(postId);
            const text = postElement.textContent;

            await navigator.clipboard.writeText(text);
            showNotification('Post copied to clipboard!', 'success');
        } catch (error) {
            console.error('Failed to copy text:', error);
            // Fallback for older browsers
            try {
                const postElement = document.getElementById(postId);
                const textArea = document.createElement('textarea');
                textArea.value = postElement.textContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('Post copied to clipboard!', 'success');
            } catch (fallbackError) {
                showNotification('Failed to copy text to clipboard', 'error');
            }
        }
    }

    // Close view modal
    function closeViewSocialModal() {
        document.getElementById('view-social-modal').classList.add('hidden');
    }

    // Initialize view modal event listeners
    document.addEventListener('DOMContentLoaded', function () {
        // Close modal when clicking outside
        const viewModal = document.getElementById('view-social-modal');
        if (viewModal) {
            viewModal.addEventListener('click', function (e) {
                if (!e.target.closest("#view-social-modal-content")) {
                    closeViewSocialModal();
                }
            });
        }
    });

    // Close modal when pressing ESC key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' || e.keyCode === 27) {
            const modal = document.getElementById('view-social-modal');
            if (!modal.classList.contains('hidden')) {
                closeViewSocialModal();
            }
        }
    });
</script>