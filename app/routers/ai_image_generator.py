from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.util.ai_image import generate_and_upload_content_image
from app.util.settings import app_settings

router = APIRouter()


class ImageGenerationRequest(BaseModel):
    title: str = Field(..., description="Title for the image generation")
    content_description: str = Field(..., description="Content description for the image")
    app_key: str = Field(..., description="Application key")
    folder: str = Field(default="images", description="S3 folder to store the image")


class ImageGenerationResponse(BaseModel):
    success: bool
    image_url: Optional[str] = None
    error: Optional[str] = None


@router.post("/generate-image", response_model=ImageGenerationResponse)
async def generate_image(request: ImageGenerationRequest):
    """
    Generate AI image based on the provided title and content.
    """
    try:
        # Get app details from settings
        app_info = app_settings.APP_DESCRIPTIONS.get(request.app_key)
        if not app_info:
            raise HTTPException(status_code=400, detail=f"Invalid app_key: {request.app_key}")

        app_name = app_info["name"]

        # Call the image generation function
        s3_image_url, success = await generate_and_upload_content_image(title=request.title, content=request.content_description, app_name=app_name, folder=request.folder)

        if success and s3_image_url:
            return ImageGenerationResponse(success=True, image_url=s3_image_url)
        else:
            return ImageGenerationResponse(success=False, error="Failed to generate or upload image")

    except Exception as e:
        error_msg = f"Image generation failed: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
