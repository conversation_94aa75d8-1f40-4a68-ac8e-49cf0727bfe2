from datetime import datetime, timezone
from typing import Optional

import requests
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.util.ai_content import call_ai_lambda
from app.util.settings import app_settings

router = APIRouter()

APP_DEBUG = app_settings.APP_DEBUG


class SocialContentRequest(BaseModel):
    app_key: str = Field(..., description="Application name")
    primary_keyword: str = Field(..., description="Primary keyword for content generation")
    secondary_keyword: str = Field(..., description="Secondary keyword for content generation")
    instructions: str = Field(..., description="Instructions for content generation")
    content_type: str = Field(..., description="Type of social content (e.g., 'twitter', 'medium', 'linkedin', 'instagram', 'meta')")
    count_words: int = Field(default=0, description="Word count for the content")
    count_to_create: int = Field(default=3, description="Number of content pieces to create")
    seed_token: str = Field(..., description="Seed token for content generation")
    url: str = Field(..., description="URL to include in the content")
    url_content: str = Field(..., description="URL content description")


class SocialContentResponse(BaseModel):
    success: bool
    data: Optional[dict] = None
    error: Optional[str] = None


@router.post("/generate-social-content", response_model=SocialContentResponse)
async def generate_social_content(request: SocialContentRequest):
    """
    Generate social media content using AI based on the provided parameters.
    """
    try:
        # Get app details from settings
        app_info = app_settings.APP_DESCRIPTIONS.get(request.app_key)
        if not app_info:
            raise HTTPException(status_code=400, detail=f"Invalid app_key: {request.app_key}")

        app_name = app_info["name"]
        app_description = app_info["description"]

        # Prepare seed value
        seed = datetime.now(timezone.utc).strftime("%Y-%m-%d")  # Only year-month-day
        # Construct the payload
        payload = {
            "input_dict": {
                "app_name": app_name,
                "app_description": app_description,
                "primary_keyword": request.primary_keyword,
                "secondary_keyword": request.secondary_keyword,
                "instructions": request.instructions,
                "content_type": request.content_type,
                "count_words": request.count_words,
                "count_to_create": request.count_to_create,
                "seed_token": None if APP_DEBUG else seed,
                "url": request.url,
                "url_content": request.url_content,
            }
        }

        # Call the AI Lambda with the SOCIAL_CONTENT_API_URL
        result = call_ai_lambda(payload, api_url=app_settings.SOCIAL_CONTENT_API_URL)

        return SocialContentResponse(success=True, data=result)

    except requests.exceptions.RequestException as e:
        error_msg = f"API request failed: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=503, detail=error_msg)

    except Exception as e:
        error_msg = f"Social content generation failed: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
