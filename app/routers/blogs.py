import time
from datetime import datetime, timezone
from typing import List

from fastapi import APIRouter, FastAPI, HTTPException
from pydantic import BaseModel
from pymongo import MongoClient

from app.util.common import (
    add_metadata_fields,
    build_search_filter,
    clean_and_validate_uuids,
    clean_description,
    convert_markdown_to_html,
    enrich_with_author,
    handle_publish_date,
    serialize_document,
    validate_pagination,
)
from app.util.db.database import BlogDB, safe_document
from app.util.settings import app_settings

app = FastAPI()
router = APIRouter()

# MongoDB connection using settings.py
client = MongoClient(app_settings.BLOG_MONGO_DB_CONNECTION)


# Pydantic model for request validation
class BlogData(BaseModel):
    data: dict
    dbName: str


def get_all_blogs(
    app_name: str,
    project_name: str = "",
    limit: int = 300,
    skip: int = 0,
    publish: bool = False,
) -> List[dict]:
    """Get all blogs with optional filtering"""
    collection = BlogDB.get_collection(app_name, "blogs")

    # Construct the filter
    query_filter = {}
    if project_name:
        query_filter["project_name"] = project_name
    if publish:
        query_filter["status"] = "publish"

    # Projection (optional): only return fields you need to reduce payload size
    projection = {"_id": 0}

    # Fetch blogs with skip/limit/sort and use projection
    start = time.time()
    cursor = collection.find(query_filter, projection).skip(skip).limit(limit).sort([("is_featured", -1), ("publish_date", -1)])
    print("Mongo fetch time:", time.time() - start)

    blogs = [safe_document(blog) for blog in cursor]

    # Enrich blogs with author information
    blogs = enrich_with_author(blogs, app_name)

    return blogs


@router.post("/blogs")
async def save_blog(blog: BlogData):
    """Save a new blog to the database"""
    try:
        # Dynamically select the database based on dbName
        db = BlogDB.get_database(blog.dbName)
        collection = db.get_collection("blogs")

        blog_data = blog.data.copy()

        # Add metadata fields
        add_metadata_fields(blog_data)

        # Handle publish_date safely
        handle_publish_date(blog_data)

        collection.insert_one(blog_data)
        return {"status": "success", "message": "Blog saved successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving blog: {e}")


@router.get("/blogs")
async def get_blogs(app_name: str, project_name: str = "", query: str = "", page: int = 1, limit: int = 100):
    """
    Fetch blogs from MongoDB using a page-based pagination system.
    """
    try:
        skip, limit = validate_pagination(page, limit)

        db = BlogDB.get_database(app_name)
        collection = db.get_collection("blogs")

        # Build search filter
        base_filter = {}
        if project_name:
            base_filter["project_name"] = project_name

        search_filter = build_search_filter(base_filter, query, "meta_title")

        # Fetch blogs with pagination and sorting
        blogs = list(collection.find(search_filter).skip(skip).limit(limit).sort([("is_featured", -1), ("publish_date", -1)]))

        # Enrich blogs with author information
        blogs = enrich_with_author(blogs, app_name)

        return {"status": "success", "data": [serialize_document(blog) for blog in blogs], "page": page}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching blogs: {e}")


@router.get("/blogs/publish")
async def get_blogs_publish(app_name: str, project_name: str = "", limit: int = 300, skip: int = 0):
    """Get published blogs with related and featured blogs"""
    try:
        blogs = get_all_blogs(app_name, project_name, limit, skip, publish=True)

        # Get database collections
        db = BlogDB.get_database(app_name)
        collection = db.get_collection("blogs")

        # Collect all related blog UUIDs
        all_related_uuids = set()
        for blog in blogs:
            related_uuids = clean_and_validate_uuids(blog.get("related_blogs", []))
            all_related_uuids.update(related_uuids)

        # Get all related blogs in one query
        related_blogs_data = {}
        if all_related_uuids:
            related_results = list(collection.find({"uuid": {"$in": list(all_related_uuids)}, "status": "publish"}))

            for blog_data in related_results:
                uuid = blog_data.get("uuid")
                if uuid:
                    related_blogs_data[uuid] = {
                        "title": blog_data.get("meta_title", ""),
                        "slug": blog_data.get("slug", ""),
                        "description": clean_description(blog_data.get("meta_description", "")),
                        "image_url": blog_data.get("image1_url", ""),
                    }

        # Get featured blogs once
        featured_blogs_results = list(collection.find({"is_featured": True, "status": "publish"}).limit(20))

        featured_blogs_lookup = {}
        for f in featured_blogs_results:
            uuid = f.get("uuid")
            if uuid:
                featured_blogs_lookup[uuid] = {
                    "title": f.get("meta_title", ""),
                    "slug": f.get("slug", ""),
                    "description": clean_description(f.get("meta_description", "")),
                    "image_url": f.get("image1_url", ""),
                }

        # Enrich each blog
        for blog in blogs:
            # Add related blogs
            blog_related_list = []
            related_uuids = clean_and_validate_uuids(blog.get("related_blogs", []))

            for uuid in related_uuids:
                if uuid in related_blogs_data:
                    blog_related_list.append(related_blogs_data[uuid])

            blog["related_blogs"] = blog_related_list

            # Add featured blogs (exclude current blog)
            current_uuid = blog.get("uuid", "")
            blog_featured_list = []
            for uuid, featured_blog in featured_blogs_lookup.items():
                if uuid != current_uuid:
                    blog_featured_list.append(featured_blog)
                if len(blog_featured_list) >= 6:  # Limit to 6
                    break

            blog["featured_blogs"] = blog_featured_list

        return {"status": "success", "data": blogs}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching blogs: {str(e)}")


@router.put("/blogs")
async def update_blog(blog: BlogData):
    """
    Update an existing blog in the database based on the provided uuid.
    """
    try:
        db = BlogDB.get_database(blog.dbName)
        collection = db.get_collection("blogs")

        blog_data = blog.data.copy()

        # Ensure uuid is provided for update
        if "uuid" not in blog_data:
            raise HTTPException(status_code=400, detail="Missing 'uuid' in blog data for update.")

        # Handle publish_date safely
        handle_publish_date(blog_data)

        # Update the blog with the provided uuid
        result = collection.update_one({"uuid": blog_data["uuid"]}, {"$set": blog_data})

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Blog with the specified 'uuid' not found.")

        return {"status": "success", "message": "Blog updated successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating blog: {e}")


@router.get("/{slug}")
async def get_blog(slug: str, app_name: str, project_name: str = "", limit: int = 300, skip: int = 0):
    """Fetch blog from MongoDB by slug"""
    try:
        db = BlogDB.get_database(app_name)
        collection = db.get_collection("blogs")

        project_filter = {"project_name": project_name} if project_name else {}

        # Get main blog
        blogs = list(collection.find({"slug": slug, **project_filter}))

        if not blogs:
            raise HTTPException(status_code=404, detail="Blog not found")

        blog = serialize_document(blogs[0])

        # Convert markdown
        markdown_text = blog.get("blog_description", "")
        blog["blog_description"] = convert_markdown_to_html(markdown_text, "blog_description")

        # Enrich with author information
        blog = enrich_with_author(blog, app_name)

        # Get related blogs
        related_blogs = []
        related_uuids = clean_and_validate_uuids(blog.get("related_blogs", []))

        if related_uuids:
            related_data = list(collection.find({"uuid": {"$in": related_uuids}, "status": "publish"}))

            for r in related_data:
                related_blogs.append(
                    {"title": r.get("meta_title", ""), "slug": r.get("slug", ""), "description": clean_description(r.get("meta_description", "")), "image_url": r.get("image1_url", "")}
                )

        # Get featured blogs
        featured_blogs = []
        current_uuid = blog.get("uuid", "")
        featured_data = list(collection.find({"is_featured": True, "status": "publish", "uuid": {"$ne": current_uuid}}).limit(6))

        for f in featured_data:
            featured_blogs.append({"title": f.get("meta_title", ""), "slug": f.get("slug", ""), "description": clean_description(f.get("meta_description", "")), "image_url": f.get("image1_url", "")})

        blog["related_blogs"] = related_blogs
        blog["featured_blogs"] = featured_blogs

        return {"status": "success", "data": blog}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
