from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.util.common import (
    add_metadata_fields,
    build_search_filter,
    convert_markdown_to_html,
    enrich_with_author,
    get_document_by_id_or_uuid,
    handle_publish_date,
    validate_pagination,
)
from app.util.db.database import BlogDB, safe_document

router = APIRouter()


# Pydantic model for request validation
class ResourceData(BaseModel):
    data: dict
    dbName: str


def validate_resource_type_and_pillar(resource_data: dict) -> None:
    """Validate resource type and pillar_id relationship"""
    if resource_data.get("type") not in ["pillar", "subpage"]:
        raise HTTPException(status_code=400, detail="Resource type must be 'pillar' or 'subpage'")

    if resource_data.get("type") == "subpage" and not resource_data.get("pillar_id"):
        raise HTTPException(status_code=400, detail="Subpages must have a pillar_id")


@router.post("/resources")
async def save_resource(resource: ResourceData):
    """Save a new resource to the database"""
    try:
        collection = BlogDB.get_collection(resource.dbName, "resources")
        resource_data = resource.data.copy()

        # Add metadata fields
        add_metadata_fields(resource_data)

        # Handle publish_date safely
        handle_publish_date(resource_data)

        # Validate resource type and pillar relationship
        validate_resource_type_and_pillar(resource_data)

        collection.insert_one(resource_data)
        return {"status": "success", "message": "Resource saved successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving resource: {e}")


@router.put("/resources")
async def update_resource(resource: ResourceData):
    """Update an existing resource in the database"""
    try:
        collection = BlogDB.get_collection(resource.dbName, "resources")
        resource_data = resource.data.copy()

        if "uuid" not in resource_data:
            raise HTTPException(status_code=400, detail="Missing 'uuid' in resource data for update.")

        # Handle publish_date safely
        handle_publish_date(resource_data)

        # Validate resource type and pillar relationship
        validate_resource_type_and_pillar(resource_data)

        result = collection.update_one({"uuid": resource_data["uuid"]}, {"$set": resource_data})

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Resource with the specified 'uuid' not found.")

        return {"status": "success", "message": "Resource updated successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating resource: {e}")


@router.get("/resources/pillars")
async def get_pillar_pages(app_name: str, published: bool = False, query: str = "", page: int = 1, limit: int = 100):
    """Get pillar pages with optional filtering"""
    try:
        skip, limit = validate_pagination(page, limit)

        collection = BlogDB.get_collection(app_name, "resources")

        # Build search filter
        base_filter = {"type": "pillar"}
        if published:
            base_filter["status"] = "publish"

        search_filter = build_search_filter(base_filter, query, "meta_title")

        pillars = list(collection.find(search_filter).skip(skip).limit(limit).sort([("created_date", -1)]))
        pillars = enrich_with_author(pillars, app_name)

        return {"status": "success", "data": [safe_document(pillar) for pillar in pillars], "page": page}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching pillar pages: {e}")


@router.get("/resources/subpages")
async def get_subpages(app_name: str, pillar_id: str, published: bool = False, query: str = "", page: int = 1, limit: int = 100):
    """Get subpages for a specific pillar"""
    try:
        skip, limit = validate_pagination(page, limit)

        collection = BlogDB.get_collection(app_name, "resources")

        # Handle both ObjectId and UUID for pillar_id
        is_uuid = len(pillar_id.split("-")) > 1
        if not is_uuid:
            try:
                pillar = get_document_by_id_or_uuid(collection, pillar_id)
                pillar_id = pillar.get("uuid")
                if not pillar_id:
                    raise HTTPException(status_code=404, detail="Pillar UUID not found")
            except HTTPException:
                raise HTTPException(status_code=404, detail="Pillar not found")

        # Build search filter
        base_filter = {"type": "subpage", "pillar_id": pillar_id}
        if published:
            base_filter["status"] = "publish"

        search_filter = build_search_filter(base_filter, query, "meta_title")

        subpages = list(collection.find(search_filter).skip(skip).limit(limit).sort([("created_date", -1)]))
        subpages = enrich_with_author(subpages, app_name)

        return {"status": "success", "data": [safe_document(subpage) for subpage in subpages], "page": page}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching subpages: {e}")


@router.get("/resources/{resource_id}")
async def get_resource(resource_id: str, app_name: str):
    """Get a specific resource by ID or UUID"""
    try:
        collection = BlogDB.get_collection(app_name, "resources")
        resource = get_document_by_id_or_uuid(collection, resource_id)

        # Convert markdown
        markdown_text = resource.get("resource_description", "")
        resource["resource_description"] = convert_markdown_to_html(markdown_text, "resource_description")

        # Enrich with author information
        resource = enrich_with_author(resource, app_name)

        return {"status": "success", "data": safe_document(resource)}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching resource: {e}")


@router.delete("/resources/{resource_id}")
async def delete_resource(resource_id: str, app_name: str):
    """Delete a resource by UUID"""
    try:
        collection = BlogDB.get_collection(app_name, "resources")
        resource = collection.find_one({"uuid": resource_id})

        if not resource:
            raise HTTPException(status_code=404, detail="Resource not found")

        # Check if it's a pillar with existing subpages
        if resource.get("type") == "pillar":
            subpages_count = collection.count_documents({"type": "subpage", "pillar_id": resource_id})
            if subpages_count > 0:
                raise HTTPException(status_code=400, detail="Cannot delete pillar page with existing subpages. Delete subpages first.")

        result = collection.delete_one({"uuid": resource_id})

        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Resource not found")

        return {"status": "success", "message": "Resource deleted successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting resource: {e}")


@router.get("/resources")
async def get_all_resources(app_name: str, page: int = 1, limit: int = 100):
    """Get all resources with pagination"""
    try:
        skip, limit = validate_pagination(page, limit)

        collection = BlogDB.get_collection(app_name, "resources")
        resources = list(collection.find().skip(skip).limit(limit).sort([("created_date", -1)]))

        resources = enrich_with_author(resources, app_name)

        return {"status": "success", "data": [safe_document(resource) for resource in resources], "page": page}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching resources: {e}")
