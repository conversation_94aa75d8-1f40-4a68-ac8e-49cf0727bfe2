from typing import Optional
import time

import requests
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.util.settings import app_settings

router = APIRouter()

HUMBOTAI_API_KEY = app_settings.HUMBOTAI_API_KEY.get_secret_value()


class TextHumanizationRequest(BaseModel):
    text: str = Field(..., description="Text to be humanized (minimum 50 words required)")
    model_type: str = Field(default="Enhanced", description="Humanization mode: Quick, Enhanced, or Advanced")


class TextHumanizationResponse(BaseModel):
    success: bool
    humanized_text: Optional[str] = None
    task_id: Optional[str] = None
    detection_result: Optional[str] = None
    detection_score: Optional[int] = None
    words_used: Optional[int] = None
    error: Optional[str] = None


@router.post("/humanize-text", response_model=TextHumanizationResponse)
async def humanize_text(request: TextHumanizationRequest):
    """
    Humanize AI-generated text using Humbot API.
    This endpoint follows the two-step process: create task, then retrieve results.
    """
    try:
        # Validate input text length (minimum 50 words)
        word_count = len(request.text.split())
        if word_count < 50:
            raise HTTPException(status_code=400, detail=f"Text must contain at least 50 words. Current text has {word_count} words.")

        # Validate model_type
        valid_modes = ["Quick", "Enhanced", "Advanced"]
        if request.model_type not in valid_modes:
            raise HTTPException(status_code=400, detail=f"Invalid model_type. Must be one of: {', '.join(valid_modes)}")

        # Step 1: Create task
        create_url = "https://humbot.ai/api/humbot/v1/create"
        headers = {"Content-Type": "application/json", "api-key": HUMBOTAI_API_KEY}

        create_payload = {"input": request.text, "model_type": request.model_type}

        print(f"Creating Humbot task for text humanization")
        print(f"Text length: {len(request.text)} characters ({word_count} words)")
        print(f"Model type: {request.model_type}")

        # Create the task
        create_response = requests.post(create_url, headers=headers, json=create_payload, timeout=30)

        if create_response.status_code != 200:
            print(f"Create task failed - Status: {create_response.status_code}")
            print(f"Response: {create_response.text}")
            create_response.raise_for_status()

        create_result = create_response.json()

        # Check for API errors
        if create_result.get("error_code", 0) != 0:
            error_msg = create_result.get("error_msg", "Unknown error from Humbot API")
            raise HTTPException(status_code=400, detail=f"Humbot API error: {error_msg}")

        # Extract task_id
        task_id = create_result.get("data", {}).get("task_id")
        if not task_id:
            raise HTTPException(status_code=500, detail="Failed to get task_id from Humbot API")

        print(f"Task created successfully with ID: {task_id}")

        # Step 2: Poll for results
        retrieve_url = "https://humbot.ai/api/humbot/v1/retrieve"
        max_attempts = 15  # Maximum polling attempts
        poll_interval = 4  # Seconds between polls

        for attempt in range(max_attempts):
            print(f"Polling attempt {attempt + 1}/{max_attempts} for task {task_id}")

            retrieve_response = requests.get(retrieve_url, headers={"api-key": HUMBOTAI_API_KEY}, params={"task_id": task_id}, timeout=30)

            if retrieve_response.status_code != 200:
                print(f"Retrieve failed - Status: {retrieve_response.status_code}")
                print(f"Response: {retrieve_response.text}")
                retrieve_response.raise_for_status()

            retrieve_result = retrieve_response.json()

            # Check for API errors
            if retrieve_result.get("error_code", 0) != 0:
                error_msg = retrieve_result.get("error_msg", "Unknown error from Humbot API")
                raise HTTPException(status_code=400, detail=f"Humbot API error: {error_msg}")

            data = retrieve_result.get("data", {})
            task_status = data.get("task_status", False)
            subtask_status = data.get("subtask_status", "running")

            print(f"Task status: {task_status}, Subtask status: {subtask_status}")

            if task_status and subtask_status == "completed":
                # Task completed successfully
                return TextHumanizationResponse(
                    success=True,
                    humanized_text=data.get("output"),
                    task_id=task_id,
                    detection_result=data.get("detection_result"),
                    detection_score=data.get("detection_score"),
                    words_used=data.get("words_used"),
                )
            elif subtask_status == "failed":
                # Task failed
                raise HTTPException(status_code=500, detail="Humbot task failed during processing")

            # Task still running, wait before next poll
            if attempt < max_attempts - 1:  # Don't sleep on the last attempt
                time.sleep(poll_interval)

        # If we get here, polling timed out
        raise HTTPException(status_code=504, detail=f"Task {task_id} did not complete within the expected time. Please try again later.")

    except requests.exceptions.Timeout as e:
        error_msg = f"Humbot API request timed out: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=504, detail=error_msg)

    except requests.exceptions.HTTPError as e:
        error_msg = f"Humbot API HTTP error: {str(e)}"
        print(error_msg)
        if hasattr(e, "response") and e.response.status_code == 401:
            raise HTTPException(status_code=401, detail="Invalid API key for Humbot")
        elif hasattr(e, "response") and e.response.status_code == 429:
            raise HTTPException(status_code=429, detail="Rate limit exceeded for Humbot API")
        else:
            raise HTTPException(status_code=500, detail=error_msg)

    except requests.exceptions.ConnectionError as e:
        error_msg = f"Failed to connect to Humbot API: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=503, detail=error_msg)

    except Exception as e:
        error_msg = f"Text humanization failed: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
