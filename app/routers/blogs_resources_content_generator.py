from datetime import datetime, timezone
from typing import Optional

import requests
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.util.ai_content import call_ai_lambda
from app.util.settings import app_settings

router = APIRouter()

APP_DEBUG = app_settings.APP_DEBUG


class ContentGenerationRequest(BaseModel):
    app_key: str = Field(..., description="Application key (e.g., 'kong_ai', 'crm_io')")
    primary_keyword: str = Field(..., description="Primary keyword for content generation")
    secondary_keyword: str = Field(..., description="Secondary keyword for content generation")
    other_keywords: str = Field(..., description="Other keywords for content generation")
    type: str = Field(..., alias="type", description="Type of content to generate")
    word_count: int = Field(..., description="Target word count for the generated content")
    seed: Optional[str] = Field(None, description="Seed for content generation (optional)")


class ContentGenerationResponse(BaseModel):
    success: bool
    data: Optional[dict] = None
    error: Optional[str] = None


@router.post("/generate-content", response_model=ContentGenerationResponse)
async def generate_content(request: ContentGenerationRequest):
    """
    Generate AI content based on the provided parameters.
    """
    try:
        # Get app details from settings
        app_info = app_settings.APP_DESCRIPTIONS.get(request.app_key)
        if not app_info:
            raise HTTPException(status_code=400, detail=f"Invalid app_key: {request.app_key}")

        app_name = app_info["name"]
        app_description = app_info["description"]

        # Prepare seed value
        seed = datetime.now(timezone.utc).strftime("%Y-%m-%d")  # Only year-month-day

        # Construct the payload
        payload = {
            "input_dict": {
                "app_name": app_name,
                "app_description": app_description,
                "primary_keyword": request.primary_keyword,
                "secondary_keyword": request.secondary_keyword,
                "other_keywords": request.other_keywords,
                "type": request.type,
                "word_count": request.word_count or 1500,
                "seed": None if APP_DEBUG else seed,
            }
        }

        # Call the AI Lambda with the constructed payload
        result = call_ai_lambda(payload, api_url=app_settings.BLOGS_RESOURCES_CONTENT_API_URL)

        return ContentGenerationResponse(success=True, data=result)

    except requests.exceptions.RequestException as e:
        error_msg = f"API request failed: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=503, detail=error_msg)

    except Exception as e:
        error_msg = f"Content generation failed: {str(e)}"
        print(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)
