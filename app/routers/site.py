import httpx
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import PlainTextResponse
from fastapi.security import HTTPBasic
import markdown2
from pymongo import MongoClient
import json


# Import settings
from app.util.settings import app_settings
from app.util.templates import templates


# MongoDB connection using settings.py
client = MongoClient(app_settings.BLOG_MONGO_DB_CONNECTION)

router = APIRouter()
security = HTTPBasic()

# Dummy credentials for basic authentication
USERNAME = "kong"
PASSWORD = "kongrocks"

App_Data = {
    "App_Data": {
        "referHeading": "Refer to Businesses",
        "referSubHeading": "We built Kong.ai with love for growing businesses. Please refer us to your friends and help us grow.",
        "referButton": "Refer & Earn Credits",
    }
}


class MarkdownRequest(BaseModel):
    md_text: str


@router.post("/blog/md-convert")
async def convert_markdown_to_html(request: MarkdownRequest):
    html_output = markdown2.markdown(request.md_text)
    return {"html": html_output}


@router.get("/blog/{page_id}")
async def page(page_id):
    """
    Fetch page from MongoDB based on the selected page id.
    """
    try:
        # List all databases in the cluster
        databases = client.list_database_names()

        results = []

        for db_name in databases:
            db = client[db_name]
            if "blogs" in db.list_collection_names():
                collection = db["blogs"]
                record = collection.find_one({"uuid": page_id})
                if record:
                    results.append({"database": db_name, "record": record})
        return templates.TemplateResponse("page.html", {"request": {}, "result": json.loads(json.dumps(results[0], default=str))})
    except Exception as e:
        print(e)
        return templates.TemplateResponse("page.html", {"request": {}, "result": []})


@router.get("/")
async def home():
    return PlainTextResponse("")


@router.get("/robots.txt")
async def robots_txt():
    return PlainTextResponse("User-agent: *\nDisallow:")


@router.post("/detect-cors")
async def iframe_cors_detect(request: Request):
    data = await request.json()
    iframe_url = data.get("url")

    if not iframe_url:
        raise HTTPException(status_code=400, detail="URL is required")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(iframe_url)

        # Check for X-Frame-Options header
        x_frame_options = response.headers.get("x-frame-options")
        if x_frame_options:
            return {"status": "blocked", "reason": f"X-Frame-Options: {x_frame_options}"}

        # Check for CORS headers that might prevent iframe loading
        cors_headers = response.headers.get("access-control-allow-origin")
        if cors_headers:
            return {"status": "blocked", "reason": "CORS policy"}

        return {"status": "accessible", "reason": "No blocking headers found"}

    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")
