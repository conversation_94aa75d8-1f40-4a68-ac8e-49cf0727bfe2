import time
import uuid
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.util.db.database import BlogDB, safe_document
from app.util.text_utils import slugify
router = APIRouter()


# Pydantic model for request validation
class AuthorData(BaseModel):
    data: dict
    dbName: str


@router.post("/authors")
async def save_author(author: AuthorData):
    try:
        collection = BlogDB.get_collection(author.dbName, "authors")

        author_data = author.data
        if not author_data.get("uuid"):
            author_data["uuid"] = str(uuid.uuid4())

        if "name" in author_data:
            author_data["slug"] = slugify(author_data["name"])

        if "created_date" not in author_data:
            author_data["created_date"] = datetime.now(timezone.utc)

        existing_author = collection.find_one({"email": author_data["email"]})
        if existing_author:
            raise HTTPException(status_code=400, detail="Author with this email already exists")

        collection.insert_one(author_data)
        return {"status": "success", "message": "Author saved successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving author: {e}")


def get_all_authors(
    app_name: str,
    limit: int = 300,
    skip: int = 0,
):
    collection = BlogDB.get_collection(app_name, "authors")

    # Projection (optional): only return fields you need to reduce payload size
    projection = {"_id": 0}

    # Fetch authors with skip/limit/sort and use projection
    start = time.time()
    cursor = collection.find({}, projection).skip(skip).limit(limit).sort([("created_date", -1)])
    print("Mongo fetch time:", time.time() - start)

    return [safe_document(author) for author in cursor]


@router.get("/authors")
async def get_authors(app_name: str, query: str = "", page: int = 1, limit: int = 100):
    """
    Fetch authors from MongoDB using a page-based pagination system.
    """
    try:
        if page < 1:
            raise HTTPException(status_code=400, detail="Page number must be 1 or greater.")

        skip = (page - 1) * limit  # Calculate the skip value for pagination

        db = BlogDB.get_database(app_name)
        collection = db.get_collection("authors")

        # Build search filter
        search_filter = {}
        if query:
            search_filter["$or"] = [
                {"name": {"$regex": query, "$options": "i"}},  # Case-insensitive search on name
                {"email": {"$regex": query, "$options": "i"}},  # Case-insensitive search on email
            ]

        # Fetch authors with pagination and sorting
        authors = list(collection.find(search_filter).skip(skip).limit(limit).sort([("created_date", -1)]))

        return {"status": "success", "data": [safe_document(author) for author in authors], "page": page}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching authors: {e}")


@router.put("/authors")
async def update_author(author: AuthorData):
    """
    Update an existing author in the database based on the provided uuid.
    """
    try:
        db = BlogDB.get_database(author.dbName)
        collection = db.get_collection("authors")

        author_data = author.data

        # Ensure uuid is provided for update
        if "uuid" not in author_data:
            raise HTTPException(status_code=400, detail="Missing 'uuid' in author data for update.")

        if "name" in author_data:
            author_data["slug"] = slugify(author_data["name"])

        # Check if email already exists for a different author
        existing_author = collection.find_one({"email": author_data["email"], "uuid": {"$ne": author_data["uuid"]}})
        if existing_author:
            raise HTTPException(status_code=400, detail="Author with this email already exists")

        # Update the author with the provided uuid
        result = collection.update_one({"uuid": author_data["uuid"]}, {"$set": author_data})

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Author with the specified 'uuid' not found.")

        return {"status": "success", "message": "Author updated successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating author: {e}")


@router.delete("/authors/{uuid}")
async def delete_author(uuid: str, app_name: str):
    """
    Delete an author from the database based on the provided uuid.
    """
    try:
        db = BlogDB.get_database(app_name)
        collection = db.get_collection("authors")

        # Delete the author with the provided uuid
        result = collection.delete_one({"uuid": uuid})

        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Author with the specified 'uuid' not found.")

        return {"status": "success", "message": "Author deleted successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting author: {e}")


@router.get("/authors/{uuid}")
async def get_author(uuid: str, app_name: str):
    """
    Fetch a single author from MongoDB based on uuid.
    """
    try:
        db = BlogDB.get_database(app_name)
        collection = db.get_collection("authors")

        author = collection.find_one({"uuid": uuid})

        if not author:
            raise HTTPException(status_code=404, detail="Author not found")

        return {"status": "success", "data": safe_document(author)}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching author: {e}")
