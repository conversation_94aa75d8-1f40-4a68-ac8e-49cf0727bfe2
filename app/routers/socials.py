from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List

from app.util.common import (
    add_metadata_fields,
    build_search_filter,
    get_document_by_id_or_uuid,
    validate_pagination,
)
from app.util.db.database import BlogDB, safe_document, safe_documents

router = APIRouter()


# Pydantic model for request validation
class SocialData(BaseModel):
    data: dict
    dbName: str


@router.post("/socials")
async def save_social(social: SocialData):
    """Save a new social to the database"""
    try:
        collection = BlogDB.get_collection(social.dbName, "socials")
        social_data = social.data.copy()

        # Add metadata fields
        add_metadata_fields(social_data)

        collection.insert_one(social_data)
        return {"status": "success", "message": "Social saved successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving social: {e}")


@router.put("/socials")
async def update_social(social: SocialData):
    """Update an existing social in the database"""
    try:
        collection = BlogDB.get_collection(social.dbName, "socials")
        social_data = social.data.copy()

        if "uuid" not in social_data:
            raise HTTPException(status_code=400, detail="Missing 'uuid' in social data for update.")

        result = collection.update_one({"uuid": social_data["uuid"]}, {"$set": social_data})

        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Social with the specified 'uuid' not found.")

        return {"status": "success", "message": "Social updated successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating social: {e}")


@router.get("/socials")
async def get_socials(app_name: str, project_name: str = "", query: str = "", page: int = 1, limit: int = 100, sort_by: str = "created_date", sort_order: str = "desc"):
    """
    Fetch socials from MongoDB using a page-based pagination system.
    """
    try:
        skip, limit = validate_pagination(page, limit)

        collection = BlogDB.get_collection(app_name, "socials")

        # Build search filter
        base_filter = {}
        if project_name:
            base_filter["project_name"] = project_name

        search_filter = build_search_filter(base_filter, query, "meta_title")

        # Get total count for pagination metadata
        total_count = collection.count_documents(search_filter)

        # Determine sort order
        sort_direction = -1 if sort_order.lower() == "desc" else 1

        # Validate sort_by field (only allow specific fields for security)
        allowed_sort_fields = ["created_date", "meta_title", "primary_keyword", "content_type"]
        if sort_by not in allowed_sort_fields:
            sort_by = "created_date"

        # Fetch socials with pagination and sorting
        socials = list(collection.find(search_filter).skip(skip).limit(limit).sort([(sort_by, sort_direction)]))

        # Enrich socials with author informatio

        # Calculate pagination metadata
        has_more = (skip + len(socials)) < total_count
        total_pages = (total_count + limit - 1) // limit  # Ceiling division

        return {
            "status": "success",
            "data": safe_documents(socials),
            "page": page,
            "total_count": total_count,
            "total_pages": total_pages,
            "has_more": has_more,
            "limit": limit,
            "sort_by": sort_by,
            "sort_order": sort_order,
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching socials: {e}")


@router.get("/socials/{social_id}")
async def get_social(social_id: str, app_name: str):
    """Get a specific social by ID or UUID"""
    try:
        collection = BlogDB.get_collection(app_name, "socials")
        social = get_document_by_id_or_uuid(collection, social_id)

        return {"status": "success", "data": safe_document(social)}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching social: {e}")


@router.delete("/socials/{social_id}")
async def delete_social(social_id: str, app_name: str):
    """Delete a social by UUID"""
    try:
        collection = BlogDB.get_collection(app_name, "socials")
        result = collection.delete_one({"uuid": social_id})

        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Social not found")

        return {"status": "success", "message": "Social deleted successfully!"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting social: {e}")
