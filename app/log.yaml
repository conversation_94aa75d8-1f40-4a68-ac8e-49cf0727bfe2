version: 1
disable_existing_loggers: False

formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

handlers:
  rotating_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: standard
    filename: app-log/uvicorn.log
    maxBytes: 10485760 # 10MB
    backupCount: 7

loggers:
  uvicorn:
    level: INFO
    handlers: [rotating_file]
    propagate: no
  uvicorn.error:
    level: INFO
    handlers: [rotating_file]
    propagate: no
  uvicorn.access:
    level: INFO
    handlers: [rotating_file]
    propagate: no

root:
  level: INFO
  handlers: [rotating_file]
