# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
app-log/
stark/
static/audio/

/data/prod

mantra-ui-builder
# C extensions
*.so
.mysql
# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.DS_Store
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec
*.pem

static/js/~partytown/

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py
test.txt
# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

static/css/app.css

node_modules

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

.idea/

# Project
postgres-data
*.db
app/data/prod/builders.csv
app/data/prod/cities.csv
app/data/prod/locations.csv
app/data/prod/projects.csv
app/data/gmaps/projects/gharpe-projects-images-*.csv

# Pickle Files
*.pkl