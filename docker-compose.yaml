services:
  app:
    image: 500apps/kong-ai:prod
    environment:
      - APP_DEBUG=False
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        order: start-first
        failure_action: rollback
        delay: 10s
      rollback_config:
        parallelism: 0
        order: stop-first
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s
    healthcheck:
      test: curl --fail http://127.0.0.1:8000/_app/_version || exit 1
      interval: 5s
      timeout: 30s
      retries: 6
      start_period: 60s
    ports:
      - "127.0.0.1:8000:8000"

    networks:
      - kong-ai

    volumes:
      - /root/kong-ai/app-log:/opt/app-log

    logging:
      driver: "json-file"
      options:
        max-size: "10m"
networks:
  kong-ai:
