!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("onnxruntime-web")):"function"==typeof define&&define.amd?define(["onnxruntime-web"],t):"object"==typeof exports?exports.vad=t(require("onnxruntime-web")):e.vad=t(e.ort)}(self,(e=>(()=>{"use strict";var t={428:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FrameProcessor=t.validateOptions=t.defaultFrameProcessorOptions=void 0;const o=s(294),r=s(842),i=[512,1024,1536];t.defaultFrameProcessorOptions={positiveSpeechThreshold:.5,negativeSpeechThreshold:.35,preSpeechPadFrames:1,redemptionFrames:8,frameSamples:1536,minSpeechFrames:3,submitUserSpeechOnPause:!1},t.validateOptions=function(e){i.includes(e.frameSamples)||r.log.warn("You are using an unusual frame size"),(e.positiveSpeechThreshold<0||e.negativeSpeechThreshold>1)&&r.log.error("postiveSpeechThreshold should be a number between 0 and 1"),(e.negativeSpeechThreshold<0||e.negativeSpeechThreshold>e.positiveSpeechThreshold)&&r.log.error("negativeSpeechThreshold should be between 0 and postiveSpeechThreshold"),e.preSpeechPadFrames<0&&r.log.error("preSpeechPadFrames should be positive"),e.redemptionFrames<0&&r.log.error("preSpeechPadFrames should be positive")};const n=e=>{const t=e.reduce(((e,t)=>(e.push(e.at(-1)+t.length),e)),[0]),s=new Float32Array(t.at(-1));return e.forEach(((e,o)=>{const r=t[o];s.set(e,r)})),s};t.FrameProcessor=class{constructor(e,t,s){this.modelProcessFunc=e,this.modelResetFunc=t,this.options=s,this.speaking=!1,this.redemptionCounter=0,this.active=!1,this.reset=()=>{this.speaking=!1,this.audioBuffer=[],this.modelResetFunc(),this.redemptionCounter=0},this.pause=()=>(this.active=!1,this.options.submitUserSpeechOnPause?this.endSegment():(this.reset(),{})),this.resume=()=>{this.active=!0},this.endSegment=()=>{const e=this.audioBuffer;this.audioBuffer=[];const t=this.speaking;this.reset();const s=e.reduce(((e,t)=>e+ +t.isSpeech),0);if(t){if(s>=this.options.minSpeechFrames){const t=n(e.map((e=>e.frame)));return{msg:o.Message.SpeechEnd,audio:t}}return{msg:o.Message.VADMisfire}}return{}},this.process=async e=>{if(!this.active)return{};const t=await this.modelProcessFunc(e);if(this.audioBuffer.push({frame:e,isSpeech:t.isSpeech>=this.options.positiveSpeechThreshold}),t.isSpeech>=this.options.positiveSpeechThreshold&&this.redemptionCounter&&(this.redemptionCounter=0),t.isSpeech>=this.options.positiveSpeechThreshold&&!this.speaking)return this.speaking=!0,{probs:t,msg:o.Message.SpeechStart};if(t.isSpeech<this.options.negativeSpeechThreshold&&this.speaking&&++this.redemptionCounter>=this.options.redemptionFrames){this.redemptionCounter=0,this.speaking=!1;const e=this.audioBuffer;if(this.audioBuffer=[],e.reduce(((e,t)=>e+ +t.isSpeech),0)>=this.options.minSpeechFrames){const s=n(e.map((e=>e.frame)));return{probs:t,msg:o.Message.SpeechEnd,audio:s}}return{probs:t,msg:o.Message.VADMisfire}}if(!this.speaking)for(;this.audioBuffer.length>this.options.preSpeechPadFrames;)this.audioBuffer.shift();return{probs:t}},this.audioBuffer=[],this.reset()}}},14:function(e,t,s){var o=this&&this.__createBinding||(Object.create?function(e,t,s,o){void 0===o&&(o=s);var r=Object.getOwnPropertyDescriptor(t,s);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,o,r)}:function(e,t,s,o){void 0===o&&(o=s),e[o]=t[s]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&o(t,e,s);return r(t,e),t},n=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||o(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),t.utils=void 0;const a=i(s(26));t.utils={minFramesForTargetMS:a.minFramesForTargetMS,arrayBufferToBase64:a.arrayBufferToBase64,encodeWAV:a.encodeWAV},n(s(405),t),n(s(428),t),n(s(294),t),n(s(842),t),n(s(260),t),n(s(724),t)},842:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.log=t.LOG_PREFIX=void 0,t.LOG_PREFIX="[VAD]";const s=["error","debug","warn"].reduce(((e,s)=>(e[s]=function(e){return(...s)=>{console[e](t.LOG_PREFIX,...s)}}(s),e)),{});t.log=s},294:(e,t)=>{var s;Object.defineProperty(t,"__esModule",{value:!0}),t.Message=void 0,function(e){e.AudioFrame="AUDIO_FRAME",e.SpeechStart="SPEECH_START",e.VADMisfire="VAD_MISFIRE",e.SpeechEnd="SPEECH_END",e.SpeechStop="SPEECH_STOP"}(s||(t.Message=s={}))},260:(e,t,s)=>{var o;Object.defineProperty(t,"__esModule",{value:!0}),t.Silero=void 0;const r=s(842);class i{constructor(e,t){this.ort=e,this.modelFetcher=t,this.init=async()=>{r.log.debug("initializing vad");const e=await this.modelFetcher();this._session=await this.ort.InferenceSession.create(e),this._sr=new this.ort.Tensor("int64",[16000n]),this.reset_state(),r.log.debug("vad is initialized")},this.reset_state=()=>{const e=Array(128).fill(0);this._h=new this.ort.Tensor("float32",e,[2,1,64]),this._c=new this.ort.Tensor("float32",e,[2,1,64])},this.process=async e=>{const t={input:new this.ort.Tensor("float32",e,[1,e.length]),h:this._h,c:this._c,sr:this._sr},s=await this._session.run(t);this._h=s.hn,this._c=s.cn;const[o]=s.output.data;return{notSpeech:1-o,isSpeech:o}}}}t.Silero=i,o=i,i.new=async(e,t)=>{const s=new o(e,t);return await s.init(),s}},405:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PlatformAgnosticNonRealTimeVAD=t.defaultNonRealTimeVADOptions=void 0;const o=s(428),r=s(294),i=s(260),n=s(724);t.defaultNonRealTimeVADOptions={...o.defaultFrameProcessorOptions,ortConfig:void 0},t.PlatformAgnosticNonRealTimeVAD=class{static async _new(e,s,o={}){const r={...t.defaultNonRealTimeVADOptions,...o};void 0!==r.ortConfig&&r.ortConfig(s);const i=new this(e,s,r);return await i.init(),i}constructor(e,t,s){this.modelFetcher=e,this.ort=t,this.options=s,this.init=async()=>{const e=await i.Silero.new(this.ort,this.modelFetcher);this.frameProcessor=new o.FrameProcessor(e.process,e.reset_state,{frameSamples:this.options.frameSamples,positiveSpeechThreshold:this.options.positiveSpeechThreshold,negativeSpeechThreshold:this.options.negativeSpeechThreshold,redemptionFrames:this.options.redemptionFrames,preSpeechPadFrames:this.options.preSpeechPadFrames,minSpeechFrames:this.options.minSpeechFrames,submitUserSpeechOnPause:this.options.submitUserSpeechOnPause}),this.frameProcessor.resume()},this.run=async function*(e,t){const s={nativeSampleRate:t,targetSampleRate:16e3,targetFrameSize:this.options.frameSamples},o=new n.Resampler(s);let i=0,a=0,c=0;for await(const t of o.stream(e)){const{msg:e,audio:s}=await this.frameProcessor.process(t);switch(e){case r.Message.SpeechStart:i=c*this.options.frameSamples/16;break;case r.Message.SpeechEnd:a=(c+1)*this.options.frameSamples/16,yield{audio:s,start:i,end:a}}c++}const{msg:h,audio:u}=this.frameProcessor.endSegment();h==r.Message.SpeechEnd&&(yield{audio:u,start:i,end:c*this.options.frameSamples/16})},(0,o.validateOptions)(s)}}},724:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Resampler=void 0;const o=s(842);t.Resampler=class{constructor(e){this.options=e,this.process=e=>{const t=[];for(this.fillInputBuffer(e);this.hasEnoughDataForFrame();){const e=this.generateOutputFrame();t.push(e)}return t},this.stream=async function*(e){for(this.fillInputBuffer(e);this.hasEnoughDataForFrame();){const e=this.generateOutputFrame();yield e}},e.nativeSampleRate<16e3&&o.log.error("nativeSampleRate is too low. Should have 16000 = targetSampleRate <= nativeSampleRate"),this.inputBuffer=[]}fillInputBuffer(e){for(const t of e)this.inputBuffer.push(t)}hasEnoughDataForFrame(){return this.inputBuffer.length*this.options.targetSampleRate/this.options.nativeSampleRate>=this.options.targetFrameSize}generateOutputFrame(){const e=new Float32Array(this.options.targetFrameSize);let t=0,s=0;for(;t<this.options.targetFrameSize;){let o=0,r=0;for(;s<Math.min(this.inputBuffer.length,(t+1)*this.options.nativeSampleRate/this.options.targetSampleRate);){const e=this.inputBuffer[s];void 0!==e&&(o+=e,r++),s++}e[t]=o/r,t++}return this.inputBuffer=this.inputBuffer.slice(s),e}}},26:(e,t)=>{function s(e,t,s){for(var o=0;o<s.length;o++)e.setUint8(t+o,s.charCodeAt(o))}Object.defineProperty(t,"__esModule",{value:!0}),t.encodeWAV=t.arrayBufferToBase64=t.minFramesForTargetMS=void 0,t.minFramesForTargetMS=function(e,t,s=16e3){return Math.ceil(e*s/1e3/t)},t.arrayBufferToBase64=function(e){for(var t=new Uint8Array(e),s=t.byteLength,o=0;o<s;o++)String.fromCharCode(t[o]);return Buffer.from(t).toString("base64")},t.encodeWAV=function(e,t=3,o=16e3,r=1,i=32){var n=i/8,a=r*n,c=new ArrayBuffer(44+e.length*n),h=new DataView(c);return s(h,0,"RIFF"),h.setUint32(4,36+e.length*n,!0),s(h,8,"WAVE"),s(h,12,"fmt "),h.setUint32(16,16,!0),h.setUint16(20,t,!0),h.setUint16(22,r,!0),h.setUint32(24,o,!0),h.setUint32(28,o*a,!0),h.setUint16(32,a,!0),h.setUint16(34,i,!0),s(h,36,"data"),h.setUint32(40,e.length*n,!0),1===t?function(e,t,s){for(var o=0;o<s.length;o++,t+=2){var r=Math.max(-1,Math.min(1,s[o]));e.setInt16(t,r<0?32768*r:32767*r,!0)}}(h,44,e):function(e,t,s){for(var o=0;o<s.length;o++,t+=4)e.setFloat32(t,s[o],!0)}(h,44,e),c}},485:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.assetPath=void 0;const s="undefined"!=typeof window&&void 0!==window.document?window.document.currentScript:null;let o="/";s&&(o=s.src.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/")),t.assetPath=e=>o+e},973:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultModelFetcher=void 0,t.defaultModelFetcher=e=>fetch(e).then((e=>e.arrayBuffer()))},590:function(e,t,s){var o=this&&this.__createBinding||(Object.create?function(e,t,s,o){void 0===o&&(o=s);var r=Object.getOwnPropertyDescriptor(t,s);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,o,r)}:function(e,t,s,o){void 0===o&&(o=s),e[o]=t[s]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&o(t,e,s);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultRealTimeVADOptions=t.AudioNodeVAD=t.MicVAD=t.NonRealTimeVAD=t.Message=t.FrameProcessor=t.utils=t.defaultNonRealTimeVADOptions=void 0;const n=i(s(656)),a=s(14);Object.defineProperty(t,"FrameProcessor",{enumerable:!0,get:function(){return a.FrameProcessor}}),Object.defineProperty(t,"Message",{enumerable:!0,get:function(){return a.Message}});const c=s(787),h=s(973),u=s(485);t.defaultNonRealTimeVADOptions={modelURL:(0,u.assetPath)("silero_vad.onnx"),modelFetcher:h.defaultModelFetcher};class l extends a.PlatformAgnosticNonRealTimeVAD{static async new(e={}){const{modelURL:s,modelFetcher:o}={...t.defaultNonRealTimeVADOptions,...e};return await this._new((()=>o(s)),n,e)}}t.NonRealTimeVAD=l,t.utils={audioFileToArray:c.audioFileToArray,...a.utils};var d=s(746);Object.defineProperty(t,"MicVAD",{enumerable:!0,get:function(){return d.MicVAD}}),Object.defineProperty(t,"AudioNodeVAD",{enumerable:!0,get:function(){return d.AudioNodeVAD}}),Object.defineProperty(t,"defaultRealTimeVADOptions",{enumerable:!0,get:function(){return d.defaultRealTimeVADOptions}})},746:function(e,t,s){var o=this&&this.__createBinding||(Object.create?function(e,t,s,o){void 0===o&&(o=s);var r=Object.getOwnPropertyDescriptor(t,s);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,o,r)}:function(e,t,s,o){void 0===o&&(o=s),e[o]=t[s]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var s in e)"default"!==s&&Object.prototype.hasOwnProperty.call(e,s)&&o(t,e,s);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.AudioNodeVAD=t.MicVAD=t.defaultRealTimeVADOptions=t.ort=void 0;const n=i(s(656)),a=s(14),c=s(485),h=s(973);t.ort=n,t.defaultRealTimeVADOptions={...a.defaultFrameProcessorOptions,onFrameProcessed:e=>{},onVADMisfire:()=>{a.log.debug("VAD misfire")},onSpeechStart:()=>{a.log.debug("Detected speech start")},onSpeechEnd:()=>{a.log.debug("Detected speech end")},workletURL:(0,c.assetPath)("vad.worklet.bundle.min.js"),modelURL:(0,c.assetPath)("silero_vad.onnx"),modelFetcher:h.defaultModelFetcher,stream:void 0,ortConfig:void 0};class u{static async new(e={}){const s={...t.defaultRealTimeVADOptions,...e};let o;(0,a.validateOptions)(s),o=void 0===s.stream?await navigator.mediaDevices.getUserMedia({audio:{...s.additionalAudioConstraints,channelCount:1,echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0}}):s.stream;const r=new AudioContext,i=new MediaStreamAudioSourceNode(r,{mediaStream:o}),n=await l.new(r,s);return n.receive(i),new u(s,r,o,n,i)}constructor(e,t,s,o,r,i=!1){this.options=e,this.audioContext=t,this.stream=s,this.audioNodeVAD=o,this.sourceNode=r,this.listening=i,this.pause=()=>{this.audioNodeVAD.pause(),this.listening=!1},this.start=()=>{this.audioNodeVAD.start(),this.listening=!0},this.destroy=()=>{this.listening&&this.pause(),void 0===this.options.stream&&this.stream.getTracks().forEach((e=>e.stop())),this.sourceNode.disconnect(),this.audioNodeVAD.destroy(),this.audioContext.close()}}}t.MicVAD=u;class l{static async new(e,s={}){const o={...t.defaultRealTimeVADOptions,...s};(0,a.validateOptions)(o),void 0!==o.ortConfig&&o.ortConfig(t.ort);try{await e.audioWorklet.addModule(o.workletURL)}catch(e){throw console.error(`Encountered an error while loading worklet. Please make sure the worklet vad.bundle.min.js at the specified path:\n        ${o.workletURL}\n        If need be, you can customize the worklet file location using the \`workletURL\` option.`),e}const r=new AudioWorkletNode(e,"vad-helper-worklet",{processorOptions:{frameSamples:o.frameSamples}});let i;try{i=await a.Silero.new(t.ort,(()=>o.modelFetcher(o.modelURL)))}catch(e){throw console.error(`Encountered an error while loading model file. Please make sure silero_vad.onnx available at the specified path:\n      ${o.modelURL}\n      If need be, you can customize the model file location using the \`modelsURL\` option.`),e}const n=new a.FrameProcessor(i.process,i.reset_state,{frameSamples:o.frameSamples,positiveSpeechThreshold:o.positiveSpeechThreshold,negativeSpeechThreshold:o.negativeSpeechThreshold,redemptionFrames:o.redemptionFrames,preSpeechPadFrames:o.preSpeechPadFrames,minSpeechFrames:o.minSpeechFrames,submitUserSpeechOnPause:o.submitUserSpeechOnPause}),c=new l(e,o,n,r);return r.port.onmessage=async e=>{if(e.data?.message===a.Message.AudioFrame){const t=e.data.data,s=new Float32Array(t);await c.processFrame(s)}},c}constructor(e,t,s,o){this.ctx=e,this.options=t,this.frameProcessor=s,this.entryNode=o,this.pause=()=>{const e=this.frameProcessor.pause();this.handleFrameProcessorEvent(e)},this.start=()=>{this.frameProcessor.resume()},this.receive=e=>{e.connect(this.entryNode)},this.processFrame=async e=>{const t=await this.frameProcessor.process(e);this.handleFrameProcessorEvent(t)},this.handleFrameProcessorEvent=e=>{switch(void 0!==e.probs&&this.options.onFrameProcessed(e.probs),e.msg){case a.Message.SpeechStart:this.options.onSpeechStart();break;case a.Message.VADMisfire:this.options.onVADMisfire();break;case a.Message.SpeechEnd:this.options.onSpeechEnd(e.audio)}},this.destroy=()=>{this.entryNode.port.postMessage({message:a.Message.SpeechStop}),this.entryNode.disconnect()}}}t.AudioNodeVAD=l},787:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.audioFileToArray=void 0,t.audioFileToArray=async function(e){const t=new OfflineAudioContext(1,1,44100),s=new FileReader;let o=null;if(await new Promise((r=>{s.addEventListener("loadend",(e=>{const i=s.result;t.decodeAudioData(i,(e=>{o=e,t.startRendering().then((e=>{console.log("Rendering completed successfully"),r()})).catch((e=>{console.error(`Rendering failed: ${e}`)}))}),(e=>{console.log(`Error with decoding audio data: ${e}`)}))})),s.readAsArrayBuffer(e)})),null===o)throw Error("some error");let r=o,i=new Float32Array(r.length);for(let e=0;e<r.length;e++)for(let t=0;t<r.numberOfChannels;t++)i[e]+=r.getChannelData(t)[e];return{audio:i,sampleRate:r.sampleRate}}},656:t=>{t.exports=e}},s={};return function e(o){var r=s[o];if(void 0!==r)return r.exports;var i=s[o]={exports:{}};return t[o].call(i.exports,i,i.exports,e),i.exports}(590)})()));