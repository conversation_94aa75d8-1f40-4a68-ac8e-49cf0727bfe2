class Crud {
    constructor(collectionName, dbName = null) {
        this.collectionName = collectionName;
        this.dbName = dbName;
        this.baseUrl = `${window.Mantra_AI_Server}/db/${collectionName}`;
    }

    async get({
        filters = {},
        search = "",
        page = 1,
        limit = 25,
        sort = "desc",
        fields = [],
    }) {
        const queryParams = new URLSearchParams({
            page,
            limit,
            sort,
            search,
        });

        Object.entries(filters).forEach(([key, value]) => {
            queryParams.append("filter", `${key}:${value}`);
        });

        fields.forEach((field) => {
            queryParams.append("fields", field);
        });

        const response = await fetch(`${this.baseUrl}?${queryParams.toString()}`, {
            headers: this._getHeaders(),
        });
        if (!response.ok) {
            throw new Error(`Error fetching documents: ${response.statusText}`);
        }
        return response.json();
    }

    async create(data) {
        const response = await fetch(this.baseUrl, {
            method: "POST",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify({ data }),
        });
        if (!response.ok) {
            throw new Error(`Error creating document: ${response.statusText}`);
        }
        return response.json();
    }

    async update(documentId, data) {
        const response = await fetch(`${this.baseUrl}/${documentId}`, {
            method: "PUT",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify({ data }),
        });
        if (!response.ok) {
            throw new Error(`Error updating document: ${response.statusText}`);
        }
        return response.json();
    }

    async delete(documentId) {
        const response = await fetch(`${this.baseUrl}/${documentId}`, {
            method: "DELETE",
            headers: this._getHeaders(),
        });
        if (!response.ok) {
            throw new Error(`Error deleting document: ${response.statusText}`);
        }
        return response.json();
    }

    async bulkDelete(documentIds, count) {
        const response = await fetch(`${this.baseUrl}?count=${count}`, {
            method: "DELETE",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify(documentIds),
        });
        if (!response.ok) {
            throw new Error(`Error bulk deleting documents: ${response.statusText}`);
        }
        return response.json();
    }

    async getDocument(documentId) {
        const response = await fetch(`${this.baseUrl}/${documentId}`, {
            headers: this._getHeaders(),
        });
        if (!response.ok) {
            throw new Error(`Error fetching document: ${response.statusText}`);
        }
        return response.json();
    }

    async getArrayKey(documentId, key, sort = "desc") {
        const response = await fetch(
            `${this.baseUrl}/${documentId}/${key}?sort=${sort}`,
            { headers: this._getHeaders() }
        );
        if (!response.ok) {
            throw new Error(`Error fetching array key: ${response.statusText}`);
        }
        return response.json();
    }

    async addToArrayKey(documentId, key, data) {
        const response = await fetch(`${this.baseUrl}/${documentId}/${key}`, {
            method: "POST",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify({ data }),
        });
        if (!response.ok) {
            throw new Error(`Error adding to array key: ${response.statusText}`);
        }
        return response.json();
    }

    async updateInArrayKey(documentId, key, uid, data) {
        const response = await fetch(
            `${this.baseUrl}/${documentId}/${key}/${uid}`,
            {
                method: "PUT",
                headers: this._getHeaders({ "Content-Type": "application/json" }),
                body: JSON.stringify({ data }),
            }
        );
        if (!response.ok) {
            throw new Error(`Error updating array key: ${response.statusText}`);
        }
        return response.json();
    }

    async deleteFromArrayKey(documentId, key, uid) {
        const response = await fetch(
            `${this.baseUrl}/${documentId}/${key}/${uid}`,
            {
                method: "DELETE",
                headers: this._getHeaders(),
            }
        );
        if (!response.ok) {
            throw new Error(`Error deleting from array key: ${response.statusText}`);
        }
        return response.json();
    }

    async getFromArrayKey(documentId, key, uid) {
        const response = await fetch(`${this.baseUrl}/${documentId}/${key}/${uid}`, {
            headers: this._getHeaders(),
        });
        if (!response.ok) {
            throw new Error(`Error fetching from array key: ${response.statusText}`);
        }
        return response.json();
    }

    async bulkUpdate(documentIds = [], data, filter = {}) {
        if (!documentIds.length && !Object.keys(filter).length) {
            throw new Error("Either documentIds or filter must be provided.");
        }

        const response = await fetch(`${this.baseUrl}/bulk`, {
            method: "PUT",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify({ document_ids: documentIds, data, filter }),
        });

        if (!response.ok) {
            throw new Error(`Error bulk updating documents: ${response.statusText}`);
        }
        return response.json();
    }

    async bulkUpdateRecords(data) {
        if (!data.length) {
            throw new Error("data must be provided.");
        }

        const response = await fetch(`/crud/bulk`, {
            method: "PUT",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify({ document_updates: data, db_name: this.dbName, collection_name: this.collectionName }),
        });

        if (!response.ok) {
            throw new Error(`Error bulk updating documents: ${response.statusText}`);
        }
        return response.json();
    }

    async bulkAddToArrayKey(documentIds = [], key, data, filter = {}) {
        if (!documentIds.length && !Object.keys(filter).length) {
            throw new Error("Either documentIds or filter must be provided.");
        }

        const response = await fetch(`${this.baseUrl}/bulk_add_to_array/${key}`, {
            method: "POST",
            headers: this._getHeaders({ "Content-Type": "application/json" }),
            body: JSON.stringify({ document_ids: documentIds, data, filter }),
        });

        if (!response.ok) {
            throw new Error(`Error bulk adding to array key: ${response.statusText}`);
        }
        return response.json();
    }

    async aggregate(fieldName, aggregateFunction, filters = {}, groupKey = null) {
        const queryParams = new URLSearchParams();

        if (Object.keys(filters).length > 0) {
            Object.entries(filters).forEach(([key, value]) => {
                queryParams.append("filter", `${key}:${value}`);
            });
        } else {
            queryParams.append("filter", "[]"); // Ensure an empty list is sent when no filters are provided
        }

        if (groupKey) {
            queryParams.append("group_key", groupKey);
        }

        const response = await fetch(
            `${this.baseUrl}/aggregate/${fieldName}/${aggregateFunction}?${queryParams.toString()}`,
            { headers: this._getHeaders() }
        );
        if (!response.ok) {
            throw new Error(`Error aggregating documents: ${response.statusText}`);
        }
        return response.json();
    }

    async sum(fieldName, filters = {}, groupKey = null) {
        return this.aggregate(fieldName, "sum", filters, groupKey);
    }

    async count(fieldName, filters = {}, groupKey = null) {
        return this.aggregate(fieldName, "count", filters, groupKey);
    }

    async distinctCount(fieldName, filters = {}, groupKey = null) {
        return this.aggregate(fieldName, "distinct_count", filters, groupKey);
    }

    async avg(fieldName, filters = {}, groupKey = null) {
        return this.aggregate(fieldName, "avg", filters, groupKey);
    }

    async min(fieldName, filters = {}, groupKey = null) {
        return this.aggregate(fieldName, "min", filters, groupKey);
    }

    async max(fieldName, filters = {}, groupKey = null) {
        return this.aggregate(fieldName, "max", filters, groupKey);
    }

    _getHeaders(additionalHeaders = {}) {
        const headers = { ...additionalHeaders };
        if (this.dbName) {
            headers["X-Mantra-DB"] = this.dbName;
        }
        return headers;
    }
}

// Usage Examples

/*
const contactsCrud = new Crud('contacts', 'my_database');

// 1. Get Documents with Filters, Search, Pagination, and Sort
contactsCrud.get({
    filters: { status: 'active', age: '30' }, // Filters: key-value pairs
    search: 'john',                          // Search query
    page: 1,                                 // Page number
    limit: 25,                               // Number of items per page
    sort: 'desc'                             // Sort order: 'asc' or 'desc'
}).then(data => console.log(data))
  .catch(error => console.error(error));

// 2. Create a New Document
const newContact = {
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone_number: '************',
    tags: ['friend', 'colleague']
};

contactsCrud.create(newContact)
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 3. Update an Existing Document
const updatedContact = {
    email: '<EMAIL>',
    phone_number: '************'
};

contactsCrud.update('documentId123', updatedContact)
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 4. Delete a Document
contactsCrud.delete('documentId123')
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 5. Get Array Key
contactsCrud.getArrayKey('documentId123', 'keyName', 'desc')
    .then(data => console.log(data))
    .catch(error => console.error(error));

// 6. Add to Array Key
const newData = {
    uid: 'itemUid123',
    data: {
        field1: 'value1',
        field2: 'value2'
    }
};

contactsCrud.addToArrayKey('documentId123', 'keyName', newData)
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 7. Update in Array Key
const updatedData = {
    field1: 'newValue1',
    field2: 'newValue2'
};

contactsCrud.updateInArrayKey('documentId123', 'keyName', 'itemUid123', updatedData)
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 8. Delete from Array Key
contactsCrud.deleteFromArrayKey('documentId123', 'keyName', 'itemUid123')
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 9. Get from Array Key
contactsCrud.getFromArrayKey('documentId123', 'keyName', 'itemUid123')
    .then(data => console.log(data))
    .catch(error => console.error(error));

// 10. Bulk Update Documents
const documentIds = ['docId1', 'docId2', 'docId3'];
const bulkUpdateData = { status: 'inactive' };

contactsCrud.bulkUpdate(documentIds, bulkUpdateData)
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 11. Bulk Add to Array Key
const bulkAddData = { cid: 111, start_time: 100 };
const filter = { status: 'active' };

contactsCrud.bulkAddToArrayKey(documentIds, 'campaigns', bulkAddData, filter)
    .then(response => console.log(response))
    .catch(error => console.error(error));

// 12. Bulk Delete Documents
const documentIdsToDelete = ['docId1', 'docId2', 'docId3'];
const expectedDeleteCount = 3;

contactsCrud.bulkDelete(documentIdsToDelete, expectedDeleteCount)
    .then(response => console.log(response))
    .catch(error => console.error(error));
*/
