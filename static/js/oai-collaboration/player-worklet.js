class AudioBufferQueue{constructor(e){this.chunks=[],this.readIndex=0,this.readOffset=0,this.totalLength=0,this.TypedArrayConstructor=e}clear(){this.chunks=[],this.totalLength=0,this.readIndex=0,this.readOffset=0}enqueue(e){0!==e.length&&(this.chunks.push(e),this.totalLength+=e.length)}dequeue(e){if(e<=0)throw new Error("Invalid argument: 'count' must be a positive integer");var t=new this.TypedArrayConstructor(e);let s=0;for(;s<e&&this.readIndex<this.chunks.length;){var r=this.chunks[this.readIndex],h=r.length-this.readOffset,h=Math.min(h,e-s);t.set(r.subarray(this.readOffset,this.readOffset+h),s),this.readOffset+=h,s+=h,this.readOffset>=r.length&&(this.readIndex+=1,this.readOffset=0)}return this.totalLength-=s,0<this.readIndex&&(this.chunks.splice(0,this.readIndex),this.readIndex=0),t}get length(){return this.totalLength}}class AudioPlayerProcessor extends AudioWorkletProcessor{constructor(){super(),this.queue=new AudioBufferQueue(Float32Array),this.state="stopped",this.port.onmessage=e=>{var t=e.data;t.audioData&&(0===this.queue.length&&this.port.postMessage({state:"playing"}),this.queue.enqueue(t.audioData)),t.clear&&0<this.queue.length&&(this.queue.clear(),this.port.postMessage({state:"stopped"}))}}process(e,t){var s=t[0][0];return 0<this.queue.length&&(s.set(this.queue.dequeue(s.length)),0===this.queue.length)&&this.port.postMessage({state:"stopped"}),!0}}registerProcessor("audio-player",AudioPlayerProcessor);