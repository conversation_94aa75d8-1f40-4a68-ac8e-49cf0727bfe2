class AudioRecorderProcessor extends AudioWorkletProcessor{constructor(){super()}process(e){var r=resampleIfNeeded(e[0]?.[0]);if(r){var o=new Int16Array(r.length);for(let e=0;e<r.length;e++){var t=Math.max(-1,Math.min(1,r[e]));o[e]=t<0?32768*t:32767*t}var a=new Uint8Array(o.buffer);this.port.postMessage(a,[a.buffer])}return!0}}function resampleIfNeeded(e){if(!e||24e3===sampleRate)return e;var r=24e3/sampleRate,o=Math.floor(e.length*r),t=new Float32Array(o);for(let f=0;f<o;f++){var a,s,n=f/r,l=Math.floor(n),n=n-l;l+1<e.length?(a=e[l],s=e[l+1],t[f]=a*(1-n)+s*n):t[f]=e[l]}return t}registerProcessor("audio-recorder",AudioRecorderProcessor);