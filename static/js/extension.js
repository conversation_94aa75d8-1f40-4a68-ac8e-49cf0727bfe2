const KONG_AI_EXPECTED_VERSION = '1.0.0';

// Function to check if the Kong.ai extension is installed and has the correct version
function checkKongAIExtension(expectedVersion = KONG_AI_EXPECTED_VERSION) {
    // Check if the extension's div exists on the page
    const extensionCheckDiv = document.getElementById('kong-ai-extension');

    if (extensionCheckDiv) {
        // Extension is installed, now check the version
        const installedVersion = extensionCheckDiv.getAttribute('data-version');

        if (installedVersion === expectedVersion) {
            console.log(`Kong.ai Extension is installed with the correct version: ${installedVersion}`);
            return true; // Return true if the correct version is installed
        } else {
            console.warn(`Kong.ai Extension is installed, but the version (${installedVersion}) does not match the expected version (${expectedVersion}).`);
            return false; // Return false if the version doesn't match
        }
    } else {
        console.warn('Kong.ai Extension is not installed.');
        return false; // Return false if the extension is not installed
    }
}

// Function to check if the extension is installed and has the correct version, and show alerts if not
function checkKongAIExtensionAndAlert(expectedVersion = KONG_AI_EXPECTED_VERSION) {
    const isExtensionCorrect = checkKongAIExtension(expectedVersion);

    if (!isExtensionCorrect) {
        if (!document.getElementById('kong-ai-extension')) {
            // Extension is not installed
            showError('Kong.ai Extension Missing', 'The Kong.ai Extension is not installed on your browser.');
        } else {
            // Extension version does not match
            showAlert('The installed Kong.ai Extension version does not match the expected version.');
        }
    }
}

// Function to show the extension status in the DOM
function showExtensionStatus(statusDivId = 'kong-ai-extension-status') {
    console.log('Checking Kong.ai Extension status......');
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM Loaded - Checking Kong.ai Extension status...');

        // Delay the execution by 2 seconds
        setTimeout(() => {
            const statusDiv = document.getElementById(statusDivId);
            const extensionCheckDiv = document.getElementById('kong-ai-extension');

            if (!extensionCheckDiv) {
                statusDiv.innerHTML = '<div class="bg-gray-100 text-sm p-3 text-black rounded-md mb-4">Kong.ai Extension is not installed. <a href="https://kong-ai.s3.ap-south-1.amazonaws.com/kong-ai.zip" target="_blank" class="underline" onmouseover="this.style.color=\'blue\'" onmouseout="this.style.color=\'initial\'">Download Here</a></div>';
            } else {
                const installedVersion = extensionCheckDiv.getAttribute('data-version');
                if (installedVersion !== KONG_AI_EXPECTED_VERSION) {
                    statusDiv.innerHTML = `<div class="bg-gray-100 text-sm text-black p-3 rounded-md mb-4">Kong.ai Extension version ${installedVersion} does not match the expected version (${KONG_AI_EXPECTED_VERSION}). <a href="https://kong-ai.s3.ap-south-1.amazonaws.com/kong-ai.zip" target="_blank" class="underline" onmouseover="this.style.color=\'blue\'" onmouseout="this.style.color=\'initial\'">Update Here</a></div>`;
                } else {
                    statusDiv.innerHTML = '<div class="bg-gray-100 text-sm p-3 rounded-md mb-4">Kong.ai Extension is installed and up-to-date.</div>';
                }
            }
        }, 2000); // 2 seconds delay
    });
}

// Automatically call showExtensionStatus on DOMContentLoaded
showExtensionStatus();

function _sendToExtension(action, data = {}) {
    console.log('Sending message to extension', action, data);
    return new Promise((resolve, reject) => {
        const requestId = Math.random().toString(36).substr(2, 9);
        const uniqueAction = `${action}-${requestId}-response`;
        const messageHandler = (event) => {
            if (event.source !== window || event.data.type !== 'response' || event.data.requestId !== requestId) {
                return;
            }
            window.removeEventListener('message', messageHandler);
            console.log("Message from extension", event.data);
            if (event.data.response && event.data.response.error) {
                reject(event.data.response.error);
            } else {
                resolve(event.data.response);
            }
        };
        window.addEventListener('message', messageHandler);
        window.postMessage({ type: 'pageCommands', action, data, requestId }, '*');
    });
}

function extensionRefreshTasks() {
    return _sendToExtension('refresh-tasks');
}
