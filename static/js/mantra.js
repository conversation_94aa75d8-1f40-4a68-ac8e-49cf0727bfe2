function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(";").shift();
}

// Helper function to get URL parameter
function getUrlParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// Function to get the access token, prioritizing URL param over cookie
function getAccessToken() {
    return getUrlParam("t") || getCookie("access_token");
}


function overrideFetch() {
    // Save the original fetch function
    const originalFetch = window.fetch;

    // Create a wrapper function around the original fetch
    window.fetch = async (input, init = {}) => {
        // Define the value for the X-Mantra-App header
        const mantraAppHeaderValue = window.Mantra_App_Name;

        // Ensure init is an object
        init = init || {};

        // Ensure headers are an object
        init.headers = init.headers || {};

        // Add the X-Mantra-App header
        init.headers["X-Mantra-App"] = mantraAppHeaderValue;

        // Add the Authorization header if access_token cookie is present and not already set
        const accessToken = getAccessToken();
        if (accessToken && !init.headers["Authorization"]) {
            init.headers["Authorization"] = `Bearer ${accessToken}`;
        }

        // Add any URL parameters starting with "x-" as headers
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.forEach((value, key) => {
            if (key.startsWith("x-")) {
                init.headers[key] = value;
            }
        });

        // Call the original fetch with the modified init object
        return originalFetch(input, init);
    };
}

overrideFetch();

async function getFingerprint() {
    const fp = await FingerprintJS.load();
    const result = await fp.get();
    window.fingerPrint = result.visitorId;
    return result.visitorId;
}

// getFingerprint();

// Function to generate a UUID
function generateUUID() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0,
            v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

function formatLocalTime(utcTimeString) {
    const date = new Date(utcTimeString);
    return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        timeZoneName: "short",
    }).format(date);
}

// Function to check login and show modal if not logged in
function checkLogin(message) {
    // Check if the "x-skip-check" parameter is present in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const skipCheck = urlParams.has("x-skip-check");

    // If "x-skip-check" is present, skip the login check
    if (skipCheck) {
        return true;
    }

    // Proceed with login check if "x-skip-check" is not present
    const accessToken = getCookie("access_token");
    if (!accessToken) {
        showModal("Please Signup or Login", message);
        return false;
    }

    return true;
}
