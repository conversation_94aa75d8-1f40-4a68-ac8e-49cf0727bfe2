
// Wizard data
const data = [
    {
        image: "https://cdn.kong.ai/static/images/slide1.svg",
        type: "object",
        title: "Welcome to Kong.ai",
        html: `<p class="pt-3">At Kong.ai, you have the power to <b>train</b>, <b>upload</b>, and <b>create bots</b> effortlessly. Our platform allows you to craft AI agents tailored to your specific needs. Whether it's for <b>sales</b>, <b>marketing</b>, <b>HR interviews</b>, or <b>newsletters</b>, Kong.ai is your ultimate AI companion. Just type your requirements and watch as our AI brings your vision to life.</p>`,
    },
    {
        image: "https://cdn.kong.ai/static/images/slide2.svg",
        type: "object",
        title: "Explore the Bots You Can Create",
        html: `<p class="pt-3">With Kong.ai, you can develop bots for various platforms like <b>WhatsApp</b>, <b>SMS</b>, <b>email</b>, and more. Train your bots using your <b>website</b>, <b>PDFs</b>, or even <b>Google data</b>. Easily set up your bots across phone, email, WhatsApp, Facebook Messenger, and other channels, ensuring seamless integration and interaction.</p>`,
    },
    {
        image: "https://cdn.kong.ai/static/images/slide3.svg",
        type: "object",
        title: "Discover the Power of AI Agents",
        html: `<p class="pt-3">AI Agents are revolutionizing the industry, and with Kong.ai, you are at the forefront of this trend. Create sophisticated AI agents by simply describing your needs. Whether for sales, marketing, or other applications, our AI agents are designed to adapt and perform, making your workflows smarter and more efficient.</p>`,
    },
];

// Call showWizard(name, data, maxCount) to display the wizard modal
// For example, you can call it on button click or page load
showWizard("welcome", data, 1);