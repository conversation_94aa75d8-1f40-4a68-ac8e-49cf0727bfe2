!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{})}(this,function(u){function l(t,n){return null==t||null==n?NaN:t<n?-1:n<t?1:n<=t?0:NaN}function g(t,n){return null==t||null==n?NaN:n<t?-1:t<n?1:t<=n?0:NaN}function v(e){let o,a,u;function c(t,n,e=0,r=t.length){if(e<r){if(0!==o(n,n))return r;do{var i=e+r>>>1;a(t[i],n)<0?e=1+i:r=i}while(e<r)}return e}return u=2!==e.length?(o=l,a=(t,n)=>l(e(t),n),(t,n)=>e(t)-n):(o=e===l||e===g?e:m,a=e),{left:c,center:function(t,n,e=0,r=t.length){var i=c(t,n,e,r-1);return e<i&&u(t[i-1],n)>-u(t[i],n)?i-1:i},right:function(t,n,e=0,r=t.length){if(e<r){if(0!==o(n,n))return r;do{var i=e+r>>>1;a(t[i],n)<=0?e=1+i:r=i}while(e<r)}return e}}}function m(){return 0}function _(t){return null===t?NaN:+t}var w=v(l),x=w.right,w=w.left,E=v(_).center,A=x;let S=W(q);var T=W(function(t){let o=q(t);return(t,n,e,r,i)=>{e<<=2,r<<=2,i<<=2,o(t,n,e+0,r+0,i),o(t,n,e+1,r+1,i),o(t,n,e+2,r+2,i),o(t,n,e+3,r+3,i)}});function W(l){return function(t,n,e=n){if(!(0<=(n=+n)))throw new RangeError("invalid rx");if(!(0<=(e=+e)))throw new RangeError("invalid ry");var r,i,o,{data:a,width:u,height:c}=t;if(!(0<=(u=Math.floor(u))))throw new RangeError("invalid width");if(0<=(c=Math.floor(void 0!==c?c:a.length/u)))return u&&c&&(n||e)&&(r=n&&l(n),i=e&&l(e),o=a.slice(),r&&i?(G(r,o,a,u,c),G(r,a,o,u,c),G(r,o,a,u,c),Z(i,a,o,u,c),Z(i,o,a,u,c),Z(i,a,o,u,c)):r?(G(r,a,o,u,c),G(r,o,a,u,c),G(r,a,o,u,c)):i&&(Z(i,a,o,u,c),Z(i,o,a,u,c),Z(i,a,o,u,c))),t;throw new RangeError("invalid height")}}function G(t,n,e,r,i){for(let o=0,a=r*i;o<a;)t(n,e,o,o+=r,1)}function Z(t,n,e,r,i){for(let o=0,a=r*i;o<r;++o)t(n,e,o,o+a,r)}function q(t){let p=Math.floor(t);if(p===t){var h=t;let f=2*h+1;return(o,a,u,c,l)=>{if((c-=l)>=u){let t=h*a[u];var s=l*h;for(let n=u,e=u+s;n<e;n+=l)t+=a[Math.min(c,n)];for(let r=u,i=c;r<=i;r+=l)t+=a[Math.min(c,r+s)],o[r]=t/f,t-=a[Math.max(u,r-s)]}}}{let h=t-p,d=2*t+1;return(o,a,u,c,l)=>{if((c-=l)>=u){let t=p*a[u];var s=l*p,f=s+l;for(let n=u,e=u+s;n<e;n+=l)t+=a[Math.min(c,n)];for(let r=u,i=c;r<=i;r+=l)t+=a[Math.min(c,r+s)],o[r]=(t+h*(a[Math.max(u,r-f)]+a[Math.min(c,r+f)]))/d,t-=a[Math.max(u,r-s)]}}}}function $(n,e){let r=0;if(void 0===e)for(var t of n)null!=t&&(t=+t)>=t&&++r;else{let t=-1;for(var i of n)null!=(i=e(i,++t,n))&&(i=+i)>=i&&++r}return r}function J(t){return 0|t.length}function K(t){return!(0<t)}function Q(t){return"object"!=typeof t||"length"in t?t:Array.from(t)}function tt(n,e){let r=0,i,o=0,a=0;if(void 0===e)for(var t of n)null!=t&&(t=+t)>=t&&(i=t-o,o+=i/++r,a+=i*(t-o));else{let t=-1;for(var u of n)null!=(u=e(u,++t,n))&&(u=+u)>=u&&(i=u-o,o+=i/++r,a+=i*(u-o))}if(1<r)return a/(r-1)}function nt(t,n){var e=tt(t,n);return e&&Math.sqrt(e)}function et(n,e){let r,i;if(void 0===e)for(var t of n)null!=t&&(void 0===r?t>=t&&(r=i=t):(r>t&&(r=t),i<t&&(i=t)));else{let t=-1;for(var o of n)null!=(o=e(o,++t,n))&&(void 0===r?o>=o&&(r=i=o):(r>o&&(r=o),i<o&&(i=o)))}return[r,i]}class k{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){var n=this._partials;let e=0;for(let o=0;o<this._n&&o<32;o++){var r=n[o],i=t+r,r=Math.abs(t)<Math.abs(r)?t-(i-r):r-(i-t);r&&(n[e++]=r),t=i}return n[e]=t,this._n=e+1,this}valueOf(){var t=this._partials;let n=this._n,e,r,i,o=0;if(0<n){for(o=t[--n];0<n&&(e=o,r=t[--n],o=e+r,!(i=r-(o-e))););0<n&&(i<0&&t[n-1]<0||0<i&&0<t[n-1])&&(r=2*i,e=o+r,r==e-o)&&(o=e)}return o}}class rt extends Map{constructor(e,r=ct){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),null!=e)for(let[t,n]of e)this.set(t,n)}get(t){return super.get(ot(this,t))}has(t){return super.has(ot(this,t))}set(t,n){return super.set(at(this,t),n)}delete(t){return super.delete(ut(this,t))}}class it extends Set{constructor(t,n=ct){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(var e of t)this.add(e)}has(t){return super.has(ot(this,t))}add(t){return super.add(at(this,t))}delete(t){return super.delete(ut(this,t))}}function ot({_intern:t,_key:n},e){var r=n(e);return t.has(r)?t.get(r):e}function at({_intern:t,_key:n},e){var r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}function ut({_intern:t,_key:n},e){var r=n(e);return t.has(r)&&(e=t.get(r),t.delete(r)),e}function ct(t){return null!==t&&"object"==typeof t?t.valueOf():t}function lt(t){return t}function st(t,...n){return vt(t,lt,lt,n)}function ft(t,...n){return vt(t,Array.from,lt,n)}function ht(t,n){for(let e=1,r=n.length;e<r;++e)t=t.flatMap(e=>e.pop().map(([t,n])=>[...e,t,n]));return t}function dt(t,n,...e){return vt(t,lt,n,e)}function pt(t,n,...e){return vt(t,Array.from,n,e)}function gt(t){if(1!==t.length)throw new Error("duplicate key");return t[0]}function vt(t,s,f,h){return function t(n,e){if(e>=h.length)return f(n);var r,i=new rt,o=h[e++];let a=-1;for(r of n){var u=o(r,++a,n),c=i.get(u);c?c.push(r):i.set(u,[r])}for(let[l,n]of i)i.set(l,t(n,e));return s(i)}(t,0)}function mt(n,t){return Array.from(t,t=>n[t])}function yt(n,...r){if("function"!=typeof n[Symbol.iterator])throw new TypeError("values is not iterable");n=Array.from(n);let[e]=r;var t;return e&&2!==e.length||1<r.length?(t=Uint32Array.from(n,(t,n)=>n),1<r.length?(r=r.map(t=>n.map(t)),t.sort((t,n)=>{for(var e of r){e=_t(e[t],e[n]);if(e)return e}})):(e=n.map(e),t.sort((t,n)=>_t(e[t],e[n]))),mt(n,t)):n.sort(bt(e))}function bt(r=l){if(r===l)return _t;if("function"!=typeof r)throw new TypeError("compare is not a function");return(t,n)=>{var e=r(t,n);return e||0===e?e:(0===r(n,n))-(0===r(t,t))}}function _t(t,n){return(null==t||!(t<=t))-(null==n||!(n<=n))||(t<n?-1:n<t?1:0)}var wt=Array.prototype.slice;function xt(t){return()=>t}let Mt=Math.sqrt(50),Et=Math.sqrt(10),At=Math.sqrt(2);function St(t,n,e){var r=(n-t)/Math.max(0,e),i=Math.floor(Math.log10(r)),r=r/Math.pow(10,i),r=r>=Mt?10:r>=Et?5:r>=At?2:1;let o,a,u;return i<0?(u=Math.pow(10,-i)/r,o=Math.round(t*u),a=Math.round(n*u),o/u<t&&++o,a/u>n&&--a,u=-u):(u=Math.pow(10,i)*r,o=Math.round(t/u),a=Math.round(n/u),o*u<t&&++o,a*u>n&&--a),a<o&&.5<=e&&e<2?St(t,n,2*e):[o,a,u]}function Tt(t,n,e){if(!(0<(e=+e)))return[];if((t=+t)===(n=+n))return[t];var r=n<t,[i,o,a]=r?St(n,t,e):St(t,n,e);if(!(i<=o))return[];var u=o-i+1,c=new Array(u);if(r)if(a<0)for(let t=0;t<u;++t)c[t]=(o-t)/-a;else for(let n=0;n<u;++n)c[n]=(o-n)*a;else if(a<0)for(let t=0;t<u;++t)c[t]=(i+t)/-a;else for(let n=0;n<u;++n)c[n]=(i+n)*a;return c}function kt(t,n,e){return St(t=+t,n=+n,e=+e)[2]}function Ct(t,n,e){e=+e;var r=(n=+n)<(t=+t),i=r?kt(n,t,e):kt(t,n,e);return(r?-1:1)*(i<0?1/-i:i)}function Nt(t,n,e){let r;for(;;){var i=kt(t,n,e);if(i===r||0===i||!isFinite(i))return[t,n];0<i?(t=Math.floor(t/i)*i,n=Math.ceil(n/i)*i):i<0&&(t=Math.ceil(t*i)/i,n=Math.floor(n*i)/i),r=i}}function Dt(t){return Math.max(1,Math.ceil(Math.log($(t))/Math.LN2)+1)}function Pt(){var m=lt,y=et,b=Dt;function n(t){for(var n,e,r=(t=Array.isArray(t)?t:Array.from(t)).length,i=new Array(r),o=0;o<r;++o)i[o]=m(t[o],o,t);var a=y(i),u=a[0],c=a[1],l=b(i,u,c);if(!Array.isArray(l)){var a=c,s=+l;if(y===et&&([u,c]=Nt(u,c,s)),(l=Tt(u,c,s))[0]<=u&&(e=kt(u,c,s)),l[l.length-1]>=c)if(c<=a&&y===et){let t=kt(u,c,s);isFinite(t)&&(0<t?c=(Math.floor(c/t)+1)*t:t<0&&(c=(Math.ceil(c*-t)+1)/-t))}else l.pop()}for(var f=l.length,h=0,d=f;l[h]<=u;)++h;for(;l[d-1]>c;)--d;(h||d<f)&&(l=l.slice(h,d),f=d-h);var p,g,v=new Array(f+1);for(o=0;o<=f;++o)(p=v[o]=[]).x0=0<o?l[o-1]:u,p.x1=o<f?l[o]:c;if(isFinite(e)){if(0<e)for(o=0;o<r;++o)null!=(n=i[o])&&u<=n&&n<=c&&v[Math.min(f,Math.floor((n-u)/e))].push(t[o]);else if(e<0)for(o=0;o<r;++o)null!=(n=i[o])&&u<=n&&n<=c&&(g=Math.floor((u-n)*e),v[Math.min(f,g+(l[g]<=n))].push(t[o]))}else for(o=0;o<r;++o)null!=(n=i[o])&&u<=n&&n<=c&&v[A(l,n,0,f)].push(t[o]);return v}return n.value=function(t){return arguments.length?(m="function"==typeof t?t:xt(t),n):m},n.domain=function(t){return arguments.length?(y="function"==typeof t?t:xt([t[0],t[1]]),n):y},n.thresholds=function(t){return arguments.length?(b="function"==typeof t?t:xt(Array.isArray(t)?wt.call(t):t),n):b},n}function Lt(n,e){let r;if(void 0===e)for(var t of n)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let t=-1;for(var i of n)null!=(i=e(i,++t,n))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function It(t,n){let e,r=-1,i=-1;if(void 0===n)for(var o of t)++i,null!=o&&(e<o||void 0===e&&o>=o)&&(e=o,r=i);else for(var a of t)null!=(a=n(a,++i,t))&&(e<a||void 0===e&&a>=a)&&(e=a,r=i);return r}function Rt(n,e){let r;if(void 0===e)for(var t of n)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let t=-1;for(var i of n)null!=(i=e(i,++t,n))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function Ot(t,n){let e,r=-1,i=-1;if(void 0===n)for(var o of t)++i,null!=o&&(e>o||void 0===e&&o>=o)&&(e=o,r=i);else for(var a of t)null!=(a=n(a,++i,t))&&(e>a||void 0===e&&a>=a)&&(e=a,r=i);return r}function Bt(e,r,i=0,o=1/0,a){if(r=Math.floor(r),i=Math.floor(Math.max(0,i)),o=Math.floor(Math.min(e.length-1,o)),i<=r&&r<=o)for(a=void 0===a?_t:bt(a);i<o;){600<o-i&&(u=o-i+1,c=r-i+1,s=Math.log(u),l=.5*Math.exp(2*s/3),s=.5*Math.sqrt(s*l*(u-l)/u)*(c-u/2<0?-1:1),Bt(e,r,Math.max(i,Math.floor(r-c*l/u+s)),Math.min(o,Math.floor(r+(u-c)*l/u+s)),a));var u,c,l,s,f=e[r];let t=i,n=o;for(Ft(e,i,r),0<a(e[o],f)&&Ft(e,i,o);t<n;){for(Ft(e,t,n),++t,--n;a(e[t],f)<0;)++t;for(;0<a(e[n],f);)--n}0===a(e[i],f)?Ft(e,i,n):Ft(e,++n,o),n<=r&&(i=n+1),r<=n&&(o=n-1)}return e}function Ft(t,n,e){var r=t[n];t[n]=t[e],t[e]=r}function Yt(n,e=l){let r,i=!1;if(1===e.length){let t;for(var o of n){var a=e(o);(i?0<l(a,t):0===l(a,a))&&(r=o,t=a,i=!0)}}else for(var t of n)(i?0<e(t,r):0===e(t,t))&&(r=t,i=!0);return r}function zt(t,n,e){var r,i,o;if((r=(t=Float64Array.from(function*(n,e){if(void 0===e)for(var t of n)null!=t&&(t=+t)>=t&&(yield t);else{let t=-1;for(var r of n)null!=(r=e(r,++t,n))&&(r=+r)>=r&&(yield r)}}(t,e))).length)&&!isNaN(n=+n))return n<=0||r<2?Rt(t):1<=n?Lt(t):(r=(r-1)*n,(o=Lt(Bt(t,i=Math.floor(r)).subarray(0,i+1)))+(Rt(t.subarray(i+1))-o)*(r-i))}function Ht(t,n,e=_){var r,i,o;if((r=t.length)&&!isNaN(n=+n))return n<=0||r<2?+e(t[0],0,t):1<=n?+e(t[r-1],r-1,t):(r=(r-1)*n,(o=+e(t[i=Math.floor(r)],i,t))+(+e(t[i+1],i+1,t)-o)*(r-i))}function jt(e,t,r=_){var i,n,o,a;if(!isNaN(t=+t))return i=Float64Array.from(e,(t,n)=>_(r(e[n],n,e))),t<=0?Ot(i):1<=t?It(i):(n=Uint32Array.from(e,(t,n)=>n),o=i.length-1,Bt(n,a=Math.floor(o*t),0,o,(t,n)=>_t(i[t],i[n])),0<=(a=Yt(n.subarray(0,a+1),t=>i[t]))?a:-1)}function Vt(t){return Array.from(function*(t){for(var n of t)yield*n}(t))}function Xt(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=new Array(i);++r<i;)o[r]=t+r*e;return o}function Ut(t,n=l){if(1===n.length)return Ot(t,n);let e,r=-1,i=-1;for(var o of t)++i,(r<0?0===n(o,o):n(o,e)<0)&&(e=o,r=i);return r}var Wt=Gt(Math.random);function Gt(a){return function(t,n=0,e=t.length){let r=e-(n=+n);for(;r;){var i=a()*r--|0,o=t[r+n];t[r+n]=t[i+n],t[i+n]=o}return t}}function Zt(t){if(!(i=t.length))return[];for(var n=-1,e=Rt(t,qt),r=new Array(e);++n<e;)for(var i,o=-1,a=r[n]=new Array(i);++o<i;)a[o]=t[o][n];return r}function qt(t){return t.length}function $t(t){return t instanceof it?t:new it(t)}function Jt(t,n){var e,r=t[Symbol.iterator](),i=new Set;for(e of n){var o,a=Kt(e);if(!i.has(a))for(;{value:o,done:u}=r.next();){if(u)return!1;var u=Kt(o);if(i.add(u),Object.is(a,u))break}}return!0}function Kt(t){return null!==t&&"object"==typeof t?t.valueOf():t}function Qt(t){return t}var tn=1,nn=2,en=3,rn=4,on=1e-6;function an(t){return"translate("+t+",0)"}function un(t){return"translate(0,"+t+")"}function cn(n){return t=>+n(t)}function ln(n,e){return e=Math.max(0,n.bandwidth()-2*e)/2,n.round()&&(e=Math.round(e)),t=>+n(t)+e}function sn(){return!this.__axis}function fn(d,p){var g=[],v=null,m=null,y=6,b=6,_=3,w="undefined"!=typeof window&&1<window.devicePixelRatio?0:.5,x=d===tn||d===rn?-1:1,M=d===rn||d===nn?"x":"y",E=d===tn||d===en?an:un;function n(t){var n=null==v?p.ticks?p.ticks.apply(p,g):p.domain():v,e=null==m?p.tickFormat?p.tickFormat.apply(p,g):Qt:m,r=Math.max(y,0)+_,i=p.range(),o=+i[0]+w,i=+i[i.length-1]+w,a=(p.bandwidth?ln:cn)(p.copy(),w),u=t.selection?t.selection():t,c=u.selectAll(".domain").data([null]),l=(n=u.selectAll(".tick").data(n,p).order()).exit(),s=n.enter().append("g").attr("class","tick"),f=n.select("line"),h=n.select("text"),c=c.merge(c.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),n=n.merge(s),f=f.merge(s.append("line").attr("stroke","currentColor").attr(M+"2",x*y)),h=h.merge(s.append("text").attr("fill","currentColor").attr(M,x*r).attr("dy",d===tn?"0em":d===en?"0.71em":"0.32em"));t!==u&&(c=c.transition(t),n=n.transition(t),f=f.transition(t),h=h.transition(t),l=l.transition(t).attr("opacity",on).attr("transform",function(t){return isFinite(t=a(t))?E(t+w):this.getAttribute("transform")}),s.attr("opacity",on).attr("transform",function(t){var n=this.parentNode.__axis;return E((n&&isFinite(n=n(t))?n:a(t))+w)})),l.remove(),c.attr("d",d===rn||d===nn?b?"M"+x*b+","+o+"H"+w+"V"+i+"H"+x*b:"M"+w+","+o+"V"+i:b?"M"+o+","+x*b+"V"+w+"H"+i+"V"+x*b:"M"+o+","+w+"H"+i),n.attr("opacity",1).attr("transform",function(t){return E(a(t)+w)}),f.attr(M+"2",x*y),h.attr(M,x*r).text(e),u.filter(sn).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",d===nn?"start":d===rn?"end":"middle"),u.each(function(){this.__axis=a})}return n.scale=function(t){return arguments.length?(p=t,n):p},n.ticks=function(){return g=Array.from(arguments),n},n.tickArguments=function(t){return arguments.length?(g=null==t?[]:Array.from(t),n):g.slice()},n.tickValues=function(t){return arguments.length?(v=null==t?null:Array.from(t),n):v&&v.slice()},n.tickFormat=function(t){return arguments.length?(m=t,n):m},n.tickSize=function(t){return arguments.length?(y=b=+t,n):y},n.tickSizeInner=function(t){return arguments.length?(y=+t,n):y},n.tickSizeOuter=function(t){return arguments.length?(b=+t,n):b},n.tickPadding=function(t){return arguments.length?(_=+t,n):_},n.offset=function(t){return arguments.length?(w=+t,n):w},n}var hn={value:()=>{}};function dn(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new pn(r)}function pn(t){this._=t}function gn(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=hn,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}pn.prototype=dn.prototype={constructor:pn,on:function(t,n){var e,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(0<=e&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}})),a=-1,u=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++a<u;)if(e=(t=o[a]).type)i[e]=gn(i[e],t.name,n);else if(null==n)for(e in i)i[e]=gn(i[e],t.name,null);return this}for(;++a<u;)if(e=(e=(t=o[a]).type)&&function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(i[e],t.name))return e},copy:function(){var t,n={},e=this._;for(t in e)n[t]=e[t].slice();return new pn(n)},call:function(t,n){if(0<(e=arguments.length-2))for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var vn="http://www.w3.org/1999/xhtml",mn={svg:"http://www.w3.org/2000/svg",xhtml:vn,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function yn(t){var n=t+="",e=n.indexOf(":");return 0<=e&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),mn.hasOwnProperty(n)?{space:mn[n],local:t}:t}function bn(t){var n=yn(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===vn&&t.documentElement.namespaceURI===vn?t.createElement(e):t.createElementNS(n,e)}})(n)}function _n(){}function wn(t){return null==t?_n:function(){return this.querySelector(t)}}function xn(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function Mn(){return[]}function En(t){return null==t?Mn:function(){return this.querySelectorAll(t)}}function An(t){return function(){return this.matches(t)}}function Sn(n){return function(t){return t.matches(n)}}var Tn=Array.prototype.find;function kn(){return this.firstElementChild}var Cn=Array.prototype.filter;function Nn(){return Array.from(this.children)}function Dn(t){return new Array(t.length)}function Pn(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function Ln(t,n,e,r,i,o){for(var a,u=0,c=n.length,l=o.length;u<l;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new Pn(t,o[u]);for(;u<c;++u)(a=n[u])&&(i[u]=a)}function In(t,n,e,r,i,o,a){for(var u,c,l=new Map,s=n.length,f=o.length,h=new Array(s),d=0;d<s;++d)(u=n[d])&&(h[d]=c=a.call(u,u.__data__,d,n)+"",l.has(c)?i[d]=u:l.set(c,u));for(d=0;d<f;++d)c=a.call(t,o[d],d,o)+"",(u=l.get(c))?((r[d]=u).__data__=o[d],l.delete(c)):e[d]=new Pn(t,o[d]);for(d=0;d<s;++d)(u=n[d])&&l.get(h[d])===u&&(i[d]=u)}function Rn(t){return t.__data__}function On(t,n){return t<n?-1:n<t?1:n<=t?0:NaN}function Bn(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Fn(t,n){return t.style.getPropertyValue(n)||Bn(t).getComputedStyle(t,null).getPropertyValue(n)}function Yn(t){return t.trim().split(/^|\s+/)}function zn(t){return t.classList||new Hn(t)}function Hn(t){this._node=t,this._names=Yn(t.getAttribute("class")||"")}function jn(t,n){for(var e=zn(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function Vn(t,n){for(var e=zn(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function Xn(){this.textContent=""}function Un(){this.innerHTML=""}function Wn(){this.nextSibling&&this.parentNode.appendChild(this)}function Gn(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Zn(){return null}function qn(){var t=this.parentNode;t&&t.removeChild(this)}function $n(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function Jn(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function Kn(o){return function(){var t=this.__on;if(t){for(var n,e=0,r=-1,i=t.length;e<i;++e)n=t[e],o.type&&n.type!==o.type||n.name!==o.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function Qn(a,u,c){return function(){function t(t){e.call(this,t,this.__data__)}var n,e,r=this.__on;e=u;if(r)for(var i=0,o=r.length;i<o;++i)if((n=r[i]).type===a.type&&n.name===a.name)return this.removeEventListener(n.type,n.listener,n.options),this.addEventListener(n.type,n.listener=t,n.options=c),void(n.value=u);this.addEventListener(a.type,t,c),n={type:a.type,name:a.name,value:u,listener:t,options:c},r?r.push(n):this.__on=[n]}}function te(t,n,e){var r=Bn(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}Pn.prototype={constructor:Pn,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},Hn.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);0<=n&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return 0<=this._names.indexOf(t)}};var ne=[null];function M(t,n){this._groups=t,this._parents=n}function ee(){return new M([[document.documentElement]],ne)}function H(t){return"string"==typeof t?new M([[document.querySelector(t)]],[document.documentElement]):new M([[t]],ne)}M.prototype=ee.prototype={constructor:M,select:function(t){"function"!=typeof t&&(t=wn(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a,u=n[i],c=u.length,l=r[i]=new Array(c),s=0;s<c;++s)(o=u[s])&&(a=t.call(o,o.__data__,s,u))&&("__data__"in o&&(a.__data__=o.__data__),l[s]=a);return new M(r,this._parents)},selectAll:function(t){var n;t="function"==typeof t?(n=t,function(){return xn(n.apply(this,arguments))}):En(t);for(var e=this._groups,r=e.length,i=[],o=[],a=0;a<r;++a)for(var u,c=e[a],l=c.length,s=0;s<l;++s)(u=c[s])&&(i.push(t.call(u,u.__data__,s,c)),o.push(u));return new M(i,o)},selectChild:function(t){return this.select(null==t?kn:(n="function"==typeof t?t:Sn(t),function(){return Tn.call(this.children,n)}));var n},selectChildren:function(t){return this.selectAll(null==t?Nn:(n="function"==typeof t?t:Sn(t),function(){return Cn.call(this.children,n)}));var n},filter:function(t){"function"!=typeof t&&(t=An(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,c=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&c.push(o);return new M(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,Rn);var e,r=n?In:Ln,i=this._parents,o=this._groups;"function"!=typeof t&&(e=t,t=function(){return e});for(var a,u=o.length,c=new Array(u),l=new Array(u),s=new Array(u),f=0;f<u;++f){var h=i[f],d=o[f],p=d.length,g="object"==typeof(a=t.call(h,h&&h.__data__,f,i))&&"length"in a?a:Array.from(a),v=g.length,m=l[f]=new Array(v),y=c[f]=new Array(v);r(h,d,m,y,s[f]=new Array(p),g,n);for(var b,_,w=0,x=0;w<v;++w)if(b=m[w]){for(x<=w&&(x=w+1);!(_=y[x])&&++x<v;);b._next=_||null}}return(c=new M(c,i))._enter=l,c._exit=s,c},enter:function(){return new M(this._enter||this._groups.map(Dn),this._parents)},exit:function(){return new M(this._exit||this._groups.map(Dn),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit(),r="function"==typeof t?(r=t(r))&&r.selection():r.append(t+"");return null!=n&&(i=(i=n(i))&&i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,n=r.length,o=Math.min(i,n),a=new Array(i),u=0;u<o;++u)for(var c,l=e[u],s=r[u],f=l.length,h=a[u]=new Array(f),d=0;d<f;++d)(c=l[d]||s[d])&&(h[d]=c);for(;u<i;++u)a[u]=e[u];return new M(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,a=i[o];0<=--o;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e=e||On;for(var n=this._groups,r=n.length,i=new Array(r),o=0;o<r;++o){for(var a,u=n[o],c=u.length,l=i[o]=new Array(c),s=0;s<c;++s)(a=u[s])&&(l[s]=a);l.sort(t)}return new M(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(var n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,n){var e,r=yn(t);return arguments.length<2?(e=this.node(),r.local?e.getAttributeNS(r.space,r.local):e.getAttribute(r)):this.each((null==n?r.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?r.local?function(n,e){return function(){var t=e.apply(this,arguments);null==t?this.removeAttributeNS(n.space,n.local):this.setAttributeNS(n.space,n.local,t)}}:function(n,e){return function(){var t=e.apply(this,arguments);null==t?this.removeAttribute(n):this.setAttribute(n,t)}}:r.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(r,n))},style:function(t,n,e){return 1<arguments.length?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(n,e,r){return function(){var t=e.apply(this,arguments);null==t?this.style.removeProperty(n):this.style.setProperty(n,t,r)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):Fn(this.node(),t)},property:function(t,n){return 1<arguments.length?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(n,e){return function(){var t=e.apply(this,arguments);null==t?delete this[n]:this[n]=t}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=Yn(t+"");if(arguments.length<2){for(var r=zn(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?jn:Vn)(this,t)}}:n?function(t){return function(){jn(this,t)}}:function(t){return function(){Vn(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?Xn:("function"==typeof t?function(n){return function(){var t=n.apply(this,arguments);this.textContent=null==t?"":t}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?Un:("function"==typeof t?function(n){return function(){var t=n.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(Wn)},lower:function(){return this.each(Gn)},append:function(t){var n="function"==typeof t?t:bn(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:bn(t),r=null==n?Zn:"function"==typeof n?n:wn(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(qn)},clone:function(t){return this.select(t?Jn:$n)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return 0<=e&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),a=o.length;if(!(arguments.length<2)){for(u=n?Qn:Kn,r=0;r<a;++r)this.each(u(o[r],n,e));return this}var u=this.node().__on;if(u)for(var c,l=0,s=u.length;l<s;++l)for(r=0,c=u[l];r<a;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return te(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return te(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};var re=0;function ie(){return new oe}function oe(){this._="@"+(++re).toString(36)}function ae(t){for(var n;n=t.sourceEvent;)t=n;return t}function ue(t,n){if(t=ae(t),n=void 0===n?t.currentTarget:n){var e=n.ownerSVGElement||n;if(e.createSVGPoint)return(e=e.createSVGPoint()).x=t.clientX,e.y=t.clientY,[(e=e.matrixTransform(n.getScreenCTM().inverse())).x,e.y];if(n.getBoundingClientRect)return e=n.getBoundingClientRect(),[t.clientX-e.left-n.clientLeft,t.clientY-e.top-n.clientTop]}return[t.pageX,t.pageY]}let ce={passive:!(oe.prototype=ie.prototype={constructor:oe,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}})},le={capture:!0,passive:!1};function se(t){t.stopImmediatePropagation()}function fe(t){t.preventDefault(),t.stopImmediatePropagation()}function he(t){var n=t.document.documentElement,e=H(t).on("dragstart.drag",fe,le);"onselectstart"in n?e.on("selectstart.drag",fe,le):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function de(t,n){var e=t.document.documentElement,r=H(t).on("dragstart.drag",null);n&&(r.on("click.drag",fe,le),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}var pe=t=>()=>t;function ge(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:a,y:u,dx:c,dy:l,dispatch:s}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:s}})}function ve(t){return!t.ctrlKey&&!t.button}function me(){return this.parentNode}function ye(t,n){return null==n?{x:t.x,y:t.y}:n}function be(){return navigator.maxTouchPoints||"ontouchstart"in this}function _e(t,n,e){(t.prototype=n.prototype=e).constructor=t}function we(t,n){var e,r=Object.create(t.prototype);for(e in n)r[e]=n[e];return r}function xe(){}ge.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var Me=1/.7,Ee="\\s*([+-]?\\d+)\\s*",Ae="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Se="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Te=/^#([0-9a-f]{3,8})$/,ke=new RegExp(`^rgb\\(${Ee},${Ee},${Ee}\\)$`),Ce=new RegExp(`^rgb\\(${Se},${Se},${Se}\\)$`),Ne=new RegExp(`^rgba\\(${Ee},${Ee},${Ee},${Ae}\\)$`),De=new RegExp(`^rgba\\(${Se},${Se},${Se},${Ae}\\)$`),Pe=new RegExp(`^hsl\\(${Ae},${Se},${Se}\\)$`),Le=new RegExp(`^hsla\\(${Ae},${Se},${Se},${Ae}\\)$`),Ie={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Re(){return this.rgb().formatHex()}function Oe(){return this.rgb().formatRgb()}function Be(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=Te.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?Fe(n):3===e?new s(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?Ye(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?Ye(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=ke.exec(t))?new s(n[1],n[2],n[3],1):(n=Ce.exec(t))?new s(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=Ne.exec(t))?Ye(n[1],n[2],n[3],n[4]):(n=De.exec(t))?Ye(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=Pe.exec(t))?Ge(n[1],n[2]/100,n[3]/100,1):(n=Le.exec(t))?Ge(n[1],n[2]/100,n[3]/100,n[4]):Ie.hasOwnProperty(t)?Fe(Ie[t]):"transparent"===t?new s(NaN,NaN,NaN,0):null}function Fe(t){return new s(t>>16&255,t>>8&255,255&t,1)}function Ye(t,n,e,r){return new s(t=r<=0?n=e=NaN:t,n,e,r)}function ze(t){return(t=t instanceof xe?t:Be(t))?new s((t=t.rgb()).r,t.g,t.b,t.opacity):new s}function He(t,n,e,r){return 1===arguments.length?ze(t):new s(t,n,e,null==r?1:r)}function s(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function je(){return"#"+We(this.r)+We(this.g)+We(this.b)}function Ve(){var t=Xe(this.opacity);return(1===t?"rgb(":"rgba(")+Ue(this.r)+`, ${Ue(this.g)}, `+Ue(this.b)+(1===t?")":`, ${t})`)}function Xe(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Ue(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function We(t){return((t=Ue(t))<16?"0":"")+t.toString(16)}function Ge(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||1<=e?t=n=NaN:n<=0&&(t=NaN),new $e(t,n,e,r)}function Ze(t){var n,e,r,i,o,a,u,c;return t instanceof $e?new $e(t.h,t.s,t.l,t.opacity):(t=t instanceof xe?t:Be(t))?t instanceof $e?t:(n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),a=NaN,c=((o=Math.max(n,e,r))+i)/2,(u=o-i)?(a=n===o?(e-r)/u+6*(e<r):e===o?(r-n)/u+2:(n-e)/u+4,u/=c<.5?o+i:2-o-i,a*=60):u=0<c&&c<1?0:a,new $e(a,u,c,t.opacity)):new $e}function qe(t,n,e,r){return 1===arguments.length?Ze(t):new $e(t,n,e,null==r?1:r)}function $e(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Je(t){return(t=(t||0)%360)<0?t+360:t}function Ke(t){return Math.max(0,Math.min(1,t||0))}function Qe(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}_e(xe,Be,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Re,formatHex:Re,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Ze(this).formatHsl()},formatRgb:Oe,toString:Oe}),_e(s,He,we(xe,{brighter(t){return t=null==t?Me:Math.pow(Me,t),new s(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new s(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new s(Ue(this.r),Ue(this.g),Ue(this.b),Xe(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:je,formatHex:je,formatHex8:function(){return"#"+We(this.r)+We(this.g)+We(this.b)+We(255*(isNaN(this.opacity)?1:this.opacity))},formatRgb:Ve,toString:Ve})),_e($e,qe,we(xe,{brighter(t){return t=null==t?Me:Math.pow(Me,t),new $e(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new $e(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,n=e+(e<.5?e:1-e)*n,e=2*e-n;return new s(Qe(240<=t?t-240:120+t,e,n),Qe(t,e,n),Qe(t<120?240+t:t-120,e,n),this.opacity)},clamp(){return new $e(Je(this.h),Ke(this.s),Ke(this.l),Xe(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){var t=Xe(this.opacity);return(1===t?"hsl(":"hsla(")+Je(this.h)+`, ${100*Ke(this.s)}%, ${100*Ke(this.l)}%`+(1===t?")":`, ${t})`)}}));let tr=Math.PI/180,nr=180/Math.PI,er=.96422,rr=1,ir=.82521,or=4/29,ar=6/29,ur=3*ar*ar,cr=ar*ar*ar;function lr(t){var n,e,r,i,o,a;return t instanceof fr?new fr(t.l,t.a,t.b,t.opacity):t instanceof yr?br(t):(i=hr((.2225045*(n=gr((t=t instanceof s?t:ze(t)).r))+.7168786*(e=gr(t.g))+.0606169*(r=gr(t.b)))/rr),n===e&&e===r?o=a=i:(o=hr((.4360747*n+.3850649*e+.1430804*r)/er),a=hr((.0139322*n+.0971045*e+.7141733*r)/ir)),new fr(116*i-16,500*(o-i),200*(i-a),t.opacity))}function sr(t,n,e,r){return 1===arguments.length?lr(t):new fr(t,n,e,null==r?1:r)}function fr(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function hr(t){return t>cr?Math.pow(t,1/3):t/ur+or}function dr(t){return t>ar?t*t*t:ur*(t-or)}function pr(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function gr(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function vr(t){var n;return t instanceof yr?new yr(t.h,t.c,t.l,t.opacity):0===(t=t instanceof fr?t:lr(t)).a&&0===t.b?new yr(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity):new yr((n=Math.atan2(t.b,t.a)*nr)<0?360+n:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function mr(t,n,e,r){return 1===arguments.length?vr(t):new yr(t,n,e,null==r?1:r)}function yr(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function br(t){var n;return isNaN(t.h)?new fr(t.l,0,0,t.opacity):(n=t.h*tr,new fr(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity))}_e(fr,sr,we(xe,{brighter(t){return new fr(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new fr(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return new s(pr(3.1338561*(n=er*dr(n))-1.6168667*(t=rr*dr(t))-.4906146*(e=ir*dr(e))),pr(-.9787684*n+1.9161415*t+.033454*e),pr(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),_e(yr,mr,we(xe,{brighter(t){return new yr(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new yr(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return br(this).rgb()}}));var _r=1.78277,wr=-.29227,xr=-.90649,Mr=1.97294,Er=Mr*xr,Ar=Mr*_r,Sr=_r*wr- -.14861*xr;function Tr(t,n,e,r){return 1===arguments.length?(i=t)instanceof kr?new kr(i.h,i.s,i.l,i.opacity):(o=(i=i instanceof s?i:ze(i)).r/255,a=i.g/255,c=i.b/255,c=(Mr*(a-(o=(Sr*c+Er*o-Ar*a)/(Sr+Er-Ar)))-wr*(a=c-o))/xr,new kr((c=(u=Math.sqrt(c*c+a*a)/(Mr*o*(1-o)))?Math.atan2(c,a)*nr-120:NaN)<0?c+360:c,u,o,i.opacity)):new kr(t,n,e,null==r?1:r);var i,o,a,u,c}function kr(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Cr(t,n,e,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*n+(4-6*o+3*a)*e+(1+3*t+3*o-3*a)*r+a*i)/6}function Nr(a){var u=a.length-1;return function(t){var n=t<=0?t=0:1<=t?u-(t=1):Math.floor(t*u),e=a[n],r=a[n+1],i=0<n?a[n-1]:2*e-r,o=n<u-1?a[n+2]:2*r-e;return Cr((t-n/u)*u,i,e,r,o)}}function Dr(a){var u=a.length;return function(t){var n=Math.floor(((t%=1)<0?++t:t)*u),e=a[(n+u-1)%u],r=a[n%u],i=a[(n+1)%u],o=a[(n+2)%u];return Cr((t-n/u)*u,e,r,i,o)}}_e(kr,Tr,we(xe,{brighter(t){return t=null==t?Me:Math.pow(Me,t),new kr(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new kr(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*tr,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),t=Math.sin(t);return new s(255*(n+e*(-.14861*r+_r*t)),255*(n+e*(wr*r+xr*t)),255*(n+Mr*r*e),this.opacity)}}));var Pr=t=>()=>t;function Lr(n,e){return function(t){return n+t*e}}function Ir(t,n){var e=n-t;return e?Lr(t,180<e||e<-180?e-360*Math.round(e/360):e):Pr(isNaN(t)?n:t)}function Rr(o){return 1==(o=+o)?c:function(t,n){return n-t?(e=t,r=n,i=o,e=Math.pow(e,i),r=Math.pow(r,i)-e,i=1/i,function(t){return Math.pow(e+t*r,i)}):Pr(isNaN(t)?n:t);var e,r,i}}function c(t,n){var e=n-t;return e?Lr(t,e):Pr(isNaN(t)?n:t)}var Or=function t(n){var a=Rr(n);function e(n,t){var e=a((n=He(n)).r,(t=He(t)).r),r=a(n.g,t.g),i=a(n.b,t.b),o=c(n.opacity,t.opacity);return function(t){return n.r=e(t),n.g=r(t),n.b=i(t),n.opacity=o(t),n+""}}return e.gamma=t,e}(1);function Br(u){return function(t){for(var n,e=t.length,r=new Array(e),i=new Array(e),o=new Array(e),a=0;a<e;++a)n=He(t[a]),r[a]=n.r||0,i[a]=n.g||0,o[a]=n.b||0;return r=u(r),i=u(i),o=u(o),n.opacity=1,function(t){return n.r=r(t),n.g=i(t),n.b=o(t),n+""}}}var Fr=Br(Nr),Ee=Br(Dr);function Yr(n,e){e=e||[];var r,i=n?Math.min(e.length,n.length):0,o=e.slice();return function(t){for(r=0;r<i;++r)o[r]=n[r]*(1-t)+e[r]*t;return o}}function zr(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function Hr(t,n){for(var e=n?n.length:0,r=t?Math.min(e,t.length):0,i=new Array(r),o=new Array(e),a=0;a<r;++a)i[a]=Zr(t[a],n[a]);for(;a<e;++a)o[a]=n[a];return function(t){for(a=0;a<r;++a)o[a]=i[a](t);return o}}function jr(n,e){var r=new Date;return n=+n,e=+e,function(t){return r.setTime(n*(1-t)+e*t),r}}function Vr(n,e){return n=+n,e=+e,function(t){return n*(1-t)+e*t}}function Xr(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),n=null!==n&&"object"==typeof n?n:{})e in t?r[e]=Zr(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var Ur=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Wr=new RegExp(Ur.source,"g");function Gr(t,r){var n,e,i,o,a,u=Ur.lastIndex=Wr.lastIndex=0,c=-1,l=[],s=[];for(t+="",r+="";(n=Ur.exec(t))&&(e=Wr.exec(r));)(i=e.index)>u&&(i=r.slice(u,i),l[c]?l[c]+=i:l[++c]=i),(n=n[0])===(e=e[0])?l[c]?l[c]+=e:l[++c]=e:(l[++c]=null,s.push({i:c,x:Vr(n,e)})),u=Wr.lastIndex;return u<r.length&&(i=r.slice(u),l[c]?l[c]+=i:l[++c]=i),l.length<2?s[0]?(a=s[0].x,function(t){return a(t)+""}):(o=r,function(){return o}):(r=s.length,function(t){for(var n,e=0;e<r;++e)l[(n=s[e]).i]=n.x(t);return l.join("")})}function Zr(t,n){var e=typeof n;return null==n||"boolean"==e?Pr(n):("number"==e?Vr:"string"==e?(e=Be(n))?(n=e,Or):Gr:n instanceof Be?Or:n instanceof Date?jr:zr(n)?Yr:Array.isArray(n)?Hr:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?Xr:Vr)(t,n)}function qr(n,e){return n=+n,e=+e,function(t){return Math.round(n*(1-t)+e*t)}}var $r,Jr=180/Math.PI,Kr={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Qr(t,n,e,r,i,o){var a,u,c;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(u=Math.sqrt(e*e+r*r))&&(e/=u,r/=u,c/=u),t*r<n*e&&(t=-t,n=-n,c=-c,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*Jr,skewX:Math.atan(c)*Jr,scaleX:a,scaleY:u}}function ti(f,h,d,p){function g(t){return t.length?t.pop()+" ":""}return function(t,n){var e,r,i,o,a,u,c,l=[],s=[];return t=f(t),n=f(n),r=t.translateX,e=t.translateY,o=n.translateX,u=n.translateY,a=l,i=s,r!==o||e!==u?(c=a.push("translate(",null,h,null,d),i.push({i:c-4,x:Vr(r,o)},{i:c-2,x:Vr(e,u)})):(o||u)&&a.push("translate("+o+h+u+d),i=t.rotate,r=n.rotate,e=l,a=s,i!==r?(180<i-r?r+=360:180<r-i&&(i+=360),a.push({i:e.push(g(e)+"rotate(",null,p)-2,x:Vr(i,r)})):r&&e.push(g(e)+"rotate("+r+p),o=t.skewX,u=n.skewX,a=l,i=s,o!==u?i.push({i:a.push(g(a)+"skewX(",null,p)-2,x:Vr(o,u)}):u&&a.push(g(a)+"skewX("+u+p),e=t.scaleX,r=t.scaleY,i=n.scaleX,o=n.scaleY,a=l,u=s,e!==i||r!==o?(c=a.push(g(a)+"scale(",null,",",null,")"),u.push({i:c-4,x:Vr(e,i)},{i:c-2,x:Vr(r,o)})):1===i&&1===o||a.push(g(a)+"scale("+i+","+o+")"),t=n=null,function(t){for(var n,e=-1,r=s.length;++e<r;)l[(n=s[e]).i]=n.x(t);return l.join("")}}}var ni=ti(function(t){var n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?Kr:Qr(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),ei=ti(function(t){return null!=t&&(($r=$r||document.createElementNS("http://www.w3.org/2000/svg","g")).setAttribute("transform",t),t=$r.transform.baseVal.consolidate())?Qr((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):Kr},", ",")",")");function ri(t){return((t=Math.exp(t))+1/t)/2}var ii=function r(d,p,g){function t(t,n){var i,o,a,u=t[0],c=t[1],l=t[2],e=n[0],r=n[1],s=n[2],f=e-u,h=r-c,e=f*f+h*h;return(s=e<1e-12?(a=Math.log(s/l)/d,function(t){return[u+t*f,c+t*h,l*Math.exp(d*t*a)]}):(i=Math.sqrt(e),r=(s*s-l*l+g*e)/(2*l*p*i),e=(s*s-l*l-g*e)/(2*s*p*i),o=Math.log(Math.sqrt(r*r+1)-r),a=(Math.log(Math.sqrt(e*e+1)-e)-o)/d,function(t){var n=t*a,e=ri(o),r=l/(p*i)*(e*(t=d*n+o,((t=Math.exp(2*t))-1)/(t+1))-(t=o,((t=Math.exp(o))-1/t)/2));return[u+r*f,c+r*h,l*e/ri(d*n+o)]})).duration=1e3*a*d/Math.SQRT2,s}return t.rho=function(t){var n=Math.max(.001,+t),e=n*n;return r(n,e,e*e)},t}(Math.SQRT2,2,4);function oi(a){return function(n,t){var e=a((n=qe(n)).h,(t=qe(t)).h),r=c(n.s,t.s),i=c(n.l,t.l),o=c(n.opacity,t.opacity);return function(t){return n.h=e(t),n.s=r(t),n.l=i(t),n.opacity=o(t),n+""}}}Se=oi(Ir),Ae=oi(c);function ai(a){return function(n,t){var e=a((n=mr(n)).h,(t=mr(t)).h),r=c(n.c,t.c),i=c(n.l,t.l),o=c(n.opacity,t.opacity);return function(t){return n.h=e(t),n.c=r(t),n.l=i(t),n.opacity=o(t),n+""}}}var ui=ai(Ir),ci=ai(c);function li(u){return function t(a){function n(n,t){var e=u((n=Tr(n)).h,(t=Tr(t)).h),r=c(n.s,t.s),i=c(n.l,t.l),o=c(n.opacity,t.opacity);return function(t){return n.h=e(t),n.s=r(t),n.l=i(Math.pow(t,a)),n.opacity=o(t),n+""}}return a=+a,n.gamma=t,n}(1)}var si=li(Ir),fi=li(c);function hi(t,n){void 0===n&&(n=t,t=Zr);for(var e=0,r=n.length-1,i=n[0],o=new Array(r<0?0:r);e<r;)o[e]=t(i,i=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[n](t-n)}}var di,pi,gi=0,vi=0,mi=0,yi=1e3,bi=0,_i=0,wi=0,xi="object"==typeof performance&&performance.now?performance:Date,Mi="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Ei(){return _i||(Mi(Ai),_i=xi.now()+wi)}function Ai(){_i=0}function Si(){this._call=this._time=this._next=null}function Ti(t,n,e){var r=new Si;return r.restart(t,n,e),r}function ki(){Ei(),++gi;for(var t,n=di;n;)0<=(t=_i-n._time)&&n._call.call(void 0,t),n=n._next;--gi}function Ci(){_i=(bi=xi.now())+wi,gi=vi=0;try{ki()}finally{for(var t,n,e=di,r=1/(gi=0);e;)e=e._call?(r>e._time&&(r=e._time),(t=e)._next):(n=e._next,e._next=null,t?t._next=n:di=n);pi=t,Di(r),_i=0}}function Ni(){var t=xi.now(),n=t-bi;yi<n&&(wi-=n,bi=t)}function Di(t){gi||(vi=vi&&clearTimeout(vi),24<t-_i?(t<1/0&&(vi=setTimeout(Ci,t-xi.now()-wi)),mi=mi&&clearInterval(mi)):(mi||(bi=xi.now(),mi=setInterval(Ni,yi)),gi=1,Mi(Ci)))}function Pi(n,e,t){var r=new Si;return e=null==e?0:+e,r.restart(t=>{r.stop(),n(t+e)},e,t),r}Si.prototype=Ti.prototype={constructor:Si,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?Ei():+e)+(null==n?0:+n),this._next||pi===this||(pi?pi._next=this:di=this,pi=this),this._call=t,this._time=e,Di()},stop:function(){this._call&&(this._call=null,this._time=1/0,Di())}};var Li=dn("start","end","cancel","interrupt"),Ii=[],Ri=0,Oi=1,Bi=2,Fi=3,Yi=4,zi=5,Hi=6;function ji(t,n,e,r,i,o){var a,u,c,l,s,f=t.__transition;if(f){if(e in f)return}else t.__transition={};function h(t){var n,e,r,i;if(c.state!==Oi)return p();for(n in s)if((i=s[n]).name===c.name){if(i.state===Fi)return Pi(h);i.state===Yi?(i.state=Hi,i.timer.stop(),i.on.call("interrupt",a,a.__data__,i.index,i.group),delete s[n]):+n<u&&(i.state=Hi,i.timer.stop(),i.on.call("cancel",a,a.__data__,i.index,i.group),delete s[n])}if(Pi(function(){c.state===Fi&&(c.state=Yi,c.timer.restart(d,c.delay,c.time),d(t))}),c.state=Bi,c.on.call("start",a,a.__data__,c.index,c.group),c.state===Bi){for(c.state=Fi,l=new Array(r=c.tween.length),n=0,e=-1;n<r;++n)(i=c.tween[n].value.call(a,a.__data__,c.index,c.group))&&(l[++e]=i);l.length=e+1}}function d(t){for(var n=t<c.duration?c.ease.call(null,t/c.duration):(c.timer.restart(p),c.state=zi,1),e=-1,r=l.length;++e<r;)l[e].call(a,n);c.state===zi&&(c.on.call("end",a,a.__data__,c.index,c.group),p())}function p(){for(var t in c.state=Hi,c.timer.stop(),delete s[u],s)return;delete a.__transition}a=t,u=e,c={name:n,index:r,group:i,on:Li,tween:Ii,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:Ri},((s=a.__transition)[u]=c).timer=Ti(function(t){c.state=Oi,c.timer.restart(h,c.delay,c.time),c.delay<=t&&h(t-c.delay)},0,c.time)}function Vi(t,n){var e=Ui(t,n);if(e.state>Ri)throw new Error("too late; already scheduled");return e}function Xi(t,n){var e=Ui(t,n);if(e.state>Fi)throw new Error("too late; already running");return e}function Ui(t,n){var e=t.__transition;if(e=e&&e[n])return e;throw new Error("transition not found")}function Wi(t,n){var e,r,i,o=t.__transition,a=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name!==n?a=!1:(r=e.state>Bi&&e.state<zi,e.state=Hi,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]);a&&delete t.__transition}}function Gi(t,n,e){var r=t._id;return t.each(function(){var t=Xi(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return Ui(t,r).value[n]}}function Zi(t,n){var e;return("number"==typeof n?Vr:n instanceof Be?Or:(e=Be(n))?(n=e,Or):Gr)(t,n)}function qi(r,i){var o,a;function t(){var n,e,t=i.apply(this,arguments);return o=t!==a?(a=t)&&(n=r,e=t,function(t){this.setAttributeNS(n.space,n.local,e.call(this,t))}):o}return t._value=i,t}function $i(r,i){var o,a;function t(){var n,e,t=i.apply(this,arguments);return o=t!==a?(a=t)&&(n=r,e=t,function(t){this.setAttribute(n,e.call(this,t))}):o}return t._value=i,t}var Ji=ee.prototype.constructor;function Ki(t){return function(){this.style.removeProperty(t)}}function Qi(i,o,a){var u,c;function t(){var n,e,r,t=o.apply(this,arguments);return u=t!==c?(c=t)&&(n=i,e=t,r=a,function(t){this.style.setProperty(n,e.call(this,t),r)}):u}return t._value=o,t}function to(e){var r,i;function t(){var n,t=e.apply(this,arguments);return r=t!==i?(i=t)&&(n=t,function(t){this.textContent=n.call(this,t)}):r}return t._value=e,t}var no=0;function eo(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function ro(t){return ee().transition(t)}var io=ee.prototype;eo.prototype=ro.prototype={constructor:eo,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=wn(t));for(var r=this._groups,i=r.length,o=new Array(i),a=0;a<i;++a)for(var u,c,l=r[a],s=l.length,f=o[a]=new Array(s),h=0;h<s;++h)(u=l[h])&&(c=t.call(u,u.__data__,h,l))&&("__data__"in u&&(c.__data__=u.__data__),f[h]=c,ji(f[h],n,e,h,f,Ui(u,e)));return new eo(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=En(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var c,l=r[u],s=l.length,f=0;f<s;++f)if(c=l[f]){for(var h,d=t.call(c,c.__data__,f,l),p=Ui(c,e),g=0,v=d.length;g<v;++g)(h=d[g])&&ji(h,n,e,g,d,p);o.push(d),a.push(c)}return new eo(o,a,n,e)},selectChild:io.selectChild,selectChildren:io.selectChildren,filter:function(t){"function"!=typeof t&&(t=An(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,c=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&c.push(o);return new eo(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),a=new Array(r),u=0;u<o;++u)for(var c,l=n[u],s=e[u],f=l.length,h=a[u]=new Array(f),d=0;d<f;++d)(c=l[d]||s[d])&&(h[d]=c);for(;u<r;++u)a[u]=n[u];return new eo(a,this._parents,this._name,this._id)},selection:function(){return new Ji(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++no,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],c=u.length,l=0;l<c;++l)(a=u[l])&&ji(a,t,e,l,u,{time:(a=Ui(a,n)).time+a.delay+a.duration,delay:0,duration:a.duration,ease:a.ease});return new eo(r,this._parents,t,e)},call:io.call,nodes:io.nodes,node:io.node,size:io.size,empty:io.empty,each:io.each,on:function(t,n){var e,r,i,o,a,u,c=this._id;return arguments.length<2?Ui(this.node(),c).on.on(t):this.each((e=c,i=n,u=((r=t)+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return!(t=0<=n?t.slice(0,n):t)||"start"===t})?Vi:Xi,function(){var t=u(this,e),n=t.on;n!==o&&(a=(o=n).copy()).on(r,i),t.on=a}))},attr:function(t,n){var e=yn(t),r="transform"===e?ei:Zi;return this.attrTween(t,"function"==typeof n?(e.local?function(r,i,o){var a,u,c;return function(){var t,n,e=o(this);if(null!=e)return(t=this.getAttributeNS(r.space,r.local))===(n=e+"")?null:t===a&&n===u?c:(u=n,c=i(a=t,e));this.removeAttributeNS(r.space,r.local)}}:function(r,i,o){var a,u,c;return function(){var t,n,e=o(this);if(null!=e)return(t=this.getAttribute(r))===(n=e+"")?null:t===a&&n===u?c:(u=n,c=i(a=t,e));this.removeAttribute(r)}})(e,r,Gi(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(n,e,r){var i,o,a=r+"";return function(){var t=this.getAttributeNS(n.space,n.local);return t===a?null:t===i?o:o=e(i=t,r)}}:function(n,e,r){var i,o,a=r+"";return function(){var t=this.getAttribute(n);return t===a?null:t===i?o:o=e(i=t,r)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var r=yn(t);return this.tween(e,(r.local?qi:$i)(r,n))},style:function(t,n,e){var r,i,o,a,u,c,l,s,f,h,d,p,g,v,m,y,b,_,w,x,M,E,A,S,T,k="transform"==(t+="")?ni:Zi;return null==n?this.styleTween(t,(M=t,E=k,function(){var t=Fn(this,M),n=(this.style.removeProperty(M),Fn(this,M));return t===n?null:t===A&&n===S?T:T=E(A=t,S=n)})).on("end.style."+t,Ki(t)):"function"==typeof n?this.styleTween(t,(y=k,b=Gi(this,"style."+(m=t),n),function(){var t=Fn(this,m),n=b(this),e=n+"";return null==n&&(this.style.removeProperty(m),e=n=Fn(this,m)),t===e?null:t===_&&e===w?x:(w=e,x=y(_=t,n))})).each((l=this._id,v="end."+(g="style."+(s=t)),function(){var t=Xi(this,l),n=t.on,e=null==t.value[g]?p=p||Ki(s):void 0;n===f&&d===e||(h=(f=n).copy()).on(v,d=e),t.on=h})):this.styleTween(t,(r=t,i=k,c=(o=n)+"",function(){var t=Fn(this,r);return t===c?null:t===a?u:u=i(a=t,o)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,Qi(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?(e=Gi(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}));var n,e},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;return this.tween(n,to(t))},remove:function(){return this.on("end.remove",(e=this._id,function(){var t,n=this.parentNode;for(t in this.__transition)if(+t!==e)return;n&&n.removeChild(this)}));var e},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=Ui(this.node(),e).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(i,o){var a,u;return function(){var t=Xi(this,i),n=t.tween;if(n!==a)for(var e=0,r=(u=a=n).length;e<r;++e)if(u[e].name===o){(u=u.slice()).splice(e,1);break}t.tween=u}}:function(o,a,u){var c,l;if("function"!=typeof u)throw new Error;return function(){var t=Xi(this,o),n=t.tween;if(n!==c){l=(c=n).slice();for(var e={name:a,value:u},r=0,i=l.length;r<i;++r)if(l[r].name===a){l[r]=e;break}r===i&&l.push(e)}t.tween=l}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){Vi(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){Vi(this,t).delay=n}})(n,t)):Ui(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){Xi(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){Xi(this,t).duration=n}})(n,t)):Ui(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw new Error;return function(){Xi(this,t).ease=n}}(n,t)):Ui(this.node(),n).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each((n=this._id,e=t,function(){var t=e.apply(this,arguments);if("function"!=typeof t)throw new Error;Xi(this,n).ease=t}));var n,e},end:function(){var i,o,a=this,u=a._id,c=a.size();return new Promise(function(t,n){var e={value:n},r={value:function(){0==--c&&t()}};a.each(function(){var t=Xi(this,u),n=t.on;n!==i&&((o=(i=n).copy())._.cancel.push(e),o._.interrupt.push(e),o._.end.push(r)),t.on=o}),0===c&&t()})},[Symbol.iterator]:io[Symbol.iterator]};function oo(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function ao(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var io=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),uo=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),co=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),lo=Math.PI,so=lo/2;function fo(t){return(1-Math.cos(lo*t))/2}function ho(t){return 1.0009775171065494*(Math.pow(2,-10*t)-.0009765625)}function po(t){return((t*=2)<=1?ho(1-t):2-ho(t-1))/2}function go(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var vo=7.5625;function mo(t){return(t=+t)<4/11?vo*t*t:t<8/11?vo*(t-=6/11)*t+.75:t<10/11?vo*(t-=9/11)*t+.9375:vo*(t-=21/22)*t+63/64}var yo=1.70158,bo=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(yo),_o=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(yo),yo=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(yo),wo=2*Math.PI,xo=function n(e,r){var i=Math.asin(1/(e=Math.max(1,e)))*(r/=wo);function t(t){return e*ho(- --t)*Math.sin((i-t)/r)}return t.amplitude=function(t){return n(t,r*wo)},t.period=function(t){return n(e,t)},t}(1,.3),Mo=function n(e,r){var i=Math.asin(1/(e=Math.max(1,e)))*(r/=wo);function t(t){return 1-e*ho(t=+t)*Math.sin((t+i)/r)}return t.amplitude=function(t){return n(t,r*wo)},t.period=function(t){return n(e,t)},t}(1,.3),Eo=function n(e,r){var i=Math.asin(1/(e=Math.max(1,e)))*(r/=wo);function t(t){return((t=2*t-1)<0?e*ho(-t)*Math.sin((i-t)/r):2-e*ho(t)*Math.sin((i+t)/r))/2}return t.amplitude=function(t){return n(t,r*wo)},t.period=function(t){return n(e,t)},t}(1,.3),Ao={time:null,delay:0,duration:250,ease:ao};ee.prototype.interrupt=function(t){return this.each(function(){Wi(this,t)})},ee.prototype.transition=function(t){var n,e;t=t instanceof eo?(n=t._id,t._name):(n=++no,(e=Ao).time=Ei(),null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],c=u.length,l=0;l<c;++l)(a=u[l])&&ji(a,t,n,l,u,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}(a,n));return new eo(r,this._parents,t,n)};var So=[null];var To=t=>()=>t;function ko(t,{sourceEvent:n,target:e,selection:r,mode:i,dispatch:o}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},selection:{value:r,enumerable:!0,configurable:!0},mode:{value:i,enumerable:!0,configurable:!0},_:{value:o}})}function Co(t){t.preventDefault(),t.stopImmediatePropagation()}var No={name:"drag"},Do={name:"space"},Po={name:"handle"},Lo={name:"center"};let{abs:Io,max:j,min:V}=Math;function Ro(t){return[+t[0],+t[1]]}function Oo(t){return[Ro(t[0]),Ro(t[1])]}var Bo={name:"x",handles:["w","e"].map(Uo),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},Fo={name:"y",handles:["n","s"].map(Uo),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},Yo={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(Uo),input:function(t){return null==t?null:Oo(t)},output:function(t){return t}},zo={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},Ho={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},jo={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},Vo={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},Xo={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function Uo(t){return{type:t}}function Wo(t){return!t.ctrlKey&&!t.button}function Go(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function Zo(){return navigator.maxTouchPoints||"ontouchstart"in this}function qo(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function $o(R){var O,n=Go,B=Wo,e=Zo,F=!0,i=dn("start","brush","end"),r=6;function o(t){var n=t.property("__brush",s).selectAll(".overlay").data([Uo("overlay")]),n=(n.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",zo.overlay).merge(n).each(function(){var t=qo(this).extent;H(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),t.selectAll(".selection").data([Uo("selection")]).enter().append("rect").attr("class","selection").attr("cursor",zo.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges"),t.selectAll(".handle").data(R.handles,function(t){return t.type}));n.exit().remove(),n.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return zo[t.type]}),t.each(Y).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",u).filter(e).on("touchstart.brush",u).on("touchmove.brush",c).on("touchend.brush touchcancel.brush",l).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function Y(){var t=H(this),n=qo(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-r/2:n[0][0]-r/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-r/2:n[0][1]-r/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+r:r}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+r:r})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function z(t,n,e){var r=t.__brush.emitter;return!r||e&&r.clean?new a(t,n,e):r}function a(t,n,e){this.that=t,this.args=n,this.state=t.__brush,this.active=0,this.clean=e}function u(t){var o,i,a,u,c,l,s,f,h,d,p,g,v,m,y,b,_,w,x,M,E,A,S,T,k,C,N,n,e,D,r;function P(t){for(var n of t.changedTouches||[t])for(var e of C)e.identifier===n.identifier&&(e.cur=ue(n,o));var r,i;!S||T||k||1!==C.length||(r=C[0],Io(r.cur[0]-r[0])>Io(r.cur[1]-r[1])?k=!0:T=!0);for(i of C)i.cur&&(i[0]=i.cur[0],i[1]=i.cur[1]);A=!0,Co(t),L(t)}function L(t){var n,e=C[0],r=e.point0;switch(M=e[0]-r[0],E=e[1]-r[1],a){case Do:case No:u&&(M=j(f-h,V(m-y,M)),d=h+M,b=y+M),c&&(E=j(p-g,V(_-w,E)),v=g+E,x=w+E);break;case Po:C[1]?(u&&(d=j(f,V(m,C[0][0])),b=j(f,V(m,C[1][0])),u=1),c&&(v=j(p,V(_,C[0][1])),x=j(p,V(_,C[1][1])),c=1)):(u<0?(M=j(f-h,V(m-h,M)),d=h+M,b=y):0<u&&(M=j(f-y,V(m-y,M)),d=h,b=y+M),c<0?(E=j(p-g,V(_-g,E)),v=g+E,x=w):0<c&&(E=j(p-w,V(_-w,E)),v=g,x=w+E));break;case Lo:u&&(d=j(f,V(m,h-M*u)),b=j(f,V(m,y+M*u))),c&&(v=j(p,V(_,g-E*c)),x=j(p,V(_,w+E*c)))}b<d&&(u*=-1,n=h,h=y,y=n,n=d,d=b,b=n,i in Ho)&&D.attr("cursor",zo[i=Ho[i]]),x<v&&(c*=-1,n=g,g=w,w=n,n=v,v=x,x=n,i in jo)&&D.attr("cursor",zo[i=jo[i]]),l.selection&&(s=l.selection),T&&(d=s[0][0],b=s[1][0]),k&&(v=s[0][1],x=s[1][1]),s[0][0]===d&&s[0][1]===v&&s[1][0]===b&&s[1][1]===x||(l.selection=[[d,v],[b,x]],Y.call(o),N.brush(t,a.name))}function I(t){if(t.stopImmediatePropagation(),t.touches){if(t.touches.length)return;O&&clearTimeout(O),O=setTimeout(function(){O=null},500)}else de(t.view,A),r.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);var n;e.attr("pointer-events","all"),D.attr("cursor",zo.overlay),l.selection&&(s=l.selection),(n=s)[0][0]!==n[1][0]&&n[0][1]!==n[1][1]||(l.selection=null,Y.call(o)),N.end(t,a.name)}O&&!t.touches||B.apply(this,arguments)&&(o=this,i=t.target.__data__.type,a="selection"===(F&&t.metaKey?i="overlay":i)?No:F&&t.altKey?Lo:Po,u=R===Fo?null:Vo[i],c=R===Bo?null:Xo[i],n=(l=qo(o)).extent,s=l.selection,f=n[0][0],p=n[0][1],m=n[1][0],_=n[1][1],E=M=0,S=u&&c&&F&&t.shiftKey,C=Array.from(t.touches||[t],t=>{var n=t.identifier;return(t=ue(t,o)).point0=t.slice(),t.identifier=n,t}),Wi(o),N=z(o,arguments,!0).beforestart(),"overlay"===i?(s&&(A=!0),n=[C[0],C[1]||C[0]],l.selection=s=[[h=R===Fo?f:V(n[0][0],n[1][0]),g=R===Bo?p:V(n[0][1],n[1][1])],[y=R===Fo?m:j(n[0][0],n[1][0]),w=R===Bo?_:j(n[0][1],n[1][1])]],1<C.length&&L(t)):(h=s[0][0],g=s[0][1],y=s[1][0],w=s[1][1]),d=h,v=g,b=y,x=w,e=H(o).attr("pointer-events","none"),D=e.selectAll(".overlay").attr("cursor",zo[i]),t.touches?(N.moved=P,N.ended=I):(r=H(t.view).on("mousemove.brush",P,!0).on("mouseup.brush",I,!0),F&&r.on("keydown.brush",function(t){switch(t.keyCode){case 16:S=u&&c;break;case 18:a===Po&&(u&&(y=b-M*u,h=d+M*u),c&&(w=x-E*c,g=v+E*c),a=Lo,L(t));break;case 32:a!==Po&&a!==Lo||(u<0?y=b-M:0<u&&(h=d-M),c<0?w=x-E:0<c&&(g=v-E),a=Do,D.attr("cursor",zo.selection),L(t));break;default:return}Co(t)},!0).on("keyup.brush",function(t){switch(t.keyCode){case 16:S&&(T=k=S=!1,L(t));break;case 18:a===Lo&&(u<0?y=b:0<u&&(h=d),c<0?w=x:0<c&&(g=v),a=Po,L(t));break;case 32:a===Do&&(a=t.altKey?(u&&(y=b-M*u,h=d+M*u),c&&(w=x-E*c,g=v+E*c),Lo):(u<0?y=b:0<u&&(h=d),c<0?w=x:0<c&&(g=v),Po),D.attr("cursor",zo[i]),L(t));break;default:return}Co(t)},!0),he(t.view)),Y.call(o),N.start(t,a.name))}function c(t){z(this,arguments).moved(t)}function l(t){z(this,arguments).ended(t)}function s(){var t=this.__brush||{selection:null};return t.extent=Oo(n.apply(this,arguments)),t.dim=R,t}return o.move=function(t,u,r){t.tween?t.on("start.brush",function(t){z(this,arguments).beforestart().start(t)}).on("interrupt.brush end.brush",function(t){z(this,arguments).end(t)}).tween("brush",function(){var n=this,e=n.__brush,r=z(n,arguments),t=e.selection,i=R.input("function"==typeof u?u.apply(this,arguments):u,e.extent),o=Zr(t,i);function a(t){e.selection=1===t&&null===i?null:o(t),Y.call(n),r.brush()}return null!==t&&null!==i?a:a(1)}):t.each(function(){var t=arguments,n=this.__brush,e=R.input("function"==typeof u?u.apply(this,t):u,n.extent),t=z(this,t).beforestart();Wi(this),n.selection=null===e?null:e,Y.call(this),t.start(r).brush(r).end(r)})},o.clear=function(t,n){o.move(t,null,n)},a.prototype={beforestart:function(){return 1==++this.active&&((this.state.emitter=this).starting=!0),this},start:function(t,n){return this.starting?(this.starting=!1,this.emit("start",t,n)):this.emit("brush",t),this},brush:function(t,n){return this.emit("brush",t,n),this},end:function(t,n){return 0==--this.active&&(delete this.state.emitter,this.emit("end",t,n)),this},emit:function(t,n,e){var r=H(this.that).datum();i.call(t,this.that,new ko(t,{sourceEvent:n,target:o,selection:R.output(this.state.selection),mode:e,dispatch:i}),r)}},o.extent=function(t){return arguments.length?(n="function"==typeof t?t:To(Oo(t)),o):n},o.filter=function(t){return arguments.length?(B="function"==typeof t?t:To(!!t),o):B},o.touchable=function(t){return arguments.length?(e="function"==typeof t?t:To(!!t),o):e},o.handleSize=function(t){return arguments.length?(r=+t,o):r},o.keyModifiers=function(t){return arguments.length?(F=!!t,o):F},o.on=function(){var t=i.on.apply(i,arguments);return t===i?o:t},o}var Jo=Math.abs,Ko=Math.cos,Qo=Math.sin,t=Math.PI,ta=t/2,na=2*t,ea=Math.max,ra=1e-12;function ia(e,t){return Array.from({length:t-e},(t,n)=>e+n)}function oa(g,n){var v=0,m=null,y=null,b=null;function r(r){var t,i=r.length,o=new Array(i),a=ia(0,i),u=new Array(i*i),c=new Array(i),l=0;r=Float64Array.from({length:i*i},n?(t,n)=>r[n%i][n/i|0]:(t,n)=>r[n/i|0][n%i]);for(let e=0;e<i;++e){let t=0;for(let n=0;n<i;++n)t+=r[e*i+n]+g*r[n*i+e];l+=o[e]=t}t=(l=ea(0,na-v*i)/l)?v:na/i;{let n=0;m&&a.sort((t,n)=>m(o[t],o[n]));for(let e of a){var s=n;if(g){var f,h=ia(1+~i,i).filter(t=>t<0?r[~t*i+e]:r[e*i+t]);y&&h.sort((t,n)=>y(t<0?-r[~t*i+e]:r[e*i+t],n<0?-r[~n*i+e]:r[e*i+n]));for(f of h)f<0?(u[~f*i+e]||(u[~f*i+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=r[~f*i+e]*l,value:r[~f*i+e]}:(u[e*i+f]||(u[e*i+f]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=r[e*i+f]*l,value:r[e*i+f]}}else{var d,p,h=ia(0,i).filter(t=>r[e*i+t]||r[t*i+e]);y&&h.sort((t,n)=>y(r[e*i+t],r[e*i+n]));for(d of h){let t;e<d?(t=u[e*i+d]||(u[e*i+d]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=r[e*i+d]*l,value:r[e*i+d]}:((t=u[d*i+e]||(u[d*i+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=r[e*i+d]*l,value:r[e*i+d]},e===d&&(t.source=t.target)),t.source&&t.target&&t.source.value<t.target.value&&(p=t.source,t.source=t.target,t.target=p)}}c[e]={index:e,startAngle:s,endAngle:n,value:o[e]},n+=t}}return(u=Object.values(u)).groups=c,b?u.sort(b):u}return r.padAngle=function(t){return arguments.length?(v=ea(0,t),r):v},r.sortGroups=function(t){return arguments.length?(m=t,r):m},r.sortSubgroups=function(t){return arguments.length?(y=t,r):y},r.sortChords=function(t){return arguments.length?(null==t?b=null:(e=t,(b=function(t,n){return e(t.source.value+t.target.value,n.source.value+n.target.value)})._=t),r):b&&b._;var e},r}let aa=Math.PI,ua=2*aa,ca=1e-6,la=ua-ca;function sa(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}let fa=class{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?sa:function(t){var n=Math.floor(t);if(!(0<=n))throw new Error("invalid digits: "+t);if(15<n)return sa;let r=10**n;return function(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=Math.round(arguments[n]*r)/r+t[n]}}(t)}moveTo(t,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,n){this._append`L${this._x1=+t},${this._y1=+n}`}quadraticCurveTo(t,n,e,r){this._append`Q${+t},${+n},${this._x1=+e},${this._y1=+r}`}bezierCurveTo(t,n,e,r,i,o){this._append`C${+t},${+n},${+e},${+r},${this._x1=+i},${this._y1=+o}`}arcTo(t,n,e,r,i){if(t=+t,n=+n,e=+e,r=+r,(i=+i)<0)throw new Error("negative radius: "+i);var o,a,u,c,l=this._x1,s=this._y1,f=e-t,h=r-n,d=l-t,p=s-n,g=d*d+p*p;null===this._x1?this._append`M${this._x1=t},${this._y1=n}`:g>ca&&(Math.abs(p*f-h*d)>ca&&i?(u=f*f+h*h,c=(l=e-l)*l+(s=r-s)*s,o=Math.sqrt(u),a=Math.sqrt(g),g=(u=i*Math.tan((aa-Math.acos((u+g-c)/(2*o*a)))/2))/a,c=u/o,Math.abs(g-1)>ca&&this._append`L${t+g*d},${n+g*p}`,this._append`A${i},${i},0,0,${+(d*s<p*l)},${this._x1=t+c*f},${this._y1=n+c*h}`):this._append`L${this._x1=t},${this._y1=n}`)}arc(t,n,e,r,i,o){if(t=+t,n=+n,o=!!o,(e=+e)<0)throw new Error("negative radius: "+e);let a=e*Math.cos(r),u=e*Math.sin(r),c=t+a,l=n+u,s=1^o,f=o?r-i:i-r;null===this._x1?this._append`M${c},${l}`:(Math.abs(this._x1-c)>ca||Math.abs(this._y1-l)>ca)&&this._append`L${c},${l}`,e&&((f=f<0?f%ua+ua:f)>la?this._append`A${e},${e},0,1,${s},${t-a},${n-u}A${e},${e},0,1,${s},${this._x1=c},${this._y1=l}`:f>ca&&this._append`A${e},${e},0,${+(f>=aa)},${s},${this._x1=t+e*Math.cos(i)},${this._y1=n+e*Math.sin(i)}`)}rect(t,n,e,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${e=+e}v${+r}h${-e}Z`}toString(){return this._}};function ha(){return new fa}ha.prototype=fa.prototype;var da=Array.prototype.slice;function pa(t){return function(){return t}}function ga(t){return t.source}function va(t){return t.target}function ma(t){return t.radius}function ya(t){return t.startAngle}function ba(t){return t.endAngle}function _a(){return 0}function wa(){return 10}function xa(c){var l=ga,s=va,f=ma,h=ma,d=ya,p=ba,g=_a,v=null;function n(){var t,n=l.apply(this,arguments),e=s.apply(this,arguments),r=g.apply(this,arguments)/2,i=da.call(arguments),n=+f.apply(this,(i[0]=n,i)),o=d.apply(this,i)-ta,a=p.apply(this,i)-ta,e=+h.apply(this,(i[0]=e,i)),u=d.apply(this,i)-ta,i=p.apply(this,i)-ta;if(v=v||(t=ha()),ra<r&&(Jo(a-o)>2*r+ra?o<a?(o+=r,a-=r):(o-=r,a+=r):o=a=(o+a)/2,Jo(i-u)>2*r+ra?u<i?(u+=r,i-=r):(u-=r,i+=r):u=i=(u+i)/2),v.moveTo(n*Ko(o),n*Qo(o)),v.arc(0,0,n,o,a),o===u&&a===i||(c?(r=e-+c.apply(this,arguments),a=(u+i)/2,v.quadraticCurveTo(0,0,r*Ko(u),r*Qo(u)),v.lineTo(e*Ko(a),e*Qo(a)),v.lineTo(r*Ko(i),r*Qo(i))):(v.quadraticCurveTo(0,0,e*Ko(u),e*Qo(u)),v.arc(0,0,e,u,i))),v.quadraticCurveTo(0,0,n*Ko(o),n*Qo(o)),v.closePath(),t)return v=null,t+""||null}return c&&(n.headRadius=function(t){return arguments.length?(c="function"==typeof t?t:pa(+t),n):c}),n.radius=function(t){return arguments.length?(f=h="function"==typeof t?t:pa(+t),n):f},n.sourceRadius=function(t){return arguments.length?(f="function"==typeof t?t:pa(+t),n):f},n.targetRadius=function(t){return arguments.length?(h="function"==typeof t?t:pa(+t),n):h},n.startAngle=function(t){return arguments.length?(d="function"==typeof t?t:pa(+t),n):d},n.endAngle=function(t){return arguments.length?(p="function"==typeof t?t:pa(+t),n):p},n.padAngle=function(t){return arguments.length?(g="function"==typeof t?t:pa(+t),n):g},n.source=function(t){return arguments.length?(l=t,n):l},n.target=function(t){return arguments.length?(s=t,n):s},n.context=function(t){return arguments.length?(v=null==t?null:t,n):v},n}var Ma=Array.prototype.slice;function Ea(t,n){return t-n}var Aa=t=>()=>t;function Sa(t,n){for(var e,r=-1,i=n.length;++r<i;)if(e=function(t,n){for(var e=n[0],r=n[1],i=-1,o=0,a=t.length,u=a-1;o<a;u=o++){var c=t[o],l=c[0],s=c[1],f=t[u],h=f[0],d=f[1];if(function(t,n,e){var r;return function(t,n,e){return(n[0]-t[0])*(e[1]-t[1])==(e[0]-t[0])*(n[1]-t[1])}(t,n,e)&&function(t,n,e){return t<=n&&n<=e||e<=n&&n<=t}(t[r=+(t[0]===n[0])],e[r],n[r])}(c,f,n))return 0;r<s!=r<d&&e<(h-l)*(r-s)/(d-s)+l&&(i=-i)}return i}(t,n[r]))return e;return 0}function Ta(){}var ka=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function Ca(){var m=1,y=1,r=Dt,b=n;function i(n){var t=r(n);if(Array.isArray(t))t=t.slice().sort(Ea);else{for(var e=et(n,Na),t=Tt(...Nt(e[0],e[1],t),t);t[t.length-1]>=e[1];)t.pop();for(;t[1]<e[0];)t.shift()}return t.map(t=>o(n,t))}function o(n,t){let e=null==t?NaN:+t;if(isNaN(e))throw new Error("invalid value: "+t);var u,c,r,i,o,a,l=[],s=[],f=n,h=e,d=function(t){b(t,n,e),0<function(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r}(t)?l.push([t]):s.push(t)},p=new Array,g=new Array;for(u=c=-1,i=Da(f[0],h),ka[i<<1].forEach(v);++u<m-1;)r=i,i=Da(f[u+1],h),ka[r|i<<1].forEach(v);for(ka[i<<0].forEach(v);++c<y-1;){for(u=-1,i=Da(f[c*m+m],h),o=Da(f[c*m],h),ka[i<<1|o<<2].forEach(v);++u<m-1;)r=i,i=Da(f[c*m+m+u+1],h),a=o,o=Da(f[c*m+u+1],h),ka[r|i<<1|o<<2|a<<3].forEach(v);ka[i|o<<3].forEach(v)}for(u=-1,o=f[c*m]>=h,ka[o<<2].forEach(v);++u<m-1;)a=o,o=Da(f[c*m+u+1],h),ka[o<<2|a<<3].forEach(v);function v(t){var n,e,r=[t[0][0]+u,t[0][1]+c],i=[t[1][0]+u,t[1][1]+c],o=_(r),a=_(i);(n=g[o])?(e=p[a])?(delete g[n.end],delete p[e.start],n===e?(n.ring.push(i),d(n.ring)):p[n.start]=g[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete g[n.end],n.ring.push(i),g[n.end=a]=n):(n=p[a])?(e=g[o])?(delete p[n.start],delete g[e.end],n===e?(n.ring.push(i),d(n.ring)):p[e.start]=g[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete p[n.start],n.ring.unshift(r),p[n.start=o]=n):p[o]=g[a]={start:o,end:a,ring:[r,i]}}return ka[o<<3].forEach(v),s.forEach(function(t){for(var n,e=0,r=l.length;e<r;++e)if(-1!==Sa((n=l[e])[0],t))return void n.push(t)}),{type:"MultiPolygon",value:t,coordinates:l}}function _(t){return 2*t[0]+t[1]*(m+1)*4}function n(t,a,u){t.forEach(function(t){var n=t[0],e=t[1],r=0|n,i=0|e,o=Pa(a[i*m+r]);0<n&&n<m&&r===n&&(t[0]=La(n,Pa(a[i*m+r-1]),o,u)),0<e&&e<y&&i===e&&(t[1]=La(e,Pa(a[(i-1)*m+r]),o,u))})}return i.contour=o,i.size=function(t){if(!arguments.length)return[m,y];var n=Math.floor(t[0]),e=Math.floor(t[1]);if(0<=n&&0<=e)return m=n,y=e,i;throw new Error("invalid size")},i.thresholds=function(t){return arguments.length?(r="function"==typeof t?t:Array.isArray(t)?Aa(Ma.call(t)):Aa(t),i):r},i.smooth=function(t){return arguments.length?(b=t?n:Ta,i):b===n},i}function Na(t){return isFinite(t)?t:NaN}function Da(t,n){return null!=t&&n<=+t}function Pa(t){return null==t||isNaN(t=+t)?-1/0:t}function La(t,n,e,r){var i=r-n,o=e-n,i=isFinite(i)||isFinite(o)?i/o:Math.sign(i)/Math.sign(o);return isNaN(i)?t:t+i-.5}function Ia(t){return t[0]}function Ra(t){return t[1]}function Oa(){return 1}t=11102230246251565e-32;let D=134217729,Ba=(3+8*t)*t;function Fa(t,n,e,r,i){let o,a,u,c,l=n[0],s=r[0],f=0,h=0,d=(s>l==s>-l?(o=l,l=n[++f]):(o=s,s=r[++h]),0);if(f<t&&h<e)for(s>l==s>-l?(a=l+o,u=o-(a-l),l=n[++f]):(a=s+o,u=o-(a-s),s=r[++h]),o=a,0!==u&&(i[d++]=u);f<t&&h<e;)s>l==s>-l?(a=o+l,c=a-o,u=o-(a-c)+(l-c),l=n[++f]):(a=o+s,c=a-o,u=o-(a-c)+(s-c),s=r[++h]),o=a,0!==u&&(i[d++]=u);for(;f<t;)a=o+l,c=a-o,u=o-(a-c)+(l-c),l=n[++f],o=a,0!==u&&(i[d++]=u);for(;h<e;)a=o+s,c=a-o,u=o-(a-c)+(s-c),s=r[++h],o=a,0!==u&&(i[d++]=u);return 0===o&&0!==d||(i[d++]=o),d}function Ya(t){return new Float64Array(t)}let za=(2+12*t)*t,Ha=(9+64*t)*t*t,ja=Ya(4),Va=Ya(8),Xa=Ya(12),Ua=Ya(16),P=Ya(4);function Wa(t,n,e,r,i,o,a){var u,c,l,s,f,h,d,p,g,v,m,y,b=t-i,_=e-i,w=n-o,x=r-o,M=b*x,E=D*b,A=E-(E-b),S=b-A,T=w*_,k=(v=S*(h=x-(f=(E=D*x)-(E-x)))-(M-A*f-S*f-A*h))-(d=v-(m=(S=w-(A=(E=D*w)-(E-w)))*(h=_-(f=(E=D*_)-(E-_)))-(T-A*f-S*f-A*h))),C=(ja[0]=v-(d+k)+(k-m),k=(g=M-((p=M+d)-(k=p-M))+(d-k))-(d=g-T),ja[1]=g-(d+k)+(k-T),k=(y=p+d)-p,ja[2]=p-(y-k)+(d-k),ja[3]=y,function(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}(4,ja)),N=za*a;if(N<=C||N<=-C)return C;if(u=t-(b+(k=t-b))+(k-i),l=e-(_+(k=e-_))+(k-i),c=n-(w+(k=n-w))+(k-o),s=r-(x+(k=r-x))+(k-o),0==u&&0==c&&0==l&&0==s)return C;if((N=Ha*a+Ba*Math.abs(C))<=(C+=b*s+x*u-(w*l+_*c))||N<=-C)return C;M=u*x,T=c*_,k=(v=(S=u-(A=(E=D*u)-(E-u)))*(h=x-(f=(E=D*x)-(E-x)))-(M-A*f-S*f-A*h))-(d=v-(m=(S=c-(A=(E=D*c)-(E-c)))*(h=_-(f=(E=D*_)-(E-_)))-(T-A*f-S*f-A*h))),P[0]=v-(d+k)+(k-m),k=(g=M-((p=M+d)-(k=p-M))+(d-k))-(d=g-T),P[1]=g-(d+k)+(k-T),k=(y=p+d)-p,P[2]=p-(y-k)+(d-k),P[3]=y;N=Fa(4,ja,4,P,Va),M=b*s,T=w*l,k=(v=(S=b-(A=(E=D*b)-(E-b)))*(h=s-(f=(E=D*s)-(E-s)))-(M-A*f-S*f-A*h))-(d=v-(m=(S=w-(A=(E=D*w)-(E-w)))*(h=l-(f=(E=D*l)-(E-l)))-(T-A*f-S*f-A*h))),P[0]=v-(d+k)+(k-m),k=(g=M-((p=M+d)-(k=p-M))+(d-k))-(d=g-T),P[1]=g-(d+k)+(k-T),k=(y=p+d)-p,P[2]=p-(y-k)+(d-k),P[3]=y,C=Fa(N,Va,4,P,Xa),M=u*s,T=c*l,k=(v=(S=u-(A=(E=D*u)-(E-u)))*(h=s-(f=(E=D*s)-(E-s)))-(M-A*f-S*f-A*h))-(d=v-(m=(S=c-(A=(E=D*c)-(E-c)))*(h=l-(f=(E=D*l)-(E-l)))-(T-A*f-S*f-A*h))),P[0]=v-(d+k)+(k-m),k=(g=M-((p=M+d)-(k=p-M))+(d-k))-(d=g-T),P[1]=g-(d+k)+(k-T),k=(y=p+d)-p,P[2]=p-(y-k)+(d-k),P[3]=y,x=Fa(C,Xa,4,P,Ua);return Ua[x-1]}function Ga(t,n,e,r,i,o){var a=(n-o)*(e-i),u=(t-i)*(r-o),c=a-u,a=Math.abs(a+u);return Math.abs(c)>=33306690738754716e-32*a?c:-Wa(t,n,e,r,i,o,a)}let Za=Math.pow(2,-52),qa=new Uint32Array(512);class $a{static from(t,n=function(t){return t[0]},e=function(t){return t[1]}){var r=t.length,i=new Float64Array(2*r);for(let a=0;a<r;a++){var o=t[a];i[2*a]=n(o),i[2*a+1]=e(o)}return new $a(i)}constructor(t){var n=t.length>>1;if(0<n&&"number"!=typeof t[0])throw new Error("Expected coords to contain numbers.");this.coords=t;var e=Math.max(2*n-5,0);this._triangles=new Uint32Array(3*e),this._halfedges=new Int32Array(3*e),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}update(){var{coords:u,_hullPrev:c,_hullNext:l,_hullTri:s,_hullHash:f}=this,h=u.length>>1;let t=1/0,n=1/0,e=-1/0,O=-1/0;for(let P=0;P<h;P++){var r=u[2*P],i=u[2*P+1];r<t&&(t=r),i<n&&(n=i),r>e&&(e=r),i>O&&(O=i),this._ids[P]=P}var B=(t+e)/2,F=(n+O)/2;let d,p,g;for(let L=0,ot=1/0;L<h;L++){var Y=Ja(B,F,u[2*L],u[2*L+1]);Y<ot&&(d=L,ot=Y)}var z,H,j,V,X,o,a,v,m,U,W,G,y=u[2*d],b=u[2*d+1];for(let I=0,at=1/0;I<h;I++)I!==d&&((z=Ja(y,b,u[2*I],u[2*I+1]))<at&&0<z)&&(p=I,at=z);let _=u[2*p],w=u[2*p+1],Z=1/0;for(let R=0;R<h;R++)R!==d&&R!==p&&(H=_,j=w,V=u[2*R],X=u[2*R+1],a=m=G=W=U=m=v=a=o=void 0,(m=(m=((m=X-b)*(U=(o=H-y)*o+(a=j-b)*a)-a*(W=(v=V-y)*v+m*m))*(G=.5/(o*m-a*v)))*m+(a=(o*W-v*U)*G)*a)<Z)&&(g=R,Z=m);let x=u[2*g],M=u[2*g+1];if(Z===1/0){for(let n=0;n<h;n++)this._dists[n]=u[2*n]-u[0]||u[2*n+1]-u[1];Ka(this._ids,this._dists,0,h-1);var q=new Uint32Array(h);let t=0;for(let e=0,r=-1/0;e<h;e++){var $=this._ids[e],J=this._dists[$];J>r&&(q[t++]=$,r=J)}this.hull=q.subarray(0,t),this.triangles=new Uint32Array(0),void(this.halfedges=new Uint32Array(0))}else{Ga(y,b,_,w,x,M)<0&&(T=p,E=_,A=w,p=g,_=x,w=M,g=T,x=E,M=A),K=_,Q=w,tt=x,nt=M;var K,Q,tt,nt,E,A,S,T,et,rt,it,k={x:T=y+((T=nt-b)*(et=(E=K-y)*E+(A=Q-b)*A)-A*(rt=(S=tt-y)*S+T*T))*(it=.5/(E*T-A*S)),y:A=b+(E*rt-S*et)*it};this._cx=k.x,this._cy=k.y;for(let t=0;t<h;t++)this._dists[t]=Ja(u[2*t],u[2*t+1],k.x,k.y);Ka(this._ids,this._dists,0,h-1);let o=3;l[this._hullStart=d]=c[g]=p,l[p]=c[d]=g,s[l[g]=c[p]=d]=0,s[p]=1,s[g]=2,f.fill(-1),f[this._hashKey(y,b)]=d,f[this._hashKey(_,w)]=p,f[this._hashKey(x,M)]=g,this.trianglesLen=0,this._addTriangle(d,p,g,-1,-1,-1);for(let n=0,e,r;n<this._ids.length;n++){var C=this._ids[n],N=u[2*C],D=u[2*C+1];if(!(0<n&&Math.abs(N-e)<=Za&&Math.abs(D-r)<=Za)&&(e=N,r=D,C!==d&&C!==p&&C!==g)){let e=0;for(let t=0,n=this._hashKey(N,D);t<this._hashSize&&(-1===(e=f[(n+t)%this._hashSize])||e===l[e]);t++);let r=e=c[e],i;for(;i=l[r],0<=Ga(N,D,u[2*r],u[2*r+1],u[2*i],u[2*i+1]);)if((r=i)===e){r=-1;break}if(-1!==r){let t=this._addTriangle(r,C,l[r],-1,-1,s[r]),n=(s[C]=this._legalize(t+2),s[r]=t,o++,l[r]);for(;i=l[n],Ga(N,D,u[2*n],u[2*n+1],u[2*i],u[2*i+1])<0;)t=this._addTriangle(n,C,i,s[C],-1,s[n]),s[C]=this._legalize(t+2),l[n]=n,o--,n=i;if(r===e)for(;Ga(N,D,u[2*(i=c[r])],u[2*i+1],u[2*r],u[2*r+1])<0;)t=this._addTriangle(i,C,r,-1,s[r],s[i]),this._legalize(t+2),s[i]=t,l[r]=r,o--,r=i;l[this._hullStart=c[C]=r]=c[n]=C,l[C]=n,f[this._hashKey(N,D)]=C,f[this._hashKey(u[2*r],u[2*r+1])]=r}}}this.hull=new Uint32Array(o);for(let i=0,a=this._hullStart;i<o;i++)a=l[this.hull[i]=a];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}}_hashKey(t,n){return Math.floor((t=t-this._cx,n=n-this._cy,e=t/(Math.abs(t)+Math.abs(n)),(0<n?3-e:1+e)/4*this._hashSize))%this._hashSize;var e}_legalize(n){var t,e,r,i,o,a,u,c,l,s,f,h,{_triangles:d,_halfedges:p,coords:g}=this;let v=0,m=0;for(;;){var y=p[n],b=n-n%3;if(m=b+(n+2)%3,-1===y){if(0===v)break;n=qa[--v]}else{var _=y-y%3,w=_+(y+2)%3,x=d[m],M=d[n],b=d[b+(n+1)%3],E=d[w];if(t=g[2*x],e=g[2*x+1],r=g[2*M],i=g[2*M+1],o=g[2*b],a=g[2*b+1],u=g[2*E],c=g[2*E+1],A=S=h=f=s=l=b=M=void 0,(M=t-u)*((s=i-c)*(A=(f=o-u)*f+(h=a-c)*h)-(S=(l=r-u)*l+s*s)*h)-(b=e-c)*(l*A-S*f)+(M*M+b*b)*(l*h-s*f)<0){d[n]=E,d[y]=x;var A=p[w];if(-1===A){let t=this._hullStart;do{if(this._hullTri[t]===w){this._hullTri[t]=n;break}}while((t=this._hullPrev[t])!==this._hullStart)}this._link(n,A),this._link(y,p[m]),this._link(m,w);var S=_+(y+1)%3;v<qa.length&&(qa[v++]=S)}else{if(0===v)break;n=qa[--v]}}}return m}_link(t,n){-1!==(this._halfedges[t]=n)&&(this._halfedges[n]=t)}_addTriangle(t,n,e,r,i,o){var a=this.trianglesLen;return this._triangles[a]=t,this._triangles[a+1]=n,this._triangles[a+2]=e,this._link(a,r),this._link(a+1,i),this._link(a+2,o),this.trianglesLen+=3,a}}function Ja(t,n,e,r){var i=t-e,o=n-r;return i*i+o*o}function Ka(e,r,i,o){if(o-i<=20)for(let n=i+1;n<=o;n++){var a=e[n],u=r[a];let t=n-1;for(;t>=i&&r[e[t]]>u;)e[t+1]=e[t--];e[t+1]=a}else{let t=i+1,n=o;Qa(e,i+o>>1,t),r[e[i]]>r[e[o]]&&Qa(e,i,o),r[e[t]]>r[e[o]]&&Qa(e,t,o),r[e[i]]>r[e[t]]&&Qa(e,i,t);for(var c=e[t],l=r[c];;){for(;r[e[++t]]<l;);for(;r[e[--n]]>l;);if(n<t)break;Qa(e,t,n)}e[i+1]=e[n],e[n]=c,o-t+1>=n-i?(Ka(e,r,t,o),Ka(e,r,i,n-1)):(Ka(e,r,i,n-1),Ka(e,r,t,o))}}function Qa(t,n,e){var r=t[n];t[n]=t[e],t[e]=r}class tu{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(t,n){this._+=`M${this._x0=this._x1=+t},`+(this._y0=this._y1=+n)}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,n){this._+=`L${this._x1=+t},`+(this._y1=+n)}arc(t,n,e){var r=(t=+t)+(e=+e),i=n=+n;if(e<0)throw new Error("negative radius");null===this._x1?this._+=`M${r},`+i:(1e-6<Math.abs(this._x1-r)||1e-6<Math.abs(this._y1-i))&&(this._+="L"+r+","+i),e&&(this._+=`A${e},${e},0,1,1,${t-e},${n}A${e},${e},0,1,1,${this._x1=r},`+(this._y1=i))}rect(t,n,e,r){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${+e}v${+r}h${-e}Z`}value(){return this._||null}}class nu{constructor(){this._=[]}moveTo(t,n){this._.push([t,n])}closePath(){this._.push(this._[0].slice())}lineTo(t,n){this._.push([t,n])}value(){return this._.length?this._:null}}class eu{constructor(t,[n,e,r,i]=[0,0,960,500]){if(!((r=+r)>=(n=+n)&&(i=+i)>=(e=+e)))throw new Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(2*t.points.length),this.vectors=new Float64Array(2*t.points.length),this.xmax=r,this.xmin=n,this.ymax=i,this.ymin=e,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){var{delaunay:{points:e,hull:r,triangles:i},vectors:t}=this;let o,a;var u=this.circumcenters=this._circumcenters.subarray(0,i.length/3*2);for(let M=0,E=0,A=i.length,S,T;M<A;M+=3,E+=2){var c=2*i[M],l=2*i[M+1],s=2*i[M+2];let t=e[c],n=e[1+c];var c=e[l],l=e[1+l],f=e[s],s=e[1+s],c=c-t,l=l-n,h=f-t,d=s-n,p=2*(c*d-l*h);if(Math.abs(p)<1e-9){if(void 0===o){o=a=0;for(var g of r)o+=e[2*g],a+=e[2*g+1];o/=r.length,a/=r.length}var v=1e9*Math.sign((o-t)*d-(a-n)*h);S=(t+f)/2-v*d,T=(n+s)/2+v*h}else{f=1/p,s=c*c+l*l,v=h*h+d*d;S=t+(d*s-l*v)*f,T=n+(c*v-h*s)*f}u[E]=S,u[E+1]=T}var n=r[r.length-1];let m,y=4*n,b,_=e[2*n],w,x=e[2*n+1];t.fill(0);for(let k=0;k<r.length;++k)n=r[k],m=y,b=_,w=x,y=4*n,_=e[2*n],x=e[2*n+1],t[m+2]=t[y]=w-x,t[m+3]=t[y+1]=_-b}render(t){var n=null==t?t=new tu:void 0,{delaunay:{halfedges:e,inedges:r,hull:i},circumcenters:o,vectors:a}=this;if(i.length<=1)return null;for(let v=0,m=e.length;v<m;++v){var u,c,l,s=e[v];s<v||(c=2*Math.floor(v/3),s=2*Math.floor(s/3),u=o[c],c=o[1+c],l=o[s],s=o[1+s],this._renderSegment(u,c,l,s,t))}let f,h=i[i.length-1];for(let y=0;y<i.length;++y){f=h,h=i[y];var d=2*Math.floor(r[h]/3),p=o[d],d=o[1+d],g=4*f,g=this._project(p,d,a[2+g],a[3+g]);g&&this._renderSegment(p,d,g[0],g[1],t)}return n&&n.value()}renderBounds(t){var n=null==t?t=new tu:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(t,e){var r=null==e?e=new tu:void 0,i=this._clip(t);if(null!==i&&i.length){e.moveTo(i[0],i[1]);let t=i.length;for(;i[0]===i[t-2]&&i[1]===i[t-1]&&1<t;)t-=2;for(let n=2;n<t;n+=2)i[n]===i[n-2]&&i[n+1]===i[n-1]||e.lineTo(i[n],i[n+1]);return e.closePath(),r&&r.value()}}*cellPolygons(){var{points:t}=this["delaunay"];for(let e=0,r=t.length/2;e<r;++e){var n=this.cellPolygon(e);n&&(n.index=e,yield n)}}cellPolygon(t){var n=new nu;return this.renderCell(t,n),n.value()}_renderSegment(t,n,e,r,i){var o=this._regioncode(t,n),a=this._regioncode(e,r);0===o&&0===a?(i.moveTo(t,n),i.lineTo(e,r)):(o=this._clipSegment(t,n,e,r,o,a))&&(i.moveTo(o[0],o[1]),i.lineTo(o[2],o[3]))}contains(t,n,e){return(n=+n)==n&&(e=+e)==e&&this.delaunay._step(t,n,e)===t}*neighbors(t){var i=this._clip(t);if(i)for(var o of this.delaunay.neighbors(t)){var a=this._clip(o);if(a)t:for(let e=0,r=i.length;e<r;e+=2)for(let t=0,n=a.length;t<n;t+=2)if(i[e]===a[t]&&i[e+1]===a[t+1]&&i[(e+2)%r]===a[(t+n-2)%n]&&i[(e+3)%r]===a[(t+n-1)%n]){yield o;break t}}}_cell(t){var{circumcenters:n,delaunay:{inedges:e,halfedges:r,triangles:i}}=this,o=e[t];if(-1===o)return null;var a=[];let u=o;do{var c=Math.floor(u/3);if(a.push(n[2*c],n[2*c+1]),i[u=u%3==2?u-2:u+1]!==t)break}while((u=r[u])!==o&&-1!==u);return a}_clip(t){var n,e,r;return 0===t&&1===this.delaunay.hull.length?[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin]:null===(n=this._cell(t))?null:(e=this["vectors"],this._simplify(e[r=4*t]||e[1+r]?this._clipInfinite(t,n,e[r],e[1+r],e[2+r],e[3+r]):this._clipFinite(t,n)))}_clipFinite(o,t){var n=t.length;let a=null,u,c,l=t[n-2],s=t[n-1],f,h=this._regioncode(l,s),d,p=0;for(let e=0;e<n;e+=2)if(u=l,c=s,l=t[e],s=t[e+1],f=h,h=this._regioncode(l,s),0===f&&0===h)d=p,p=0,a?a.push(l,s):a=[l,s];else{let t,n,e,r,i;if(0===f){if(null===(t=this._clipSegment(u,c,l,s,f,h)))continue;[n,e,r,i]=t}else{if(null===(t=this._clipSegment(l,s,u,c,h,f)))continue;[r,i,n,e]=t,d=p,p=this._edgecode(n,e),d&&p&&this._edge(o,d,p,a,a.length),a?a.push(n,e):a=[n,e]}d=p,p=this._edgecode(r,i),d&&p&&this._edge(o,d,p,a,a.length),a?a.push(r,i):a=[r,i]}if(a)d=p,p=this._edgecode(a[0],a[1]),d&&p&&this._edge(o,d,p,a,a.length);else if(this.contains(o,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return a}_clipSegment(r,i,o,a,u,c){var l=u<c;for(l&&([r,i,o,a,u,c]=[o,a,r,i,c,u]);;){if(0===u&&0===c)return l?[o,a,r,i]:[r,i,o,a];if(u&c)return null;let t,n,e=u||c;8&e?(t=r+(o-r)*(this.ymax-i)/(a-i),n=this.ymax):4&e?(t=r+(o-r)*(this.ymin-i)/(a-i),n=this.ymin):t=2&e?(n=i+(a-i)*(this.xmax-r)/(o-r),this.xmax):(n=i+(a-i)*(this.xmin-r)/(o-r),this.xmin),u?(r=t,i=n,u=this._regioncode(r,i)):(o=t,a=n,c=this._regioncode(o,a))}}_clipInfinite(i,t,o,a,u,c){let l=Array.from(t),s;if((s=this._project(l[0],l[1],o,a))&&l.unshift(s[0],s[1]),(s=this._project(l[l.length-2],l[l.length-1],u,c))&&l.push(s[0],s[1]),l=this._clipFinite(i,l))for(let t=0,n=l.length,e,r=this._edgecode(l[n-2],l[n-1]);t<n;t+=2)e=r,r=this._edgecode(l[t],l[t+1]),e&&r&&(t=this._edge(i,e,r,l,t),n=l.length);else this.contains(i,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(l=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return l}_edge(e,r,t,i,o){for(;r!==t;){let t,n;switch(r){case 5:r=4;continue;case 4:r=6,t=this.xmax,n=this.ymin;break;case 6:r=2;continue;case 2:r=10,t=this.xmax,n=this.ymax;break;case 10:r=8;continue;case 8:r=9,t=this.xmin,n=this.ymax;break;case 9:r=1;continue;case 1:r=5,t=this.xmin,n=this.ymin}i[o]===t&&i[o+1]===n||!this.contains(e,t,n)||(i.splice(o,0,t,n),o+=2)}return o}_project(t,n,e,r){let i=1/0,o,a,u;if(r<0){if(n<=this.ymin)return null;(o=(this.ymin-n)/r)<i&&(u=this.ymin,a=t+(i=o)*e)}else if(0<r){if(n>=this.ymax)return null;(o=(this.ymax-n)/r)<i&&(u=this.ymax,a=t+(i=o)*e)}if(0<e){if(t>=this.xmax)return null;(o=(this.xmax-t)/e)<i&&(a=this.xmax,u=n+(i=o)*r)}else if(e<0){if(t<=this.xmin)return null;(o=(this.xmin-t)/e)<i&&(a=this.xmin,u=n+(i=o)*r)}return[a,u]}_edgecode(t,n){return(t===this.xmin?1:t===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(t,n){return(t<this.xmin?1:t>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(n){if(n&&4<n.length){for(let t=0;t<n.length;t+=2){var e=(t+2)%n.length,r=(t+4)%n.length;(n[t]===n[e]&&n[e]===n[r]||n[t+1]===n[1+e]&&n[1+e]===n[1+r])&&(n.splice(e,2),t-=2)}n.length||(n=null)}return n}}let ru=2*Math.PI,iu=Math.pow;var ou={},au={};function uu(t){return new Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+'] || ""'}).join(",")+"}")}function cu(t){var e=Object.create(null),r=[];return t.forEach(function(t){for(var n in t)n in e||r.push(e[n]=n)}),r}function lu(t,n){var e=t+"",r=e.length;return r<n?new Array(n-r+1).join(0)+e:e}function su(t){var n,e=t.getUTCHours(),r=t.getUTCMinutes(),i=t.getUTCSeconds(),o=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":((n=t.getUTCFullYear())<0?"-"+lu(-n,6):9999<n?"+"+lu(n,6):lu(n,4))+"-"+lu(t.getUTCMonth()+1,2)+"-"+lu(t.getUTCDate(),2)+(o?"T"+lu(e,2)+":"+lu(r,2)+":"+lu(i,2)+"."+lu(o,3)+"Z":i?"T"+lu(e,2)+":"+lu(r,2)+":"+lu(i,2)+"Z":r||e?"T"+lu(e,2)+":"+lu(r,2)+"Z":"")}function fu(r){var n=new RegExp('["'+r+"\n\r]"),f=r.charCodeAt(0);function e(r,t){var n,e=[],i=r.length,o=0,a=0,u=i<=0,c=!1;function l(){if(u)return au;if(c)return c=!1,ou;var t,n,e=o;if(34===r.charCodeAt(e)){for(;o++<i&&34!==r.charCodeAt(o)||34===r.charCodeAt(++o););return(t=o)>=i?u=!0:10===(n=r.charCodeAt(o++))?c=!0:13===n&&(c=!0,10===r.charCodeAt(o))&&++o,r.slice(e+1,t-1).replace(/""/g,'"')}for(;o<i;){if(10===(n=r.charCodeAt(t=o++)))c=!0;else if(13===n)c=!0,10===r.charCodeAt(o)&&++o;else if(n!==f)continue;return r.slice(e,t)}return u=!0,r.slice(e,i)}for(10===r.charCodeAt(i-1)&&--i,13===r.charCodeAt(i-1)&&--i;(n=l())!==au;){for(var s=[];n!==ou&&n!==au;)s.push(n),n=l();t&&null==(s=t(s,a++))||e.push(s)}return e}function i(t,e){return t.map(function(n){return e.map(function(t){return a(n[t])}).join(r)})}function o(t){return t.map(a).join(r)}function a(t){return null==t?"":t instanceof Date?su(t):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,o){var a,u,n=e(t,function(t,n){if(a)return a(t,n-1);var e,r,i;u=t,a=o?(r=o,i=uu(e=t),function(t,n){return r(i(t),n,e)}):uu(t)});return n.columns=u||[],n},parseRows:e,format:function(t,n){return[(n=null==n?cu(t):n).map(a).join(r)].concat(i(t,n)).join("\n")},formatBody:function(t,n){return i(t,n=null==n?cu(t):n).join("\n")},formatRows:function(t){return t.map(o).join("\n")},formatRow:o,formatValue:a}}var t=fu(","),hu=t.parse,du=t.parseRows,pu=t.format,gu=t.formatBody,vu=t.formatRows,mu=t.formatRow,t=t.formatValue,yu=fu("\t"),bu=yu.parse,_u=yu.parseRows,wu=yu.format,xu=yu.formatBody,Mu=yu.formatRows,Eu=yu.formatRow,yu=yu.formatValue;let Au=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours();function Su(t){if(t.ok)return t.blob();throw new Error(t.status+" "+t.statusText)}function Tu(t){if(t.ok)return t.arrayBuffer();throw new Error(t.status+" "+t.statusText)}function ku(t){if(t.ok)return t.text();throw new Error(t.status+" "+t.statusText)}function Cu(t,n){return fetch(t,n).then(ku)}function Nu(r){return function(t,n,e){return 2===arguments.length&&"function"==typeof n&&(e=n,n=void 0),Cu(t,n).then(function(t){return r(t,e)})}}var Du=Nu(hu),Pu=Nu(bu);function Lu(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);if(204!==t.status&&205!==t.status)return t.json()}function Iu(e){return(t,n)=>Cu(t,n).then(t=>(new DOMParser).parseFromString(t,e))}var Ru=Iu("application/xml"),Ou=Iu("text/html"),Bu=Iu("image/svg+xml");function Fu(t,n,e,r){if(!isNaN(n)&&!isNaN(e)){var i,o,a,u,c,l,s,f,h,d=t._root,p={data:r},g=t._x0,v=t._y0,m=t._x1,y=t._y1;if(d){for(;d.length;)if((l=n>=(o=(g+m)/2))?g=o:m=o,(s=e>=(a=(v+y)/2))?v=a:y=a,!(d=(i=d)[f=s<<1|l]))return i[f]=p,t;if(u=+t._x.call(null,d.data),c=+t._y.call(null,d.data),n===u&&e===c)p.next=d,i?i[f]=p:t._root=p;else{for(;i=i?i[f]=new Array(4):t._root=new Array(4),(l=n>=(o=(g+m)/2))?g=o:m=o,(s=e>=(a=(v+y)/2))?v=a:y=a,(f=s<<1|l)==(h=(a<=c)<<1|o<=u););i[h]=d,i[f]=p}}else t._root=p}return t}function Yu(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function zu(t){return t[0]}function Hu(t){return t[1]}function ju(t,n,e){var r=new Vu(null==n?zu:n,null==e?Hu:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function Vu(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function Xu(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var n=ju.prototype=Vu.prototype;function y(t){return function(){return t}}function Uu(t){return 1e-6*(t()-.5)}function Wu(t){return t.x+t.vx}function Gu(t){return t.y+t.vy}function Zu(t){return t.index}function qu(t,n){var e=t.get(n);if(e)return e;throw new Error("node not found: "+n)}n.copy=function(){var t,n,e=new Vu(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(r)if(r.length)for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=Xu(n));else e._root=Xu(r);return e},n.add=function(t){var n=+this._x.call(null,t),e=+this._y.call(null,t);return Fu(this.cover(n,e),n,e,t)},n.addAll=function(t){for(var n,e,r=t.length,i=new Array(r),o=new Array(r),a=1/0,u=1/0,c=-1/0,l=-1/0,s=0;s<r;++s)isNaN(n=+this._x.call(null,e=t[s]))||isNaN(e=+this._y.call(null,e))||((i[s]=n)<a&&(a=n),c<n&&(c=n),(o[s]=e)<u&&(u=e),l<e&&(l=e));if(!(c<a||l<u))for(this.cover(a,u).cover(c,l),s=0;s<r;++s)Fu(this,i[s],o[s],t[s]);return this},n.cover=function(t,n){if(!isNaN(t=+t)&&!isNaN(n=+n)){var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var a,u,c=i-e||1,l=this._root;t<e||i<=t||n<r||o<=n;)switch(u=(n<r)<<1|t<e,(a=new Array(4))[u]=l,l=a,c*=2,u){case 0:i=e+c,o=r+c;break;case 1:e=i-c,o=r+c;break;case 2:i=e+c,r=o-c;break;case 3:e=i-c,r=o-c}this._root&&this._root.length&&(this._root=l)}this._x0=e,this._y0=r,this._x1=i,this._y1=o}return this},n.data=function(){var n=[];return this.visit(function(t){if(!t.length)for(;n.push(t.data),t=t.next;);}),n},n.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},n.find=function(t,n,e){var r,i,o,a,u,c,l,s,f=this._x0,h=this._y0,d=this._x1,p=this._y1,g=[],v=this._root;for(v&&g.push(new Yu(v,f,h,d,p)),null==e?e=1/0:(f=t-e,h=n-e,d=t+e,p=n+e,e*=e);r=g.pop();)!(v=r.node)||(u=r.x0)>d||(c=r.y0)>p||(a=r.x1)<f||(i=r.y1)<h||(v.length?(g.push(new Yu(v[3],o=(u+a)/2,l=(c+i)/2,a,i),new Yu(v[2],u,l,o,i),new Yu(v[1],o,c,a,l),new Yu(v[0],u,c,o,l)),(i=(l<=n)<<1|o<=t)&&(r=g[g.length-1],g[g.length-1]=g[g.length-1-i],g[g.length-1-i]=r)):(c=(a=t-+this._x.call(null,v.data))*a+(u=n-+this._y.call(null,v.data))*u)<e&&(f=t-(l=Math.sqrt(e=c)),h=n-l,d=t+l,p=n+l,s=v.data));return s},n.remove=function(t){if(!isNaN(o=+this._x.call(null,t))&&!isNaN(a=+this._y.call(null,t))){var n,e,r,i,o,a,u,c,l,s,f,h=this._root,d=this._x0,p=this._y0,g=this._x1,v=this._y1;if(h){if(h.length)for(;;){if((c=o>=(u=(d+g)/2))?d=u:g=u,(l=a>=(u=(p+v)/2))?p=u:v=u,!(h=(n=h)[s=l<<1|c]))return this;if(!h.length)break;(n[s+1&3]||n[s+2&3]||n[s+3&3])&&(e=n,f=s)}for(;h.data!==t;)if(!(h=(r=h).next))return this;(i=h.next)&&delete h.next,r?i?r.next=i:delete r.next:n?(i?n[s]=i:delete n[s],(h=n[0]||n[1]||n[2]||n[3])&&h===(n[3]||n[2]||n[1]||n[0])&&!h.length&&(e?e[f]=h:this._root=h)):this._root=i}}return this},n.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},n.root=function(){return this._root},n.size=function(){var n=0;return this.visit(function(t){if(!t.length)for(;++n,t=t.next;);}),n},n.visit=function(t){var n,e,r,i,o,a,u,c=[],l=this._root;for(l&&c.push(new Yu(l,this._x0,this._y0,this._x1,this._y1));o=c.pop();)!t(l=o.node,e=o.x0,r=o.y0,i=o.x1,o=o.y1)&&l.length&&(a=(e+i)/2,u=(r+o)/2,(n=l[3])&&c.push(new Yu(n,a,u,i,o)),(n=l[2])&&c.push(new Yu(n,e,u,a,o)),(n=l[1])&&c.push(new Yu(n,a,r,i,u)),n=l[0])&&c.push(new Yu(n,e,r,a,u));return this},n.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Yu(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i,o,a,u,c,l,s,f=n.node;f.length&&(o=n.x0,a=n.y0,l=(o+(u=n.x1))/2,s=(a+(c=n.y1))/2,(i=f[0])&&e.push(new Yu(i,o,a,l,s)),(i=f[1])&&e.push(new Yu(i,l,a,u,s)),(i=f[2])&&e.push(new Yu(i,o,s,l,c)),i=f[3])&&e.push(new Yu(i,l,s,u,c)),r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},n.x=function(t){return arguments.length?(this._x=t,this):this._x},n.y=function(t){return arguments.length?(this._y=t,this):this._y};let $u=1664525,Ju=1013904223,Ku=4294967296;function Qu(t){return t.x}function tc(t){return t.y}var nc=Math.PI*(3-Math.sqrt(5));function ec(t,n){var e,r;return(e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0?null:[1<(r=t.slice(0,e)).length?r[0]+r.slice(2):r,+t.slice(e+1)]}function rc(t){return(t=ec(Math.abs(t)))?t[1]:NaN}var ic,oc=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ac(t){var n;if(n=oc.exec(t))return new uc({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]});throw new Error("invalid format: "+t)}function uc(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function cc(t,n){var e,r=ec(t,n);return r?(e=r[0],(r=r[1])<0?"0."+new Array(-r).join("0")+e:e.length>r+1?e.slice(0,r+1)+"."+e.slice(r+1):e+new Array(r-e.length+2).join("0")):t+""}ac.prototype=uc.prototype,uc.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var lc={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return 1e21<=Math.abs(t=Math.round(t))?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>cc(100*t,n),r:cc,s:function(t,n){var e,r,i=ec(t,n);return i?(e=i[0],(i=(i=i[1])-(ic=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1)===(r=e.length)?e:r<i?e+new Array(i-r+1).join("0"):0<i?e.slice(0,i)+"."+e.slice(i):"0."+new Array(1-i).join("0")+ec(t,Math.max(0,n+i-1))[0]):t+""},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function sc(t){return t}var fc,hc=Array.prototype.map,dc=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function pc(t){var n,u,c,x=void 0===t.grouping||void 0===t.thousands?sc:(u=hc.call(t.grouping,Number),c=t.thousands+"",function(t,n){for(var e=t.length,r=[],i=0,o=u[0],a=0;0<e&&0<o&&(n<a+o+1&&(o=Math.max(1,n-a)),r.push(t.substring(e-=o,e+o)),!((a+=o+1)>n));)o=u[i=(i+1)%u.length];return r.reverse().join(c)}),r=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",M=void 0===t.decimal?".":t.decimal+"",E=void 0===t.numerals?sc:(n=hc.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),o=void 0===t.percent?"%":t.percent+"",A=void 0===t.minus?"−":t.minus+"",S=void 0===t.nan?"NaN":t.nan+"";function a(t){var l=(t=ac(t)).fill,s=t.align,f=t.sign,n=t.symbol,h=t.zero,d=t.width,p=t.comma,g=t.precision,v=t.trim,m=t.type,y=("n"===m?(p=!0,m="g"):lc[m]||(void 0===g&&(g=12),v=!0,m="g"),(h||"0"===l&&"="===s)&&(h=!0,l="0",s="="),"$"===n?r:"#"===n&&/[boxX]/.test(m)?"0"+m.toLowerCase():""),b="$"===n?i:/[%p]/.test(m)?o:"",_=lc[m],w=/[defgprs%]/.test(m);function e(t){var n,e,r,i=y,o=b;if("c"===m)o=_(t)+o,t="";else{var a=(t=+t)<0||1/t<0;if(t=isNaN(t)?S:_(Math.abs(t),g),v&&(t=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(!+t[r])break t;0<i&&(i=0)}return 0<i?t.slice(0,i)+t.slice(n+1):t}(t)),i=((a=a&&0==+t&&"+"!==f?!1:a)?"("===f?f:A:"-"===f||"("===f?"":f)+i,o=("s"===m?dc[8+ic/3]:"")+o+(a&&"("===f?")":""),w)for(n=-1,e=t.length;++n<e;)if((r=t.charCodeAt(n))<48||57<r){o=(46===r?M+t.slice(n+1):t.slice(n))+o,t=t.slice(0,n);break}}p&&!h&&(t=x(t,1/0));var u=i.length+t.length+o.length,c=u<d?new Array(d-u+1).join(l):"";switch(p&&h&&(t=x(c+t,c.length?d-o.length:1/0),c=""),s){case"<":t=i+t+o+c;break;case"=":t=i+c+t+o;break;case"^":t=c.slice(0,u=c.length>>1)+i+t+o+c.slice(u);break;default:t=c+i+t+o}return E(t)}return g=void 0===g?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),e.toString=function(){return t+""},e}return{format:a,formatPrefix:function(t,n){var e=a(((t=ac(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(rc(n)/3))),i=Math.pow(10,-r),o=dc[8+r/3];return function(t){return e(i*t)+o}}}}function gc(t){return fc=pc(t),u.format=fc.format,u.formatPrefix=fc.formatPrefix,fc}function vc(t){return Math.max(0,-rc(Math.abs(t)))}function mc(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(rc(n)/3)))-rc(Math.abs(t)))}function yc(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,rc(n)-rc(t))+1}u.format=void 0,u.formatPrefix=void 0,gc({thousands:",",grouping:[3],currency:["$",""]});var N=1e-6,bc=1e-12,C=Math.PI,L=C/2,_c=C/4,wc=2*C,I=180/C,R=C/180,O=Math.abs,xc=Math.atan,Mc=Math.atan2,B=Math.cos,Ec=Math.ceil,Ac=Math.exp,Sc=Math.hypot,Tc=Math.log,kc=Math.pow,F=Math.sin,Cc=Math.sign||function(t){return 0<t?1:t<0?-1:0},Y=Math.sqrt,Nc=Math.tan;function Dc(t){return 1<t?0:t<-1?C:Math.acos(t)}function Pc(t){return 1<t?L:t<-1?-L:Math.asin(t)}function Lc(t){return(t=F(t/2))*t}function e(){}function Ic(t,n){t&&Oc.hasOwnProperty(t.type)&&Oc[t.type](t,n)}var Rc={Feature:function(t,n){Ic(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)Ic(e[r].geometry,n)}},Oc={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){Bc(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Bc(e[r],n,0)},Polygon:function(t,n){Fc(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Fc(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)Ic(e[r],n)}};function Bc(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function Fc(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)Bc(t[e],n,1);n.polygonEnd()}function Yc(t,n){t&&Rc.hasOwnProperty(t.type)?Rc[t.type](t,n):Ic(t,n)}var zc,Hc,jc,Vc,Xc,f,Uc,h,Wc,Gc,Zc,qc,$c,Jc,Kc,Qc,tl=new k,nl=new k,el={point:e,lineStart:e,lineEnd:e,polygonStart:function(){tl=new k,el.lineStart=rl,el.lineEnd=il},polygonEnd:function(){var t=+tl;nl.add(t<0?wc+t:t),this.lineStart=this.lineEnd=this.point=e},sphere:function(){nl.add(wc)}};function rl(){el.point=ol}function il(){al(zc,Hc)}function ol(t,n){el.point=al,zc=t,Hc=n,jc=t*=R,Vc=B(n=(n*=R)/2+_c),Xc=F(n)}function al(t,n){var e=(t*=R)-jc,r=0<=e?1:-1,e=r*e,i=B(n=(n*=R)/2+_c),o=F(n),a=Xc*o,u=Vc*i+a*B(e),a=a*r*F(e);tl.add(Mc(a,u)),jc=t,Vc=i,Xc=o}function ul(t){return[Mc(t[1],t[0]),Pc(t[2])]}function cl(t){var n=t[0],e=t[1],r=B(e);return[r*B(n),r*F(n),F(e)]}function ll(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function sl(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function fl(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function hl(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function dl(t){var n=Y(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var pl,gl,vl,ml,yl,bl,_l,wl,xl,Ml,El,Al,Sl,Tl,kl,Cl,Nl={point:Dl,lineStart:Ll,lineEnd:Il,polygonStart:function(){Nl.point=Rl,Nl.lineStart=Ol,Nl.lineEnd=Bl,Jc=new k,el.polygonStart()},polygonEnd:function(){el.polygonEnd(),Nl.point=Dl,Nl.lineStart=Ll,Nl.lineEnd=Il,tl<0?(f=-(h=180),Uc=-(Wc=90)):N<Jc?Wc=90:Jc<-N&&(Uc=-90),Qc[0]=f,Qc[1]=h},sphere:function(){f=-(h=180),Uc=-(Wc=90)}};function Dl(t,n){Kc.push(Qc=[f=t,h=t]),n<Uc&&(Uc=n),Wc<n&&(Wc=n)}function Pl(t,n){var e,r,i,o,a,u=cl([t*R,n*R]);$c?(e=sl($c,u),dl(e=sl([e[1],-e[0],0],e)),e=ul(e),a=t-Gc,i=e[0]*I*(r=0<a?1:-1),(a=180<O(a))^(r*Gc<i&&i<r*t)?(o=e[1]*I,Wc<o&&(Wc=o)):a^(r*Gc<(i=(360+i)%360-180)&&i<r*t)?(o=-e[1]*I)<Uc&&(Uc=o):(n<Uc&&(Uc=n),Wc<n&&(Wc=n)),a?t<Gc?Fl(f,t)>Fl(f,h)&&(h=t):Fl(t,h)>Fl(f,h)&&(f=t):f<=h?(t<f&&(f=t),h<t&&(h=t)):Gc<t?Fl(f,t)>Fl(f,h)&&(h=t):Fl(t,h)>Fl(f,h)&&(f=t)):Kc.push(Qc=[f=t,h=t]),n<Uc&&(Uc=n),Wc<n&&(Wc=n),$c=u,Gc=t}function Ll(){Nl.point=Pl}function Il(){Qc[0]=f,Qc[1]=h,Nl.point=Dl,$c=null}function Rl(t,n){var e;$c?(e=t-Gc,Jc.add(180<O(e)?e+(0<e?360:-360):e)):(Zc=t,qc=n),el.point(t,n),Pl(t,n)}function Ol(){el.lineStart()}function Bl(){Rl(Zc,qc),el.lineEnd(),O(Jc)>N&&(f=-(h=180)),Qc[0]=f,Qc[1]=h,$c=null}function Fl(t,n){return(n-=t)<0?n+360:n}function Yl(t,n){return t[0]-n[0]}function zl(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}var Hl={sphere:e,point:jl,lineStart:Xl,lineEnd:Gl,polygonStart:function(){Hl.lineStart=Zl,Hl.lineEnd=ql},polygonEnd:function(){Hl.lineStart=Xl,Hl.lineEnd=Gl}};function jl(t,n){t*=R;var e=B(n*=R);Vl(e*B(t),e*F(t),F(n))}function Vl(t,n,e){vl+=(t-vl)/++pl,ml+=(n-ml)/pl,yl+=(e-yl)/pl}function Xl(){Hl.point=Ul}function Ul(t,n){t*=R;var e=B(n*=R);Tl=e*B(t),kl=e*F(t),Cl=F(n),Hl.point=Wl,Vl(Tl,kl,Cl)}function Wl(t,n){t*=R;var e=B(n*=R),r=e*B(t),e=e*F(t),i=F(n),o=Mc(Y((o=kl*i-Cl*e)*o+(o=Cl*r-Tl*i)*o+(o=Tl*e-kl*r)*o),Tl*r+kl*e+Cl*i);gl+=o,bl+=o*(Tl+(Tl=r)),_l+=o*(kl+(kl=e)),wl+=o*(Cl+(Cl=i)),Vl(Tl,kl,Cl)}function Gl(){Hl.point=jl}function Zl(){Hl.point=$l}function ql(){Jl(Al,Sl),Hl.point=jl}function $l(t,n){Al=t,Sl=n,t*=R,n*=R,Hl.point=Jl;var e=B(n);Tl=e*B(t),kl=e*F(t),Cl=F(n),Vl(Tl,kl,Cl)}function Jl(t,n){t*=R;var e=B(n*=R),r=e*B(t),e=e*F(t),i=F(n),o=kl*i-Cl*e,a=Cl*r-Tl*i,u=Tl*e-kl*r,c=Sc(o,a,u),l=Pc(c),c=c&&-l/c;xl.add(c*o),Ml.add(c*a),El.add(c*u),gl+=l,bl+=l*(Tl+(Tl=r)),_l+=l*(kl+(kl=e)),wl+=l*(Cl+(Cl=i)),Vl(Tl,kl,Cl)}function Kl(t){return function(){return t}}function Ql(e,r){function t(t,n){return t=e(t,n),r(t[0],t[1])}return e.invert&&r.invert&&(t.invert=function(t,n){return(t=r.invert(t,n))&&e.invert(t[0],t[1])}),t}function ts(t,n){return O(t)>C&&(t-=Math.round(t/wc)*wc),[t,n]}function ns(t,n,e){return(t%=wc)?n||e?Ql(rs(t),is(n,e)):rs(t):n||e?is(n,e):ts}function es(e){return function(t,n){return O(t+=e)>C&&(t-=Math.round(t/wc)*wc),[t,n]}}function rs(t){var n=es(t);return n.invert=es(-t),n}function is(t,n){var a=B(t),u=F(t),c=B(n),l=F(n);function e(t,n){var e=B(n),r=B(t)*e,e=F(t)*e,i=F(n),o=i*a+r*u;return[Mc(e*c-o*l,r*a-i*u),Pc(o*c+e*l)]}return e.invert=function(t,n){var e=B(n),r=B(t)*e,e=F(t)*e,i=F(n),o=i*c-e*l;return[Mc(e*c+i*l,r*a+o*u),Pc(o*a-r*u)]},e}function os(n){function t(t){return(t=n(t[0]*R,t[1]*R))[0]*=I,t[1]*=I,t}return n=ns(n[0]*R,n[1]*R,2<n.length?n[2]*R:0),t.invert=function(t){return(t=n.invert(t[0]*R,t[1]*R))[0]*=I,t[1]*=I,t},t}function as(t,n,e,r,i,o){if(e){var a=B(n),u=F(n),c=r*e;null==i?(i=n+r*wc,o=n-c/2):(i=us(a,i),o=us(a,o),(0<r?i<o:o<i)&&(i+=r*wc));for(var l,s=i;0<r?o<s:s<o;s-=c)l=ul([a,-u*B(s),-u*F(s)]),t.point(l[0],l[1])}}function us(t,n){(n=cl(n))[0]-=t,dl(n);var e=Dc(-n[1]);return((-n[2]<0?-e:e)+wc-N)%wc}function cs(){var r,n=[];return{point:function(t,n,e){r.push([t,n,e])},lineStart:function(){n.push(r=[])},lineEnd:e,rejoin:function(){1<n.length&&n.push(n.pop().concat(n.shift()))},result:function(){var t=n;return n=[],r=null,t}}}function ls(t,n){return O(t[0]-n[0])<N&&O(t[1]-n[1])<N}function ss(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function fs(t,n,e,r,o){var a,i,u=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],i=t[n];if(ls(r,i)){if(!r[2]&&!i[2]){for(o.lineStart(),a=0;a<n;++a)o.point((r=t[a])[0],r[1]);return void o.lineEnd()}i[0]+=2*N}u.push(e=new ss(r,t,null,!0)),c.push(e.o=new ss(r,null,e,!1)),u.push(e=new ss(i,t,null,!1)),c.push(e.o=new ss(i,null,e,!0))}}),u.length){for(c.sort(n),hs(u),hs(c),a=0,i=c.length;a<i;++a)c[a].e=e=!e;for(var l,s,f=u[0];;){for(var h=f,d=!0;h.v;)if((h=h.n)===f)return;l=h.z,o.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(a=0,i=l.length;a<i;++a)o.point((s=l[a])[0],s[1]);else r(h.x,h.n.x,1,o);h=h.n}else{if(d)for(l=h.p.z,a=l.length-1;0<=a;--a)o.point((s=l[a])[0],s[1]);else r(h.x,h.p.x,-1,o);h=h.p}}while(l=(h=h.o).z,d=!d,!h.v);o.lineEnd()}}}function hs(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}function ds(t){return O(t[0])<=C?t[0]:Cc(t[0])*((O(t[0])+C)%wc-C)}function ps(t,n){var e=ds(n),r=n[1],i=F(r),o=[F(e),-B(e),0],a=0,u=0,c=new k;1===i?r=L+N:-1===i&&(r=-L-N);for(var l=0,s=t.length;l<s;++l)if(h=(f=t[l]).length)for(var f,h,d=f[h-1],p=ds(d),g=d[1]/2+_c,v=F(g),m=B(g),y=0;y<h;++y,p=_,v=x,m=w,d=b){var b=f[y],_=ds(b),w=b[1]/2+_c,x=F(w),w=B(w),M=_-p,E=0<=M?1:-1,A=E*M,S=C<A,T=v*x;c.add(Mc(T*E*F(A),m*w+T*B(A))),a+=S?M+E*wc:M,S^e<=p^e<=_&&(dl(T=sl(cl(d),cl(b))),dl(A=sl(o,T)),(E=(S^0<=M?-1:1)*Pc(A[2]))<r||r===E&&(T[0]||T[1]))&&(u+=S^0<=M?1:-1)}return(a<-N||a<N&&c<-bc)^1&u}function gs(v,m,y,b){return function(u){var c,l,s,e=m(u),f=cs(),h=m(f),d=!1,n={point:r,lineStart:i,lineEnd:o,polygonStart:function(){n.point=p,n.lineStart=a,n.lineEnd=g,l=[],c=[]},polygonEnd:function(){n.point=r,n.lineStart=i,n.lineEnd=o,l=Vt(l);var t=ps(c,b);l.length?(d||(u.polygonStart(),d=!0),fs(l,ms,t,y,u)):t&&(d||(u.polygonStart(),d=!0),u.lineStart(),y(null,null,1,u),u.lineEnd()),d&&(u.polygonEnd(),d=!1),l=c=null},sphere:function(){u.polygonStart(),u.lineStart(),y(null,null,1,u),u.lineEnd(),u.polygonEnd()}};function r(t,n){v(t,n)&&u.point(t,n)}function t(t,n){e.point(t,n)}function i(){n.point=t,e.lineStart()}function o(){n.point=r,e.lineEnd()}function p(t,n){s.push([t,n]),h.point(t,n)}function a(){h.lineStart(),s=[]}function g(){p(s[0][0],s[0][1]),h.lineEnd();var t,n,e,r,i=h.clean(),o=f.result(),a=o.length;if(s.pop(),c.push(s),s=null,a)if(1&i){if(0<(n=(e=o[0]).length-1)){for(d||(u.polygonStart(),d=!0),u.lineStart(),t=0;t<n;++t)u.point((r=e[t])[0],r[1]);u.lineEnd()}}else 1<a&&2&i&&o.push(o.pop().concat(o.shift())),l.push(o.filter(vs))}return n}}function vs(t){return 1<t.length}function ms(t,n){return((t=t.x)[0]<0?t[1]-L-N:L-t[1])-((n=n.x)[0]<0?n[1]-L-N:L-n[1])}ts.invert=ts;var ys=gs(function(){return 1},function(s){var f,h=NaN,d=NaN,p=NaN;return{lineStart:function(){s.lineStart(),f=1},point:function(t,n){var e,r,i,o,a,u,c=0<t?C:-C,l=O(t-h);O(l-C)<N?(s.point(h,d=0<(d+n)/2?L:-L),s.point(p,d),s.lineEnd(),s.lineStart(),s.point(c,d),s.point(t,d),f=0):p!==c&&C<=l&&(O(h-p)<N&&(h-=p*N),O(t-c)<N&&(t-=c*N),r=d,o=n,l=F((e=h)-(i=t)),d=O(l)>N?xc((F(r)*(u=B(o))*F(i)-F(o)*(a=B(r))*F(e))/(a*u*l)):(r+o)/2,s.point(p,d),s.lineEnd(),s.lineStart(),s.point(c,d),f=0),s.point(h=t,d=n),p=c},lineEnd:function(){s.lineEnd(),h=d=NaN},clean:function(){return 2-f}}},function(t,n,e,r){var i;{var o;null==t?(i=e*L,r.point(-C,i),r.point(0,i),r.point(C,i),r.point(C,0),r.point(C,-i),r.point(0,-i),r.point(-C,-i),r.point(-C,0),r.point(-C,i)):O(t[0]-n[0])>N?(o=t[0]<n[0]?C:-C,i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)):r.point(n[0],n[1])}},[-C,-L]);function bs(i){var v=B(i),o=2*R,h=0<v,d=O(v)>N;function p(t,n){return B(t)*B(n)>v}function g(t,n,e){var r,i,o,a,u,c,l,s,f=[1,0,0],h=sl(cl(t),cl(n)),d=ll(h,h),p=h[0],g=d-p*p;return g?(r=sl(f,h),fl(f=hl(f,v*d/g),hl(h,-v*p/g)),(g=(h=ll(f,d=r))*h-(p=ll(d,d))*(ll(f,f)-1))<0?void 0:(fl(g=hl(d,(-h-(r=Y(g)))/p),f),g=ul(g),e?(i=t[0],o=n[0],a=t[1],u=n[1],o<i&&(s=i,i=o,o=s),!(l=O((c=o-i)-C)<N)&&u<a&&(s=a,a=u,u=s),(l||c<N?l?0<a+u^g[1]<(O(g[0]-i)<N?a:u):a<=g[1]&&g[1]<=u:C<c^(i<=g[0]&&g[0]<=o))?(fl(s=hl(d,(-h+r)/p),f),[g,ul(s)]):void 0):g)):!e&&t}function m(t,n){var e=h?i:C-i,r=0;return t<-e?r|=1:e<t&&(r|=2),n<-e?r|=4:e<n&&(r|=8),r}return gs(p,function(a){var u,c,l,s,f;return{lineStart:function(){s=l=!1,f=1},point:function(t,n){var e,r=[t,n],i=p(t,n),o=h?i?0:m(t,n):i?m(t+(t<0?C:-C),n):0;!u&&(s=l=i)&&a.lineStart(),i===l||(e=g(u,r))&&!ls(u,e)&&!ls(r,e)||(r[2]=1),i!==l?(f=0,i?(a.lineStart(),e=g(r,u),a.point(e[0],e[1])):(e=g(u,r),a.point(e[0],e[1],2),a.lineEnd()),u=e):d&&u&&h^i&&(o&c||!(e=g(r,u,!0))||(f=0,h?(a.lineStart(),a.point(e[0][0],e[0][1]),a.point(e[1][0],e[1][1]),a.lineEnd()):(a.point(e[1][0],e[1][1]),a.lineEnd(),a.lineStart(),a.point(e[0][0],e[0][1],3)))),!i||u&&ls(u,r)||a.point(r[0],r[1]),u=r,l=i,c=o},lineEnd:function(){l&&a.lineEnd(),u=null},clean:function(){return f|(s&&l)<<1}}},function(t,n,e,r){as(r,i,o,e,t,n)},h?[0,-i]:[-C,i-C])}var _s,ws,xs,Ms,Es=1e9,As=-Es;function Ss(m,y,b,_){function w(t,n){return m<=t&&t<=b&&y<=n&&n<=_}function x(t,n,e,r){var i=0,o=0;if(null==t||(i=a(t,e))!==(o=a(n,e))||u(t,n)<0^0<e)for(;r.point(0===i||3===i?m:b,1<i?_:y),(i=(i+e+4)%4)!==o;);else r.point(n[0],n[1])}function a(t,n){return O(t[0]-m)<N?0<n?0:3:O(t[0]-b)<N?0<n?2:1:O(t[1]-y)<N?0<n?1:0:0<n?3:2}function M(t,n){return u(t.x,n.x)}function u(t,n){var e=a(t,1),r=a(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(r){var i,f,o,a,u,c,l,s,h,d,p,g=r,t=cs(),n={point:e,lineStart:function(){n.point=v,f&&f.push(o=[]);d=!0,h=!1,l=s=NaN},lineEnd:function(){i&&(v(a,u),c&&h&&t.rejoin(),i.push(t.result()));n.point=e,h&&g.lineEnd()},polygonStart:function(){g=t,i=[],f=[],p=!0},polygonEnd:function(){var t=function(){for(var t=0,n=0,e=f.length;n<e;++n)for(var r,i,o=f[n],a=1,u=o.length,c=o[0],l=c[0],s=c[1];a<u;++a)r=l,i=s,c=o[a],l=c[0],s=c[1],i<=_?_<s&&(s-i)*(m-r)<(l-r)*(_-i)&&++t:s<=_&&(l-r)*(_-i)<(s-i)*(m-r)&&--t;return t}(),n=p&&t,e=(i=Vt(i)).length;(n||e)&&(r.polygonStart(),n&&(r.lineStart(),x(null,null,1,r),r.lineEnd()),e&&fs(i,M,t,x,r),r.polygonEnd());g=r,i=f=o=null}};function e(t,n){w(t,n)&&g.point(t,n)}function v(t,n){var e,r,i=w(t,n);f&&o.push([t,n]),d?(a=t,u=n,d=!1,(c=i)&&(g.lineStart(),g.point(t,n))):i&&h?g.point(t,n):!function(t,n,e,r,i,o){var a=t[0],u=t[1],c=0,l=1,s=n[0]-a,f=n[1]-u,h=e-a;if(s||!(0<h)){if(h/=s,s<0){if(h<c)return;h<l&&(l=h)}else if(0<s){if(l<h)return;c<h&&(c=h)}if(h=i-a,s||!(h<0)){if(h/=s,s<0){if(l<h)return;c<h&&(c=h)}else if(0<s){if(h<c)return;h<l&&(l=h)}if(h=r-u,f||!(0<h)){if(h/=f,f<0){if(h<c)return;h<l&&(l=h)}else if(0<f){if(l<h)return;c<h&&(c=h)}if(h=o-u,f||!(h<0)){if(h/=f,f<0){if(l<h)return;c<h&&(c=h)}else if(0<f){if(h<c)return;h<l&&(l=h)}return 0<c&&(t[0]=a+c*s,t[1]=u+c*f),l<1&&(n[0]=a+l*s,n[1]=u+l*f),1}}}}}(e=[l=Math.max(As,Math.min(Es,l)),s=Math.max(As,Math.min(Es,s))],r=[t=Math.max(As,Math.min(Es,t)),n=Math.max(As,Math.min(Es,n))],m,y,b,_)?i&&(g.lineStart(),g.point(t,n),p=!1):(h||(g.lineStart(),g.point(e[0],e[1])),g.point(r[0],r[1]),i||g.lineEnd(),p=!1),l=t,s=n,h=i}return n}}var Ts={sphere:e,point:e,lineStart:function(){Ts.point=Cs,Ts.lineEnd=ks},lineEnd:e,polygonStart:e,polygonEnd:e};function ks(){Ts.point=Ts.lineEnd=e}function Cs(t,n){ws=t*=R,xs=F(n*=R),Ms=B(n),Ts.point=Ns}function Ns(t,n){t*=R;var e=F(n*=R),r=B(n),i=O(t-ws),o=B(i),i=r*F(i),a=Ms*e-xs*r*o,o=xs*e+Ms*r*o;_s.add(Mc(Y(i*i+a*a),o)),ws=t,xs=e,Ms=r}function Ds(t){return _s=new k,Yc(t,Ts),+_s}var Ps=[null,null],Ls={type:"LineString",coordinates:Ps};function Is(t,n){return Ps[0]=t,Ps[1]=n,Ds(Ls)}var Rs={Feature:function(t,n){return Bs(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(Bs(e[r].geometry,n))return!0;return!1}},Os={Sphere:function(){return!0},Point:function(t,n){return Fs(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Fs(e[r],n))return!0;return!1},LineString:function(t,n){return Ys(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Ys(e[r],n))return!0;return!1},Polygon:function(t,n){return zs(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(zs(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(Bs(e[r],n))return!0;return!1}};function Bs(t,n){return!(!t||!Os.hasOwnProperty(t.type))&&Os[t.type](t,n)}function Fs(t,n){return 0===Is(t,n)}function Ys(t,n){for(var e,r,i,o=0,a=t.length;o<a;o++){if(0===(r=Is(t[o],n)))return!0;if(0<o&&0<(i=Is(t[o],t[o-1]))&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<bc*i)return!0;e=r}return!1}function zs(t,n){return!!ps(t.map(Hs),js(n))}function Hs(t){return(t=t.map(js)).pop(),t}function js(t){return[t[0]*R,t[1]*R]}function Vs(t,n,e){var r=Xt(t,n-N,e).concat(n);return function(n){return r.map(function(t){return[n,t]})}}function Xs(t,n,e){var r=Xt(t,n-N,e).concat(n);return function(n){return r.map(function(t){return[t,n]})}}function Us(){var n,e,r,i,o,a,u,c,l,s,f,h,d=10,p=d,g=90,v=360,m=2.5;function y(){return{type:"MultiLineString",coordinates:t()}}function t(){return Xt(Ec(i/g)*g,r,g).map(f).concat(Xt(Ec(c/v)*v,u,v).map(h)).concat(Xt(Ec(e/d)*d,n,d).filter(function(t){return O(t%g)>N}).map(l)).concat(Xt(Ec(a/p)*p,o,p).filter(function(t){return O(t%v)>N}).map(s))}return y.lines=function(){return t().map(function(t){return{type:"LineString",coordinates:t}})},y.outline=function(){return{type:"Polygon",coordinates:[f(i).concat(h(u).slice(1),f(r).reverse().slice(1),h(c).reverse().slice(1))]}},y.extent=function(t){return arguments.length?y.extentMajor(t).extentMinor(t):y.extentMinor()},y.extentMajor=function(t){return arguments.length?(i=+t[0][0],r=+t[1][0],c=+t[0][1],u=+t[1][1],r<i&&(t=i,i=r,r=t),u<c&&(t=c,c=u,u=t),y.precision(m)):[[i,c],[r,u]]},y.extentMinor=function(t){return arguments.length?(e=+t[0][0],n=+t[1][0],a=+t[0][1],o=+t[1][1],n<e&&(t=e,e=n,n=t),o<a&&(t=a,a=o,o=t),y.precision(m)):[[e,a],[n,o]]},y.step=function(t){return arguments.length?y.stepMajor(t).stepMinor(t):y.stepMinor()},y.stepMajor=function(t){return arguments.length?(g=+t[0],v=+t[1],y):[g,v]},y.stepMinor=function(t){return arguments.length?(d=+t[0],p=+t[1],y):[d,p]},y.precision=function(t){return arguments.length?(m=+t,l=Vs(a,o,90),s=Xs(e,n,m),f=Vs(c,u,90),h=Xs(i,r,m),y):m},y.extentMajor([[-180,-90+N],[180,90-N]]).extentMinor([[-180,-80-N],[180,80+N]])}var Ws,Gs,Zs,qs,$s=t=>t,Js=new k,Ks=new k,Qs={point:e,lineStart:e,lineEnd:e,polygonStart:function(){Qs.lineStart=tf,Qs.lineEnd=rf},polygonEnd:function(){Qs.lineStart=Qs.lineEnd=Qs.point=e,Js.add(O(Ks)),Ks=new k},result:function(){var t=Js/2;return Js=new k,t}};function tf(){Qs.point=nf}function nf(t,n){Qs.point=ef,Ws=Zs=t,Gs=qs=n}function ef(t,n){Ks.add(qs*t-Zs*n),Zs=t,qs=n}function rf(){ef(Ws,Gs)}var of=Qs,af=1/0,uf=af,cf=-af,lf=cf;var sf,ff,hf,df,pf={point:function(t,n){t<af&&(af=t);cf<t&&(cf=t);n<uf&&(uf=n);lf<n&&(lf=n)},lineStart:e,lineEnd:e,polygonStart:e,polygonEnd:e,result:function(){var t=[[af,uf],[cf,lf]];return cf=lf=-(uf=af=1/0),t}},gf=0,vf=0,mf=0,yf=0,bf=0,_f=0,wf=0,xf=0,Mf=0,Ef={point:Af,lineStart:Sf,lineEnd:Cf,polygonStart:function(){Ef.lineStart=Nf,Ef.lineEnd=Df},polygonEnd:function(){Ef.point=Af,Ef.lineStart=Sf,Ef.lineEnd=Cf},result:function(){var t=Mf?[wf/Mf,xf/Mf]:_f?[yf/_f,bf/_f]:mf?[gf/mf,vf/mf]:[NaN,NaN];return gf=vf=mf=yf=bf=_f=wf=xf=Mf=0,t}};function Af(t,n){gf+=t,vf+=n,++mf}function Sf(){Ef.point=Tf}function Tf(t,n){Ef.point=kf,Af(hf=t,df=n)}function kf(t,n){var e=t-hf,r=n-df,e=Y(e*e+r*r);yf+=e*(hf+t)/2,bf+=e*(df+n)/2,_f+=e,Af(hf=t,df=n)}function Cf(){Ef.point=Af}function Nf(){Ef.point=Pf}function Df(){Lf(sf,ff)}function Pf(t,n){Ef.point=Lf,Af(sf=hf=t,ff=df=n)}function Lf(t,n){var e=t-hf,r=n-df,e=Y(e*e+r*r);yf+=e*(hf+t)/2,bf+=e*(df+n)/2,_f+=e,wf+=(e=df*t-hf*n)*(hf+t),xf+=e*(df+n),Mf+=3*e,Af(hf=t,df=n)}var If=Ef;function Rf(t){this._context=t}Rf.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,wc)}},result:e};var Of,Bf,Ff,Yf,zf,Hf=new k,jf={point:e,lineStart:function(){jf.point=Vf},lineEnd:function(){Of&&Xf(Bf,Ff),jf.point=e},polygonStart:function(){Of=!0},polygonEnd:function(){Of=null},result:function(){var t=+Hf;return Hf=new k,t}};function Vf(t,n){jf.point=Xf,Bf=Yf=t,Ff=zf=n}function Xf(t,n){Yf-=t,zf-=n,Hf.add(Y(Yf*Yf+zf*zf)),Yf=t,zf=n}var Uf=jf;let Wf,Gf,Zf,qf;class $f{constructor(t){this._append=null==t?Jf:function(t){var n=Math.floor(t);if(!(0<=n))throw new RangeError("invalid digits: "+t);if(15<n)return Jf;if(n!==Wf){let r=10**n;Wf=n,Gf=function(t){let n=1;this._+=t[0];for(var e=t.length;n<e;++n)this._+=Math.round(arguments[n]*r)/r+t[n]}}return Gf}(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:this._append`M${t},${n}`,this._point=1;break;case 1:this._append`L${t},${n}`;break;default:var e,r;this._append`M${t},${n}`,this._radius===Zf&&this._append===Gf||(e=this._radius,r=this._,this._="",this._append`m0,${e}a${e},${e} 0 1,1 0,${-2*e}a${e},${e} 0 1,1 0,${2*e}z`,Zf=e,Gf=this._append,qf=this._,this._=r),this._+=qf}}result(){var t=this._;return this._="",t.length?t:null}}function Jf(t){let n=1;this._+=t[0];for(var e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function Kf(r){return function(t){var n,e=new Qf;for(n in r)e[n]=r[n];return e.stream=t,e}}function Qf(){}function th(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),Yc(e,t.stream(pf)),n(pf.result()),null!=r&&t.clipExtent(r),t}function nh(i,o,t){return th(i,function(t){var n=o[1][0]-o[0][0],e=o[1][1]-o[0][1],r=Math.min(n/(t[1][0]-t[0][0]),e/(t[1][1]-t[0][1])),n=+o[0][0]+(n-r*(t[1][0]+t[0][0]))/2,e=+o[0][1]+(e-r*(t[1][1]+t[0][1]))/2;i.scale(150*r).translate([n,e])},t)}function eh(t,n,e){return nh(t,[[0,0],n],e)}function rh(i,o,t){return th(i,function(t){var n=+o,e=n/(t[1][0]-t[0][0]),n=(n-e*(t[1][0]+t[0][0]))/2,r=-e*t[0][1];i.scale(150*e).translate([n,r])},t)}function ih(i,o,t){return th(i,function(t){var n=+o,e=n/(t[1][1]-t[0][1]),r=-e*t[0][0],n=(n-e*(t[1][1]+t[0][1]))/2;i.scale(150*e).translate([r,n])},t)}Qf.prototype={constructor:Qf,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var oh=16,ah=B(30*R);function uh(t,n){return+n?(T=t,k=n,function(i){var e,r,o,a,u,c,l,s,f,h,d,p,g={point:t,lineStart:n,lineEnd:m,polygonStart:function(){i.polygonStart(),g.lineStart=y},polygonEnd:function(){i.polygonEnd(),g.lineStart=n}};function t(t,n){t=T(t,n),i.point(t[0],t[1])}function n(){s=NaN,g.point=v,i.lineStart()}function v(t,n){var e=cl([t,n]),r=T(t,n);C(s,f,l,h,d,p,s=r[0],f=r[1],l=t,h=e[0],d=e[1],p=e[2],oh,i),i.point(s,f)}function m(){g.point=t,i.lineEnd()}function y(){n(),g.point=b,g.lineEnd=_}function b(t,n){v(e=t,n),r=s,o=f,a=h,u=d,c=p,g.point=v}function _(){C(s,f,l,h,d,p,r,o,e,a,u,c,oh,i),g.lineEnd=m,m()}return g}):(e=t,Kf({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}}));var e,T,k;function C(t,n,e,r,i,o,a,u,c,l,s,f,h,d){var p,g,v,m,y,b,_,w,x,M,E=a-t,A=u-n,S=E*E+A*A;4*k<S&&h--&&(v=o+f,_=Pc(v/=m=Y((p=r+l)*p+(g=i+s)*g+v*v)),y=O(O(v)-1)<N||O(e-c)<N?(e+c)/2:Mc(g,p),b=(_=T(y,_))[0],_=_[1],k<(M=A*(w=b-t)-E*(x=_-n))*M/S||.3<O((E*w+A*x)/S-.5)||r*l+i*s+o*f<ah)&&(C(t,n,e,r,i,o,b,_,y,p/=m,g/=m,v,h,d),d.point(b,_),C(b,_,y,p,g,v,a,u,c,l,s,f,h,d))}}var ch=Kf({point:function(t,n){this.stream.point(t*R,n*R)}});function lh(t,e,r,i,o,n){var a,u,c,l,s,f,h,d,p,g,v,m,y;return n?(f=B(n),h=F(n),d=f*t,p=h*t,g=f/t,v=h/t,m=(h*r-f*e)/t,y=(h*e+f*r)/t,_.invert=function(t,n){return[i*(g*t-v*n+m),o*(y-v*t-g*n)]},_):(a=t,u=e,c=r,l=i,s=o,b.invert=function(t,n){return[(t-u)/a*l,(c-n)/a*s]},b);function b(t,n){return[u+a*(t*=l),c-a*(n*=s)]}function _(t,n){return[d*(t*=i)-p*(n*=o)+e,r-p*t-d*n]}}function sh(t){return fh(function(){return t})()}function fh(t){var n,e,r,i,o,a,u,c,l,s,f=150,h=480,d=250,p=0,g=0,v=0,m=0,y=0,b=0,_=1,w=1,x=null,M=ys,E=null,A=$s,S=.5;function T(t){return c(t[0]*R,t[1]*R)}function k(t){return(t=c.invert(t[0],t[1]))&&[t[0]*I,t[1]*I]}function C(){var t=lh(f,0,0,_,w,b).apply(null,n(p,g)),t=lh(f,h-t[0],d-t[1],_,w,b);return e=ns(v,m,y),u=Ql(n,t),c=Ql(e,u),a=uh(u,S),N()}function N(){return l=s=null,T}return T.stream=function(t){return l&&s===t?l:l=ch((r=e,Kf({point:function(t,n){var e=r(t,n);return this.stream.point(e[0],e[1])}})(M(a(A(s=t))))));var r},T.preclip=function(t){return arguments.length?(M=t,x=void 0,N()):M},T.postclip=function(t){return arguments.length?(A=t,E=r=i=o=null,N()):A},T.clipAngle=function(t){return arguments.length?(M=+t?bs(x=t*R):(x=null,ys),N()):x*I},T.clipExtent=function(t){return arguments.length?(A=null==t?(E=r=i=o=null,$s):Ss(E=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),N()):null==E?null:[[E,r],[i,o]]},T.scale=function(t){return arguments.length?(f=+t,C()):f},T.translate=function(t){return arguments.length?(h=+t[0],d=+t[1],C()):[h,d]},T.center=function(t){return arguments.length?(p=t[0]%360*R,g=t[1]%360*R,C()):[p*I,g*I]},T.rotate=function(t){return arguments.length?(v=t[0]%360*R,m=t[1]%360*R,y=2<t.length?t[2]%360*R:0,C()):[v*I,m*I,y*I]},T.angle=function(t){return arguments.length?(b=t%360*R,C()):b*I},T.reflectX=function(t){return arguments.length?(_=t?-1:1,C()):_<0},T.reflectY=function(t){return arguments.length?(w=t?-1:1,C()):w<0},T.precision=function(t){return arguments.length?(a=uh(u,S=t*t),N()):Y(S)},T.fitExtent=function(t,n){return nh(T,t,n)},T.fitSize=function(t,n){return eh(T,t,n)},T.fitWidth=function(t,n){return rh(T,t,n)},T.fitHeight=function(t,n){return ih(T,t,n)},function(){return n=t.apply(this,arguments),T.invert=n.invert&&k,C()}}function hh(t){var n=0,e=C/3,r=fh(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*R,e=t[1]*R):[n*I,e*I]},i}function dh(t,n){var e,i,o,r=F(t),a=(r+F(n))/2;return O(a)<N?(e=B(t),u.invert=function(t,n){return[t/e,Pc(n*e)]},u):(o=Y(i=1+r*(2*a-r))/a,c.invert=function(t,n){var e=o-n,r=Mc(t,O(e))*Cc(e);return e*a<0&&(r-=C*Cc(t)*Cc(e)),[r/a,Pc((i-(t*t+e*e)*a*a)/(2*a))]},c);function u(t,n){return[t*e,F(n)/e]}function c(t,n){var e=Y(i-2*a*F(n))/a;return[e*F(t*=a),o-e*B(t)]}}function ph(){return hh(dh).scale(155.424).center([0,33.6442])}function gh(){return ph().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function vh(i){return function(t,n){var e=B(t),r=B(n),e=i(e*r);return e===1/0?[2,0]:[e*r*F(t),e*F(n)]}}function mh(o){return function(t,n){var e=Y(t*t+n*n),r=o(e),i=F(r),r=B(r);return[Mc(t*i,e*r),Pc(e&&n*i/e)]}}var yh=vh(function(t){return Y(2/(1+t))});yh.invert=mh(function(t){return 2*Pc(t/2)});var bh=vh(function(t){return(t=Dc(t))&&t/F(t)});function _h(t,n){return[t,Tc(Nc((L+n)/2))]}function wh(e){var r,i,o,a=sh(e),n=a.center,u=a.scale,c=a.translate,l=a.clipExtent,s=null;function f(){var t=C*u(),n=a(os(a.rotate()).invert([0,0]));return l(null==s?[[n[0]-t,n[1]-t],[n[0]+t,n[1]+t]]:e===_h?[[Math.max(n[0]-t,s),r],[Math.min(n[0]+t,i),o]]:[[s,Math.max(n[1]-t,r)],[i,Math.min(n[1]+t,o)]])}return a.scale=function(t){return(arguments.length?(u(t),f):u)()},a.translate=function(t){return(arguments.length?(c(t),f):c)()},a.center=function(t){return(arguments.length?(n(t),f):n)()},a.clipExtent=function(t){return arguments.length?(null==t?s=r=i=o=null:(s=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),f()):null==s?null:[[s,r],[i,o]]},f()}function xh(t){return Nc((L+t)/2)}function Mh(t,n){var e=B(t),o=t===n?F(t):Tc(e/B(n))/Tc(xh(n)/xh(t)),a=e*kc(xh(t),o)/o;return o?(r.invert=function(t,n){var e=a-n,r=Cc(o)*Y(t*t+e*e),i=Mc(t,O(e))*Cc(e);return e*o<0&&(i-=C*Cc(t)*Cc(e)),[i/o,2*xc(kc(a/r,1/o))-L]},r):_h;function r(t,n){0<a?n<-L+N&&(n=-L+N):L-N<n&&(n=L-N);var e=a/kc(xh(n),o);return[e*F(o*t),a-e*B(o*t)]}}function Eh(t,n){return[t,n]}function Ah(t,n){var e=B(t),i=t===n?F(t):(e-B(n))/(n-t),o=e/i+t;return O(i)<N?Eh:(r.invert=function(t,n){var e=o-n,r=Mc(t,O(e))*Cc(e);return e*i<0&&(r-=C*Cc(t)*Cc(e)),[r/i,o-Cc(i)*Y(t*t+e*e)]},r);function r(t,n){var e=o-n,r=i*t;return[e*F(r),o-e*B(r)]}}bh.invert=mh(function(t){return t}),_h.invert=function(t,n){return[t,2*xc(Ac(n))-L]},Eh.invert=Eh;var Sh=1.340264,Th=-.081106,kh=893e-6,Ch=.003796,Nh=Y(3)/2;function Dh(t,n){var e=Pc(Nh*F(n)),r=e*e,i=r*r*r;return[t*B(e)/(Nh*(Sh+3*Th*r+i*(7*kh+9*Ch*r))),e*(Sh+Th*r+i*(kh+Ch*r))]}function Ph(t,n){var e=B(n),r=B(t)*e;return[e*F(t)/r,F(n)/r]}function Lh(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(r*(.003971*e-.001529*r)-.013791)),n*(1.007226+e*(.015085+r*(.028874*e-.044475-.005916*r)))]}function Ih(t,n){return[B(n)*F(t),F(n)]}function Rh(t,n){var e=B(n),r=1+B(t)*e;return[e*F(t)/r,F(n)/r]}function Oh(t,n){return[Tc(Nc((L+n)/2)),-t]}function Bh(t,n){return t.parent===n.parent?1:2}function Fh(t,n){return t+n.x}function Yh(t,n){return Math.max(t,n.y)}function zh(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;0<=--r;)n+=e[r].value;else n=1;t.value=n}function Hh(t,n){t instanceof Map?(t=[void 0,t],void 0===n&&(n=Vh)):void 0===n&&(n=jh);for(var e,r,i,o,a,u=new Wh(t),c=[u];e=c.pop();)if((i=n(e.data))&&(a=(i=Array.from(i)).length))for(e.children=i,o=a-1;0<=o;--o)c.push(r=i[o]=new Wh(i[o])),r.parent=e,r.depth=e.depth+1;return u.eachBefore(Uh)}function jh(t){return t.children}function Vh(t){return Array.isArray(t)?t[1]:null}function Xh(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function Uh(t){for(var n=0;t.height=n,(t=t.parent)&&t.height<++n;);}function Wh(t){this.data=t,this.depth=this.height=0,this.parent=null}function Gh(t){return null==t?null:Zh(t)}function Zh(t){if("function"!=typeof t)throw new Error;return t}function qh(){return 0}function $h(t){return function(){return t}}Dh.invert=function(t,n){for(var e,r=n,i=r*r,o=i*i*i,a=0;a<12&&(o=(i=(r-=e=(r*(Sh+Th*i+o*(kh+Ch*i))-n)/(Sh+3*Th*i+o*(7*kh+9*Ch*i)))*r)*i*i,!(O(e)<bc));++a);return[Nh*t*(Sh+3*Th*i+o*(7*kh+9*Ch*i))/B(r),Pc(F(r)/Nh)]},Ph.invert=mh(xc),Lh.invert=function(t,n){var e=n,r=25;do{var i=e*e,o=i*i}while(e-=o=(e*(1.007226+i*(.015085+o*(.028874*i-.044475-.005916*o)))-n)/(1.007226+i*(.045255+o*(.259866*i-.311325-.005916*11*o))),O(o)>N&&0<--r);return[t/(.8707+(i=e*e)*(i*(i*i*i*(.003971-.001529*i)-.013791)-.131979)),e]},Ih.invert=mh(Pc),Rh.invert=mh(function(t){return 2*xc(t)}),Oh.invert=function(t,n){return[-n,2*xc(Ac(t))-L]},Wh.prototype=Hh.prototype={constructor:Wh,count:function(){return this.eachAfter(zh)},each:function(t,n){let e=-1;for(var r of this)t.call(n,r,++e,this);return this},eachAfter:function(t,n){for(var e,r,i,o=this,a=[o],u=[],c=-1;o=a.pop();)if(u.push(o),e=o.children)for(r=0,i=e.length;r<i;++r)a.push(e[r]);for(;o=u.pop();)t.call(n,o,++c,this);return this},eachBefore:function(t,n){for(var e,r,i,o=[this],a=-1;e=o.pop();)if(t.call(n,e,++a,this),r=e.children)for(i=r.length-1;0<=i;--i)o.push(r[i]);return this},find:function(t,n){let e=-1;for(var r of this)if(t.call(n,r,++e,this))return r},sum:function(i){return this.eachAfter(function(t){for(var n=+i(t.data)||0,e=t.children,r=e&&e.length;0<=--r;)n+=e[r].value;t.value=n})},sort:function(n){return this.eachBefore(function(t){t.children&&t.children.sort(n)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;t=e.pop(),n=r.pop();for(;t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)n=n.parent,r.push(n);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){return Array.from(this)},leaves:function(){var n=[];return this.eachBefore(function(t){t.children||n.push(t)}),n},links:function(){var n=this,e=[];return n.each(function(t){t!==n&&e.push({source:t.parent,target:t})}),e},copy:function(){return Hh(this).eachBefore(Xh)},[Symbol.iterator]:function*(){var t,n,e,r,i,o=[this];do{for(n=o.reverse(),o=[];t=n.pop();)if(yield t,e=t.children)for(r=0,i=e.length;r<i;++r)o.push(e[r])}while(o.length)}};let Jh=1664525,Kh=1013904223,Qh=4294967296;function t0(){let t=1;return()=>(t=(Jh*t+Kh)%Qh)/Qh}function n0(t,n){for(var e,r,i=0,o=(t=function(t,n){let e=t.length,r,i;for(;e;)i=n()*e--|0,r=t[e],t[e]=t[i],t[i]=r;return t}(Array.from(t),n)).length,a=[];i<o;)e=t[i],r&&r0(r,e)?++i:(r=function(t){switch(t.length){case 1:return function(t){return{x:t.x,y:t.y,r:t.r}}(t[0]);case 2:return o0(t[0],t[1]);case 3:return a0(t[0],t[1],t[2])}}(a=function(t,n){var e,r;if(i0(n,t))return[n];for(e=0;e<t.length;++e)if(e0(n,t[e])&&i0(o0(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(e0(o0(t[e],t[r]),n)&&e0(o0(t[e],n),t[r])&&e0(o0(t[r],n),t[e])&&i0(a0(t[e],t[r],n),t))return[t[e],t[r],n];throw new Error}(a,e)),i=0);return r}function e0(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function r0(t,n){var e=t.r-n.r+1e-9*Math.max(t.r,n.r,1),r=n.x-t.x,i=n.y-t.y;return 0<e&&r*r+i*i<e*e}function i0(t,n){for(var e=0;e<n.length;++e)if(!r0(t,n[e]))return;return 1}function o0(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,a=n.y,u=n.r,c=o-e,l=a-r,s=u-i,f=Math.sqrt(c*c+l*l);return{x:(e+o+c/f*s)/2,y:(r+a+l/f*s)/2,r:(f+i+u)/2}}function a0(t,n,e){var r=t.x,i=t.y,o=t.r,a=n.x,u=n.y,c=n.r,l=e.x,s=e.y,f=e.r,h=r-a,d=r-l,p=i-u,g=i-s,v=c-o,m=f-o,y=r*r+i*i-o*o,a=y-a*a-u*u+c*c,u=y-l*l-s*s+f*f,c=d*p-h*g,y=(p*u-g*a)/(2*c)-r,l=(g*v-p*m)/c,s=(d*a-h*u)/(2*c)-i,f=(h*m-d*v)/c,g=l*l+f*f-1,p=2*(o+y*l+s*f),a=y*y+s*s-o*o,u=-(1e-6<Math.abs(g)?(p+Math.sqrt(p*p-4*g*a))/(2*g):a/p);return{x:r+y+l*u,y:i+s+f*u,r:u}}function u0(t,n,e){var r,i,o,a,u=t.x-n.x,c=t.y-n.y,l=u*u+c*c;l?(i=n.r+e.r,a=t.r+e.r,(a*=a)<(i*=i)?(r=(l+a-i)/(2*l),o=Math.sqrt(Math.max(0,a/l-r*r)),e.x=t.x-r*u-o*c,e.y=t.y-r*c+o*u):(r=(l+i-a)/(2*l),o=Math.sqrt(Math.max(0,i/l-r*r)),e.x=n.x+r*u-o*c,e.y=n.y+r*c+o*u)):(e.x=n.x+e.r,e.y=n.y)}function c0(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return 0<e&&r*r+i*i<e*e}function l0(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,e=(n.y*e.r+e.y*n.r)/r;return i*i+e*e}function s0(t){this._=t,this.next=null,this.previous=null}function f0(t,n){if(!(o=(t="object"==typeof(e=t)&&"length"in e?e:Array.from(e)).length))return 0;var e,r,i,o,a,u,c,l,s,f,h,d=t[0];if(d.x=0,d.y=0,!(1<o))return d.r;if(r=t[1],d.x=-r.r,r.x=d.r,r.y=0,!(2<o))return d.r+r.r;u0(r,d,i=t[2]),d=new s0(d),r=new s0(r),i=new s0(i),((d.next=i.previous=r).next=d.previous=i).next=r.previous=d;t:for(c=3;c<o;++c){u0(d._,r._,i=t[c]),i=new s0(i),l=r.next,s=d.previous,f=r._.r,h=d._.r;do{if(f<=h){if(c0(l._,i._)){r=l,(d.next=r).previous=d,--c;continue t}f+=l._.r,l=l.next}else{if(c0(s._,i._)){((d=s).next=r).previous=d,--c;continue t}h+=s._.r,s=s.previous}}while(l!==s.next);for(i.previous=d,i.next=r,d.next=r.previous=r=i,a=l0(d);(i=i.next)!==r;)(u=l0(i))<a&&(d=i,a=u);r=d.next}for(d=[r._],i=r;(i=i.next)!==r;)d.push(i._);for(i=n0(d,n),c=0;c<o;++c)(d=t[c]).x-=i.x,d.y-=i.y;return i.r}function h0(t){return Math.sqrt(t.value)}function d0(n){return function(t){t.children||(t.r=Math.max(0,+n(t)||0))}}function p0(a,u,c){return function(t){if(n=t.children){var n,e,r,i=n.length,o=a(t)*u||0;if(o)for(e=0;e<i;++e)n[e].r+=o;if(r=f0(n,c),o)for(e=0;e<i;++e)n[e].r-=o;t.r=r+o}}}function g0(e){return function(t){var n=t.parent;t.r*=e,n&&(t.x=n.x+e*t.x,t.y=n.y+e*t.y)}}function v0(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function m0(t,n,e,r,i){for(var o,a=t.children,u=-1,c=a.length,l=t.value&&(r-n)/t.value;++u<c;)(o=a[u]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*l}var y0={depth:-1},b0={},_0={};function w0(t){return t.id}function x0(t){return t.parentId}function M0(t){let n=t.length;if(n<2)return"";for(;1<--n&&!E0(t,n););return t.slice(0,n)}function E0(n,e){if("/"===n[e]){let t=0;for(;0<e&&"\\"===n[--e];)++t;if(0==(1&t))return 1}}function A0(t,n){return t.parent===n.parent?1:2}function S0(t){var n=t.children;return n?n[0]:t.t}function T0(t){var n=t.children;return n?n[n.length-1]:t.t}function k0(t,n){this._=t,this.parent=null,this.children=null,this.A=null,(this.a=this).z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}function C0(t,n,e,r,i){for(var o,a=t.children,u=-1,c=a.length,l=t.value&&(i-e)/t.value;++u<c;)(o=a[u]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*l}k0.prototype=Object.create(Wh.prototype);n=(1+Math.sqrt(5))/2;function N0(t,n,e,r,i,o){for(var a,u,c,l,s,f,h,d,p,g,v,m=[],y=n.children,b=0,_=0,w=y.length,x=n.value;b<w;){for(c=i-e,l=o-r;!(s=y[_++].value)&&_<w;);for(v=(f=h=s)*s*(g=Math.max(l/c,c/l)/(x*t)),p=Math.max(h/v,v/f);_<w;++_){if(s+=u=y[_].value,u<f&&(f=u),h<u&&(h=u),v=s*s*g,p<(d=Math.max(h/v,v/f))){s-=u;break}p=d}m.push(a={value:s,dice:c<l,children:y.slice(b,_)}),a.dice?m0(a,e,r,i,x?r+=l*s/x:o):C0(a,e,r,x?e+=c*s/x:i,o),x-=s,b=_}return m}var D0=function n(o){function t(t,n,e,r,i){N0(o,t,n,e,r,i)}return t.ratio=function(t){return n(1<(t=+t)?t:1)},t}(n);n=function n(d){function t(t,n,e,r,i){if((o=t._squarify)&&o.ratio===d)for(var o,a,u,c,l,s=-1,f=o.length,h=t.value;++s<f;){for(u=(a=o[s]).children,c=a.value=0,l=u.length;c<l;++c)a.value+=u[c].value;a.dice?m0(a,n,e,r,h?e+=(i-e)*a.value/h:i):C0(a,n,e,h?n+=(r-n)*a.value/h:r,i),h-=a.value}else t._squarify=o=N0(d,t,n,e,r,i),o.ratio=d}return t.ratio=function(t){return n(1<(t=+t)?t:1)},t}(n);function P0(t,n){return t[0]-n[0]||t[1]-n[1]}function L0(t){var n,e,r,i=t.length,o=[0,1];let a=2,u;for(u=2;u<i;++u){for(;1<a&&(n=t[o[a-2]],e=t[o[a-1]],r=t[u],(e[0]-n[0])*(r[1]-n[1])-(e[1]-n[1])*(r[0]-n[0])<=0);)--a;o[a++]=u}return o.slice(0,a)}var r=Math.random,I0=function t(e){function n(t,n){return t=null==t?0:+t,n=null==n?1:+n,1===arguments.length?(n=t,t=0):n-=t,function(){return e()*n+t}}return n.source=t,n}(r),R0=function t(e){function n(t,n){return arguments.length<2&&(n=t,t=0),t=Math.floor(t),n=Math.floor(n)-t,function(){return Math.floor(e()*n+t)}}return n.source=t,n}(r),O0=function t(o){function n(n,e){var r,i;return n=null==n?0:+n,e=null==e?1:+e,function(){var t;if(null!=r)t=r,r=null;else for(;r=2*o()-1,t=2*o()-1,!(i=r*r+t*t)||1<i;);return n+e*t*Math.sqrt(-2*Math.log(i)/i)}}return n.source=t,n}(r),B0=function t(n){var e=O0.source(n);function r(){var t=e.apply(this,arguments);return function(){return Math.exp(t())}}return r.source=t,r}(r),F0=function t(r){function n(e){return(e=+e)<=0?()=>0:function(){for(var t=0,n=e;1<n;--n)t+=r();return t+n*r()}}return n.source=t,n}(r),Y0=function t(e){var r=F0.source(e);function n(t){var n;return 0==(t=+t)?e:(n=r(t),function(){return n()/t})}return n.source=t,n}(r),z0=function t(n){function e(t){return function(){return-Math.log1p(-n())/t}}return e.source=t,e}(r),H0=function t(n){function e(t){if((t=+t)<0)throw new RangeError("invalid alpha");return t=1/-t,function(){return Math.pow(1-n(),t)}}return e.source=t,e}(r),j0=function t(n){function e(t){if((t=+t)<0||1<t)throw new RangeError("invalid p");return function(){return Math.floor(n()+t)}}return e.source=t,e}(r),V0=function t(n){function e(t){if((t=+t)<0||1<t)throw new RangeError("invalid p");return 0===t?()=>1/0:1===t?()=>1:(t=Math.log1p(-t),function(){return 1+Math.floor(Math.log1p(-n())/t)})}return e.source=t,e}(r),X0=function t(u){var c=O0.source(u)();function n(t,r){if((t=+t)<0)throw new RangeError("invalid k");var i,o,a;return 0===t?()=>0:(r=null==r?1:+r,1===t?()=>-Math.log1p(-u())*r:(i=(t<1?t+1:t)-1/3,o=1/(3*Math.sqrt(i)),a=t<1?()=>Math.pow(u(),1/t):()=>1,function(){do{do{var t=c(),n=1+o*t}while(n<=0);n*=n*n;var e=1-u()}while(1-.0331*t*t*t*t<=e&&Math.log(e)>=.5*t*t+i*(1-n+Math.log(n)));return i*n*a()*r}))}return n.source=t,n}(r),U0=function t(n){var i=X0.source(n);function e(t,n){var e=i(t),r=i(n);return function(){var t=e();return 0===t?0:t/(t+r())}}return e.source=t,e}(r),W0=function t(n){var f=V0.source(n),h=U0.source(n);function e(l,s){return l=+l,1<=(s=+s)?()=>l:s<=0?()=>0:function(){for(var t=0,n=l,e=s;16<n*e&&16<n*(1-e);){var r=Math.floor((n+1)*e),i=h(r,n-r+1)();i<=e?(t+=r,n-=r,e=(e-i)/(1-i)):(n=r-1,e/=i)}for(var o=e<.5,a=f(o?e:1-e),u=a(),c=0;u<=n;++c)u+=a();return t+(o?c:n-c)}}return e.source=t,e}(r),G0=function t(i){function n(n,t,e){var r=0==(n=+n)?t=>-Math.log(t):(n=1/n,t=>Math.pow(t,n));return t=null==t?0:+t,e=null==e?1:+e,function(){return t+e*r(-Math.log1p(-i()))}}return n.source=t,n}(r),Z0=function t(e){function n(t,n){return t=null==t?0:+t,n=null==n?1:+n,function(){return t+n*Math.tan(Math.PI*e())}}return n.source=t,n}(r),q0=function t(r){function n(n,e){return n=null==n?0:+n,e=null==e?1:+e,function(){var t=r();return n+e*Math.log(t/(1-t))}}return n.source=t,n}(r),r=function t(u){var c=X0.source(u),l=W0.source(u);function n(a){return function(){for(var t=0,n=a;16<n;){var e=Math.floor(.875*n),r=c(e)();if(n<r)return t+l(e-1,n/r)();t+=e,n-=r}for(var i=-Math.log1p(-u()),o=0;i<=n;++o)i-=Math.log1p(-u());return t+o}}return n.source=t,n}(r);let $0=1/4294967296;function J0(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function K0(t,n){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof n?this.interpolator(n):this.range(n)}return this}let Q0=Symbol("implicit");function t1(){var e=new rt,r=[],i=[],o=Q0;function a(t){let n=e.get(t);if(void 0===n){if(o!==Q0)return o;e.set(t,n=r.push(t)-1)}return i[n%i.length]}return a.domain=function(t){if(!arguments.length)return r.slice();r=[],e=new rt;for(var n of t)e.has(n)||e.set(n,r.push(n)-1);return a},a.range=function(t){return arguments.length?(i=Array.from(t),a):i.slice()},a.unknown=function(t){return arguments.length?(o=t,a):o},a.copy=function(){return t1(r,i).unknown(o)},J0.apply(a,arguments),a}function n1(){var i,o,t=t1().unknown(void 0),a=t.domain,u=t.range,c=0,l=1,s=!1,f=0,h=0,d=.5;function n(){var t=a().length,n=l<c,e=n?l:c,r=n?c:l,r=(i=(r-e)/Math.max(1,t-f+2*h),s&&(i=Math.floor(i)),e+=(r-e-i*(t-f))*d,o=i*(1-f),s&&(e=Math.round(e),o=Math.round(o)),Xt(t).map(function(t){return e+i*t}));return u(n?r.reverse():r)}return delete t.unknown,t.domain=function(t){return(arguments.length?(a(t),n):a)()},t.range=function(t){return arguments.length?([c,l]=t,c=+c,l=+l,n()):[c,l]},t.rangeRound=function(t){return[c,l]=t,c=+c,l=+l,s=!0,n()},t.bandwidth=function(){return o},t.step=function(){return i},t.round=function(t){return arguments.length?(s=!!t,n()):s},t.padding=function(t){return arguments.length?(f=Math.min(1,h=+t),n()):f},t.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),n()):f},t.paddingOuter=function(t){return arguments.length?(h=+t,n()):h},t.align=function(t){return arguments.length?(d=Math.max(0,Math.min(1,t)),n()):d},t.copy=function(){return n1(a(),[c,l]).round(s).paddingInner(f).paddingOuter(h).align(d)},J0.apply(n(),arguments)}function e1(t){return+t}var r1=[0,1];function i1(t){return t}function o1(n,e){return(e-=n=+n)?function(t){return(t-n)/e}:(t=isNaN(e)?NaN:.5,function(){return t});var t}function a1(t,n,e){var r=t[0],i=t[1],o=n[0],a=n[1],o=i<r?(r=o1(i,r),e(a,o)):(r=o1(r,i),e(o,a));return function(t){return o(r(t))}}function u1(e,t,n){var r=Math.min(e.length,t.length)-1,i=new Array(r),o=new Array(r),a=-1;for(e[r]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<r;)i[a]=o1(e[a],e[a+1]),o[a]=n(t[a],t[a+1]);return function(t){var n=A(e,t,1,r)-1;return o[n](i[n](t))}}function c1(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function l1(){var e,r,n,i,o,a,u=r1,c=r1,l=Zr,s=i1;function f(){var n,e,t,r=Math.min(u.length,c.length);return s!==i1&&(n=u[0],(e=u[r-1])<n&&(t=n,n=e,e=t),s=function(t){return Math.max(n,Math.min(e,t))}),i=2<r?u1:a1,o=a=null,h}function h(t){return null==t||isNaN(t=+t)?n:(o=o||i(u.map(e),c,l))(e(s(t)))}return h.invert=function(t){return s(r((a=a||i(c,u.map(e),Vr))(t)))},h.domain=function(t){return arguments.length?(u=Array.from(t,e1),f()):u.slice()},h.range=function(t){return arguments.length?(c=Array.from(t),f()):c.slice()},h.rangeRound=function(t){return c=Array.from(t),l=qr,f()},h.clamp=function(t){return arguments.length?(s=!!t||i1,f()):s!==i1},h.interpolate=function(t){return arguments.length?(l=t,f()):l},h.unknown=function(t){return arguments.length?(n=t,h):n},function(t,n){return e=t,r=n,f()}}function s1(){return l1()(i1,i1)}function f1(t,n,e,r){var i,o=Ct(t,n,e);switch((r=ac(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(i=mc(o,a))||(r.precision=i),u.formatPrefix(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=yc(o,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=vc(o))||(r.precision=i-2*("%"===r.type))}return u.format(r)}function h1(l){var s=l.domain;return l.ticks=function(t){var n=s();return Tt(n[0],n[n.length-1],null==t?10:t)},l.tickFormat=function(t,n){var e=s();return f1(e[0],e[e.length-1],null==t?10:t,n)},l.nice=function(t){null==t&&(t=10);var n,e,r=s(),i=0,o=r.length-1,a=r[i],u=r[o],c=10;for(u<a&&(e=a,a=u,u=e,e=i,i=o,o=e);0<c--;){if((e=kt(a,u,t))===n)return r[i]=a,r[o]=u,s(r);if(0<e)a=Math.floor(a/e)*e,u=Math.ceil(u/e)*e;else{if(!(e<0))break;a=Math.ceil(a*e)/e,u=Math.floor(u*e)/e}n=e}return l},l}function d1(t,n){var e,r=0,i=(t=t.slice()).length-1,o=t[r],a=t[i];return a<o&&(e=r,r=i,i=e,e=o,o=a,a=e),t[r]=n.floor(o),t[i]=n.ceil(a),t}function p1(t){return Math.log(t)}function g1(t){return Math.exp(t)}function v1(t){return-Math.log(-t)}function m1(t){return-Math.exp(-t)}function y1(t){return isFinite(t)?+("1e"+t):t<0?0:t}function b1(e){return(t,n)=>-e(-t,n)}function _1(t){let i=t(p1,g1),s=i.domain,f=10,h,d;function n(){var n,e;return h=(e=f)===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e),d=10===(n=f)?y1:n===Math.E?Math.exp:t=>Math.pow(n,t),s()[0]<0?(h=b1(h),d=b1(d),t(v1,m1)):t(p1,g1),i}return i.base=function(t){return arguments.length?(f=+t,n()):f},i.domain=function(t){return(arguments.length?(s(t),n):s)()},i.ticks=t=>{var n=s();let e=n[0],r=n[n.length-1];n=r<e;n&&([e,r]=[r,e]);let i=h(e),o=h(r),a,u;var c=null==t?10:+t;let l=[];if(!(f%1)&&o-i<c){if(i=Math.floor(i),o=Math.ceil(o),0<e){for(;i<=o;++i)for(a=1;a<f;++a)if(!((u=i<0?a/d(-i):a*d(i))<e)){if(u>r)break;l.push(u)}}else for(;i<=o;++i)for(a=f-1;1<=a;--a)if(!((u=0<i?a/d(-i):a*d(i))<e)){if(u>r)break;l.push(u)}2*l.length<c&&(l=Tt(e,r,c))}else l=Tt(i,o,Math.min(o-i,c)).map(d);return n?l.reverse():l},i.tickFormat=(t,e)=>{if(null==t&&(t=10),"function"!=typeof(e=null==e?10===f?"s":",":e)&&(f%1||null!=(e=ac(e)).precision||(e.trim=!0),e=u.format(e)),t===1/0)return e;let r=Math.max(1,f*t/i.ticks().length);return t=>{let n=t/d(Math.round(h(t)));return n*f<f-.5&&(n*=f),n<=r?e(t):""}},i.nice=()=>s(d1(s(),{floor:t=>d(Math.floor(h(t))),ceil:t=>d(Math.ceil(h(t)))})),i}function w1(n){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/n))}}function x1(n){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*n}}function M1(n){var e=1,t=n(w1(e),x1(e));return t.constant=function(t){return arguments.length?n(w1(e=+t),x1(e)):e},h1(t)}function E1(n){return function(t){return t<0?-Math.pow(-t,n):Math.pow(t,n)}}function A1(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function S1(t){return t<0?-t*t:t*t}function T1(n){var t=n(i1,i1),e=1;return t.exponent=function(t){return arguments.length?1===(e=+t)?n(i1,i1):.5===e?n(A1,S1):n(E1(e),E1(1/e)):e},h1(t)}function k1(){var t=T1(l1());return t.copy=function(){return c1(t,k1()).exponent(t.exponent())},J0.apply(t,arguments),t}function C1(t){return Math.sign(t)*t*t}let N1=new Date,D1=new Date;function i(o,a,e,r){function u(t){return o(t=0===arguments.length?new Date:new Date(+t)),t}return u.floor=t=>(o(t=new Date(+t)),t),u.ceil=t=>(o(t=new Date(t-1)),a(t,1),o(t),t),u.round=t=>{var n=u(t),e=u.ceil(t);return t-n<e-t?n:e},u.offset=(t,n)=>(a(t=new Date(+t),null==n?1:Math.floor(n)),t),u.range=(t,n,e)=>{var r,i=[];if(t=u.ceil(t),e=null==e?1:Math.floor(e),t<n&&0<e)for(;i.push(r=new Date(+t)),a(t,e),o(t),r<t&&t<n;);return i},u.filter=e=>i(t=>{if(t<=t)for(;o(t),!e(t);)t.setTime(t-1)},(t,n)=>{if(t<=t)if(n<0)for(;++n<=0;)for(;a(t,-1),!e(t););else for(;0<=--n;)for(;a(t,1),!e(t););}),e&&(u.count=(t,n)=>(N1.setTime(+t),D1.setTime(+n),o(N1),o(D1),Math.floor(e(N1,D1))),u.every=n=>(n=Math.floor(n),isFinite(n)&&0<n?1<n?u.filter(r?t=>r(t)%n==0:t=>u.count(0,t)%n==0):u:null)),u}let P1=i(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);P1.every=e=>(e=Math.floor(e),isFinite(e)&&0<e?1<e?i(t=>{t.setTime(Math.floor(t/e)*e)},(t,n)=>{t.setTime(+t+n*e)},(t,n)=>(n-t)/e):P1:null);var L1=P1.range;let I1=1e3,R1=6e4,O1=36e5,B1=864e5,F1=7*B1,Y1=(B1,365*B1),z1=i(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+n*I1)},(t,n)=>(n-t)/I1,t=>t.getUTCSeconds());var H1=z1.range;let j1=i(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*I1)},(t,n)=>{t.setTime(+t+n*R1)},(t,n)=>(n-t)/R1,t=>t.getMinutes());var V1=j1.range;let X1=i(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+n*R1)},(t,n)=>(n-t)/R1,t=>t.getUTCMinutes());var U1=X1.range;let W1=i(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*I1-t.getMinutes()*R1)},(t,n)=>{t.setTime(+t+n*O1)},(t,n)=>(n-t)/O1,t=>t.getHours());var G1=W1.range;let Z1=i(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+n*O1)},(t,n)=>(n-t)/O1,t=>t.getUTCHours());var q1=Z1.range;let $1=i(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*R1)/B1,t=>t.getDate()-1);var J1=$1.range;let K1=i(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/B1,t=>t.getUTCDate()-1);var Q1=K1.range,td=i(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/B1,t=>Math.floor(t/B1)),nd=td.range;function ed(n){return i(t=>{t.setDate(t.getDate()-(t.getDay()+7-n)%7),t.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+7*n)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*R1)/F1)}let rd=ed(0),id=ed(1);var od=ed(2),ad=ed(3);let ud=ed(4);var cd=ed(5),ld=ed(6),sd=rd.range,fd=id.range,hd=od.range,dd=ad.range,pd=ud.range,gd=cd.range,vd=ld.range;function md(n){return i(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-n)%7),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+7*n)},(t,n)=>(n-t)/F1)}let yd=md(0),bd=md(1);var _d=md(2),wd=md(3);let xd=md(4);var Md=md(5),Ed=md(6),Ad=yd.range,Sd=bd.range,Td=_d.range,kd=wd.range,Cd=xd.range,Nd=Md.range,Dd=Ed.range;let Pd=i(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear()),t=>t.getMonth());var Ld=Pd.range;let Id=i(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth());var Rd=Id.range;let Od=i(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());Od.every=e=>isFinite(e=Math.floor(e))&&0<e?i(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n*e)}):null;var Bd=Od.range;let Fd=i(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());Fd.every=e=>isFinite(e=Math.floor(e))&&0<e?i(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)}):null;var Yd=Fd.range;function zd(o,t,n,e,r,i){let a=[[z1,1,I1],[z1,5,5e3],[z1,15,15e3],[z1,30,3e4],[i,1,R1],[i,5,3e5],[i,15,9e5],[i,30,18e5],[r,1,O1],[r,3,3*O1],[r,6,6*O1],[r,12,432e5],[e,1,B1],[e,2,2*B1],[n,1,F1],[t,1,2592e6],[t,3,7776e6],[o,1,Y1]];function u(t,n,e){var r=Math.abs(n-t)/e,i=v(([,,t])=>t).right(a,r);return i===a.length?o.every(Ct(t/Y1,n/Y1,e)):0===i?P1.every(Math.max(Ct(t,n,e),1)):([r,i]=a[r/a[i-1][2]<a[i][2]/r?i-1:i],r.every(i))}return[function(t,n,e){var r=n<t;r&&([t,n]=[n,t]);var i=(i=e&&"function"==typeof e.range?e:u(t,n,e))?i.range(t,+n+1):[];return r?i.reverse():i},u]}let[Hd,jd]=zd(Fd,Id,yd,td,Z1,X1),[Vd,Xd]=zd(Od,Pd,rd,$1,W1,j1);function Ud(t){var n;return 0<=t.y&&t.y<100?((n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L)).setFullYear(t.y),n):new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Wd(t){var n;return 0<=t.y&&t.y<100?((n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L))).setUTCFullYear(t.y),n):new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Gd(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}function Zd(t){var r=t.dateTime,i=t.date,o=t.time,n=t.periods,e=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=tp(n),s=np(n),f=tp(e),h=np(e),d=tp(a),p=np(a),g=tp(u),v=np(u),m=tp(c),y=np(c),b={a:function(t){return a[t.getDay()]},A:function(t){return e[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:xp,e:xp,f:Tp,g:Fp,G:zp,H:Mp,I:Ep,j:Ap,L:Sp,m:kp,M:Cp,p:function(t){return n[+(12<=t.getHours())]},q:function(t){return 1+~~(t.getMonth()/3)},Q:lg,s:sg,S:Np,u:Dp,U:Pp,V:Ip,w:Rp,W:Op,x:null,X:null,y:Bp,Y:Yp,Z:Hp,"%":cg},_={a:function(t){return a[t.getUTCDay()]},A:function(t){return e[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:jp,e:jp,f:Gp,g:ig,G:ag,H:Vp,I:Xp,j:Up,L:Wp,m:Zp,M:qp,p:function(t){return n[+(12<=t.getUTCHours())]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:lg,s:sg,S:$p,u:Jp,U:Kp,V:tg,w:ng,W:eg,x:null,X:null,y:rg,Y:og,Z:ug,"%":cg},w={a:function(t,n,e){var r=d.exec(n.slice(e));return r?(t.w=p.get(r[0].toLowerCase()),e+r[0].length):-1},A:function(t,n,e){var r=f.exec(n.slice(e));return r?(t.w=h.get(r[0].toLowerCase()),e+r[0].length):-1},b:function(t,n,e){var r=m.exec(n.slice(e));return r?(t.m=y.get(r[0].toLowerCase()),e+r[0].length):-1},B:function(t,n,e){var r=g.exec(n.slice(e));return r?(t.m=v.get(r[0].toLowerCase()),e+r[0].length):-1},c:function(t,n,e){return E(t,r,n,e)},d:hp,e:hp,f:yp,g:cp,G:up,H:pp,I:pp,j:dp,L:mp,m:fp,M:gp,p:function(t,n,e){var r=l.exec(n.slice(e));return r?(t.p=s.get(r[0].toLowerCase()),e+r[0].length):-1},q:sp,Q:_p,s:wp,S:vp,u:rp,U:ip,V:op,w:ep,W:ap,x:function(t,n,e){return E(t,i,n,e)},X:function(t,n,e){return E(t,o,n,e)},y:cp,Y:up,Z:lp,"%":bp};function x(c,l){return function(t){var n,e,r,i=[],o=-1,a=0,u=c.length;for(t instanceof Date||(t=new Date(+t));++o<u;)37===c.charCodeAt(o)&&(i.push(c.slice(a,o)),null!=(e=$d[n=c.charAt(++o)])?n=c.charAt(++o):e="e"===n?" ":"0",(r=l[n])&&(n=r(t,e)),i.push(n),a=o+1);return i.push(c.slice(a,o)),i.join("")}}function M(i,o){return function(t){var n,e,r=Gd(1900,void 0,1);if(E(r,i,t+="",0)!=t.length)return null;if("Q"in r)return new Date(r.Q);if("s"in r)return new Date(1e3*r.s+("L"in r?r.L:0));if(!o||"Z"in r||(r.Z=0),"p"in r&&(r.H=r.H%12+12*r.p),void 0===r.m&&(r.m="q"in r?r.q:0),"V"in r){if(r.V<1||53<r.V)return null;"w"in r||(r.w=1),"Z"in r?(n=4<(e=(n=Wd(Gd(r.y,0,1))).getUTCDay())||0===e?bd.ceil(n):bd(n),n=K1.offset(n,7*(r.V-1)),r.y=n.getUTCFullYear(),r.m=n.getUTCMonth(),r.d=n.getUTCDate()+(r.w+6)%7):(n=4<(e=(n=Ud(Gd(r.y,0,1))).getDay())||0===e?id.ceil(n):id(n),n=$1.offset(n,7*(r.V-1)),r.y=n.getFullYear(),r.m=n.getMonth(),r.d=n.getDate()+(r.w+6)%7)}else("W"in r||"U"in r)&&("w"in r||(r.w="u"in r?r.u%7:"W"in r?1:0),e="Z"in r?Wd(Gd(r.y,0,1)).getUTCDay():Ud(Gd(r.y,0,1)).getDay(),r.m=0,r.d="W"in r?(r.w+6)%7+7*r.W-(e+5)%7:r.w+7*r.U-(e+6)%7);return("Z"in r?(r.H+=r.Z/100|0,r.M+=r.Z%100,Wd):Ud)(r)}}function E(t,n,e,r){for(var i,o,a=0,u=n.length,c=e.length;a<u;){if(c<=r)return-1;if(37===(i=n.charCodeAt(a++))){if(i=n.charAt(a++),!(o=w[i in $d?n.charAt(a++):i])||(r=o(t,e,r))<0)return-1}else if(i!=e.charCodeAt(r++))return-1}return r}return b.x=x(i,b),b.X=x(o,b),b.c=x(r,b),_.x=x(i,_),_.X=x(o,_),_.c=x(r,_),{format:function(t){var n=x(t+="",b);return n.toString=function(){return t},n},parse:function(t){var n=M(t+="",!1);return n.toString=function(){return t},n},utcFormat:function(t){var n=x(t+="",_);return n.toString=function(){return t},n},utcParse:function(t){var n=M(t+="",!0);return n.toString=function(){return t},n}}}var qd,$d={"-":"",_:" ",0:"0"},o=/^\s*\d+/,Jd=/^%/,Kd=/[\\^$*+?|[\]().{}]/g;function a(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?new Array(e-o+1).join(n)+i:i)}function Qd(t){return t.replace(Kd,"\\$&")}function tp(t){return new RegExp("^(?:"+t.map(Qd).join("|")+")","i")}function np(t){return new Map(t.map((t,n)=>[t.toLowerCase(),n]))}function ep(t,n,e){var r=o.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function rp(t,n,e){var r=o.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function ip(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function op(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function ap(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function up(t,n,e){var r=o.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function cp(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(68<+r[0]?1900:2e3),e+r[0].length):-1}function lp(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function sp(t,n,e){var r=o.exec(n.slice(e,e+1));return r?(t.q=3*r[0]-3,e+r[0].length):-1}function fp(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function hp(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function dp(t,n,e){var r=o.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function pp(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function gp(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function vp(t,n,e){var r=o.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function mp(t,n,e){var r=o.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function yp(t,n,e){var r=o.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function bp(t,n,e){var r=Jd.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function _p(t,n,e){var r=o.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function wp(t,n,e){var r=o.exec(n.slice(e));return r?(t.s=+r[0],e+r[0].length):-1}function xp(t,n){return a(t.getDate(),n,2)}function Mp(t,n){return a(t.getHours(),n,2)}function Ep(t,n){return a(t.getHours()%12||12,n,2)}function Ap(t,n){return a(1+$1.count(Od(t),t),n,3)}function Sp(t,n){return a(t.getMilliseconds(),n,3)}function Tp(t,n){return Sp(t,n)+"000"}function kp(t,n){return a(t.getMonth()+1,n,2)}function Cp(t,n){return a(t.getMinutes(),n,2)}function Np(t,n){return a(t.getSeconds(),n,2)}function Dp(t){var n=t.getDay();return 0===n?7:n}function Pp(t,n){return a(rd.count(Od(t)-1,t),n,2)}function Lp(t){var n=t.getDay();return 4<=n||0===n?ud(t):ud.ceil(t)}function Ip(t,n){return t=Lp(t),a(ud.count(Od(t),t)+(4===Od(t).getDay()),n,2)}function Rp(t){return t.getDay()}function Op(t,n){return a(id.count(Od(t)-1,t),n,2)}function Bp(t,n){return a(t.getFullYear()%100,n,2)}function Fp(t,n){return a((t=Lp(t)).getFullYear()%100,n,2)}function Yp(t,n){return a(t.getFullYear()%1e4,n,4)}function zp(t,n){var e=t.getDay();return a((t=4<=e||0===e?ud(t):ud.ceil(t)).getFullYear()%1e4,n,4)}function Hp(t){var n=t.getTimezoneOffset();return(0<n?"-":(n*=-1,"+"))+a(n/60|0,"0",2)+a(n%60,"0",2)}function jp(t,n){return a(t.getUTCDate(),n,2)}function Vp(t,n){return a(t.getUTCHours(),n,2)}function Xp(t,n){return a(t.getUTCHours()%12||12,n,2)}function Up(t,n){return a(1+K1.count(Fd(t),t),n,3)}function Wp(t,n){return a(t.getUTCMilliseconds(),n,3)}function Gp(t,n){return Wp(t,n)+"000"}function Zp(t,n){return a(t.getUTCMonth()+1,n,2)}function qp(t,n){return a(t.getUTCMinutes(),n,2)}function $p(t,n){return a(t.getUTCSeconds(),n,2)}function Jp(t){var n=t.getUTCDay();return 0===n?7:n}function Kp(t,n){return a(yd.count(Fd(t)-1,t),n,2)}function Qp(t){var n=t.getUTCDay();return 4<=n||0===n?xd(t):xd.ceil(t)}function tg(t,n){return t=Qp(t),a(xd.count(Fd(t),t)+(4===Fd(t).getUTCDay()),n,2)}function ng(t){return t.getUTCDay()}function eg(t,n){return a(bd.count(Fd(t)-1,t),n,2)}function rg(t,n){return a(t.getUTCFullYear()%100,n,2)}function ig(t,n){return a((t=Qp(t)).getUTCFullYear()%100,n,2)}function og(t,n){return a(t.getUTCFullYear()%1e4,n,4)}function ag(t,n){var e=t.getUTCDay();return a((t=4<=e||0===e?xd(t):xd.ceil(t)).getUTCFullYear()%1e4,n,4)}function ug(){return"+0000"}function cg(){return"%"}function lg(t){return+t}function sg(t){return Math.floor(+t/1e3)}function fg(t){return qd=Zd(t),u.timeFormat=qd.format,u.timeParse=qd.parse,u.utcFormat=qd.utcFormat,u.utcParse=qd.utcParse,qd}u.timeFormat=void 0,u.timeParse=void 0,u.utcFormat=void 0,u.utcParse=void 0,fg({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var hg="%Y-%m-%dT%H:%M:%S.%LZ";var dg=Date.prototype.toISOString?function(t){return t.toISOString()}:u.utcFormat(hg);hg=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:u.utcParse(hg);function pg(t){return new Date(t)}function gg(t){return t instanceof Date?+t:+new Date(+t)}function vg(e,r,n,i,o,a,u,c,l,s){var f=s1(),h=f.invert,d=f.domain,p=s(".%L"),g=s(":%S"),v=s("%I:%M"),m=s("%I %p"),y=s("%a %d"),b=s("%b %d"),_=s("%B"),w=s("%Y");function x(t){return(l(t)<t?p:c(t)<t?g:u(t)<t?v:a(t)<t?m:i(t)<t?o(t)<t?y:b:n(t)<t?_:w)(t)}return f.invert=function(t){return new Date(h(t))},f.domain=function(t){return arguments.length?d(Array.from(t,gg)):d().map(pg)},f.ticks=function(t){var n=d();return e(n[0],n[n.length-1],null==t?10:t)},f.tickFormat=function(t,n){return null==n?x:s(n)},f.nice=function(t){var n=d();return(t=t&&"function"==typeof t.range?t:r(n[0],n[n.length-1],null==t?10:t))?d(d1(n,t)):f},f.copy=function(){return c1(f,vg(e,r,n,i,o,a,u,c,l,s))},f}function mg(){var n,e,r,i,o,a=0,u=1,c=i1,l=!1;function s(t){return null==t||isNaN(t=+t)?o:c(0===r?.5:(t=(i(t)-n)*r,l?Math.max(0,Math.min(1,t)):t))}function t(r){return function(t){var n,e;return arguments.length?([n,e]=t,c=r(n,e),s):[c(0),c(1)]}}return s.domain=function(t){return arguments.length?([a,u]=t,n=i(a=+a),e=i(u=+u),r=n===e?0:1/(e-n),s):[a,u]},s.clamp=function(t){return arguments.length?(l=!!t,s):l},s.interpolator=function(t){return arguments.length?(c=t,s):c},s.range=t(Zr),s.rangeRound=t(qr),s.unknown=function(t){return arguments.length?(o=t,s):o},function(t){return n=(i=t)(a),e=t(u),r=n===e?0:1/(e-n),s}}function yg(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function bg(){var t=T1(mg());return t.copy=function(){return yg(t,bg()).exponent(t.exponent())},K0.apply(t,arguments)}function _g(){var n,e,r,i,o,a,u,c=0,l=.5,s=1,f=1,h=i1,d=!1;function p(t){return isNaN(t=+t)?u:(t=.5+((t=+a(t))-e)*(f*t<f*e?i:o),h(d?Math.max(0,Math.min(1,t)):t))}function t(i){return function(t){var n,e,r;return arguments.length?([n,e,r]=t,h=hi(i,[n,e,r]),p):[h(0),h(.5),h(1)]}}return p.domain=function(t){return arguments.length?([c,l,s]=t,n=a(c=+c),e=a(l=+l),r=a(s=+s),i=n===e?0:.5/(e-n),o=e===r?0:.5/(r-e),f=e<n?-1:1,p):[c,l,s]},p.clamp=function(t){return arguments.length?(d=!!t,p):d},p.interpolator=function(t){return arguments.length?(h=t,p):h},p.range=t(Zr),p.rangeRound=t(qr),p.unknown=function(t){return arguments.length?(u=t,p):u},function(t){return n=(a=t)(c),e=t(l),r=t(s),i=n===e?0:.5/(e-n),o=e===r?0:.5/(r-e),f=e<n?-1:1,p}}function wg(){var t=T1(_g());return t.copy=function(){return yg(t,wg()).exponent(t.exponent())},K0.apply(t,arguments)}function d(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}var xg=d("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),Mg=d("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),Eg=d("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),Ag=d("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0"),Sg=d("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),Tg=d("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),kg=d("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),Cg=d("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),Ng=d("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),Dg=d("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),Pg=d("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),p=t=>Fr(t[t.length-1]),Lg=new Array(3).concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(d),Ig=p(Lg),Rg=new Array(3).concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(d),Og=p(Rg),Bg=new Array(3).concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(d),Fg=p(Bg),Yg=new Array(3).concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(d),zg=p(Yg),Hg=new Array(3).concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(d),jg=p(Hg),Vg=new Array(3).concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(d),Xg=p(Vg),Ug=new Array(3).concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(d),Wg=p(Ug),Gg=new Array(3).concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(d),Zg=p(Gg),qg=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(d),$g=p(qg),Jg=new Array(3).concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(d),Kg=p(Jg),Qg=new Array(3).concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(d),tv=p(Qg),nv=new Array(3).concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(d),ev=p(nv),rv=new Array(3).concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(d),iv=p(rv),ov=new Array(3).concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(d),av=p(ov),uv=new Array(3).concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(d),cv=p(uv),lv=new Array(3).concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(d),sv=p(lv),fv=new Array(3).concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(d),hv=p(fv),dv=new Array(3).concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(d),pv=p(dv),gv=new Array(3).concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(d),vv=p(gv),mv=new Array(3).concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(d),yv=p(mv),bv=new Array(3).concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(d),_v=p(bv),wv=new Array(3).concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(d),xv=p(wv),Mv=new Array(3).concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(d),Ev=p(Mv),Av=new Array(3).concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(d),Sv=p(Av),Tv=new Array(3).concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(d),kv=p(Tv),Cv=new Array(3).concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(d),Nv=p(Cv),Dv=new Array(3).concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(d),p=p(Dv);var Pv=fi(Tr(300,.5,0),Tr(-240,.5,1)),Lv=fi(Tr(-100,.75,.35),Tr(80,1.5,.8)),Iv=fi(Tr(260,.75,.35),Tr(80,1.5,.8)),Rv=Tr();var Ov=He(),Bv=Math.PI/3,Fv=2*Math.PI/3;function Yv(n){var e=n.length;return function(t){return n[Math.max(0,Math.min(e-1,Math.floor(t*e)))]}}var zv=Yv(d("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725")),Hv=Yv(d("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),jv=Yv(d("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),Vv=Yv(d("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));function b(t){return function(){return t}}let Xv=Math.abs,z=Math.atan2,Uv=Math.cos,Wv=Math.max,Gv=Math.min,Zv=Math.sin,X=Math.sqrt,U=1e-12,qv=Math.PI,$v=qv/2,Jv=2*qv;function Kv(t){return 1<=t?$v:t<=-1?-$v:Math.asin(t)}function Qv(e){let r=3;return e.digits=function(t){if(!arguments.length)return r;if(null==t)r=null;else{var n=Math.floor(t);if(!(0<=n))throw new RangeError("invalid digits: "+t);r=n}return e},()=>new fa(r)}function t2(t){return t.innerRadius}function n2(t){return t.outerRadius}function e2(t){return t.startAngle}function r2(t){return t.endAngle}function i2(t){return t&&t.padAngle}function o2(t,n,e,r,i,o,a){var u=t-e,c=n-r,l=(a?o:-o)/X(u*u+c*c),c=l*c,l=-l*u,u=t+c,s=n+l,f=e+c,h=r+l,d=(u+f)/2,p=(s+h)/2,g=f-u,v=h-s,m=g*g+v*v,y=i-o,u=u*h-f*s,h=(v<0?-1:1)*X(Wv(0,y*y*m-u*u)),f=(u*v-g*h)/m,s=(-u*g-v*h)/m,b=(u*v+g*h)/m,u=(-u*g+v*h)/m,g=f-d,v=s-p,h=b-d,m=u-p;return h*h+m*m<g*g+v*v&&(f=b,s=u),{cx:f,cy:s,x01:-c,y01:-l,x11:f*(i/y-1),y11:s*(i/y-1)}}var a2=Array.prototype.slice;function u2(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function c2(t){this._context=t}function l2(t){return new c2(t)}function s2(t){return t[0]}function f2(t){return t[1]}function h2(a,u){var c=b(!0),l=null,s=l2,f=null,h=Qv(n);function n(t){var n,e,r,i=(t=u2(t)).length,o=!1;for(null==l&&(f=s(r=h())),n=0;n<=i;++n)!(n<i&&c(e=t[n],n,t))===o&&((o=!o)?f.lineStart():f.lineEnd()),o&&f.point(+a(e,n,t),+u(e,n,t));if(r)return f=null,r+""||null}return a="function"==typeof a?a:void 0===a?s2:b(a),u="function"==typeof u?u:void 0===u?f2:b(u),n.x=function(t){return arguments.length?(a="function"==typeof t?t:b(+t),n):a},n.y=function(t){return arguments.length?(u="function"==typeof t?t:b(+t),n):u},n.defined=function(t){return arguments.length?(c="function"==typeof t?t:b(!!t),n):c},n.curve=function(t){return arguments.length?(s=t,null!=l&&(f=s(l)),n):s},n.context=function(t){return arguments.length?(null==t?l=f=null:f=s(l=t),n):l},n}function d2(s,f,h){var d=null,p=b(!0),g=null,v=l2,m=null,y=Qv(n);function n(t){var n,e,r,i,o,a=(t=u2(t)).length,u=!1,c=new Array(a),l=new Array(a);for(null==g&&(m=v(o=y())),n=0;n<=a;++n){if(!(n<a&&p(i=t[n],n,t))===u)if(u=!u)e=n,m.areaStart(),m.lineStart();else{for(m.lineEnd(),m.lineStart(),r=n-1;e<=r;--r)m.point(c[r],l[r]);m.lineEnd(),m.areaEnd()}u&&(c[n]=+s(i,n,t),l[n]=+f(i,n,t),m.point(d?+d(i,n,t):c[n],h?+h(i,n,t):l[n]))}if(o)return m=null,o+""||null}function t(){return h2().defined(p).curve(v).context(g)}return s="function"==typeof s?s:void 0===s?s2:b(+s),f="function"==typeof f?f:b(void 0===f?0:+f),h="function"==typeof h?h:void 0===h?f2:b(+h),n.x=function(t){return arguments.length?(s="function"==typeof t?t:b(+t),d=null,n):s},n.x0=function(t){return arguments.length?(s="function"==typeof t?t:b(+t),n):s},n.x1=function(t){return arguments.length?(d=null==t?null:"function"==typeof t?t:b(+t),n):d},n.y=function(t){return arguments.length?(f="function"==typeof t?t:b(+t),h=null,n):f},n.y0=function(t){return arguments.length?(f="function"==typeof t?t:b(+t),n):f},n.y1=function(t){return arguments.length?(h=null==t?null:"function"==typeof t?t:b(+t),n):h},n.lineX0=n.lineY0=function(){return t().x(s).y(f)},n.lineY1=function(){return t().x(s).y(h)},n.lineX1=function(){return t().x(d).y(f)},n.defined=function(t){return arguments.length?(p="function"==typeof t?t:b(!!t),n):p},n.curve=function(t){return arguments.length?(v=t,null!=g&&(m=v(g)),n):v},n.context=function(t){return arguments.length?(null==t?g=m=null:m=v(g=t),n):g},n}function p2(t,n){return n<t?-1:t<n?1:t<=n?0:NaN}function g2(t){return t}c2.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var v2=y2(l2);function m2(t){this._curve=t}function y2(n){function t(t){return new m2(n(t))}return t._curve=n,t}function b2(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(y2(t)):n()._curve},t}function _2(){return b2(h2().curve(v2))}function w2(){var t=d2().curve(v2),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return b2(e())},delete t.lineX0,t.lineEndAngle=function(){return b2(r())},delete t.lineX1,t.lineInnerRadius=function(){return b2(i())},delete t.lineY0,t.lineOuterRadius=function(){return b2(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(y2(t)):n()._curve},t}function x2(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]}m2.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),n*-Math.cos(t))}};class M2{constructor(t,n){this._context=t,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n)}this._x0=t,this._y0=n}}class E2{constructor(t){this._context=t}lineStart(){this._point=0}lineEnd(){}point(t,n){var e,r,i,o;t=+t,n=+n,0===this._point?this._point=1:(e=x2(this._x0,this._y0),r=x2(this._x0,this._y0=(this._y0+n)/2),i=x2(t,this._y0),o=x2(t,n),this._context.moveTo(...e),this._context.bezierCurveTo(...r,...i,...o)),this._x0=t,this._y0=n}}function A2(t){return new M2(t,!0)}function S2(t){return new M2(t,!1)}function T2(t){return new E2(t)}function k2(t){return t.source}function C2(t){return t.target}function N2(i){let o=k2,a=C2,u=s2,c=f2,l=null,s=null,f=Qv(n);function n(){let t;var n=a2.call(arguments),e=o.apply(this,n),r=a.apply(this,n);if((s=null==l?i(t=f()):s).lineStart(),n[0]=e,s.point(+u.apply(this,n),+c.apply(this,n)),n[0]=r,s.point(+u.apply(this,n),+c.apply(this,n)),s.lineEnd(),t)return s=null,t+""||null}return n.source=function(t){return arguments.length?(o=t,n):o},n.target=function(t){return arguments.length?(a=t,n):a},n.x=function(t){return arguments.length?(u="function"==typeof t?t:b(+t),n):u},n.y=function(t){return arguments.length?(c="function"==typeof t?t:b(+t),n):c},n.context=function(t){return arguments.length?(null==t?l=s=null:s=i(l=t),n):l},n}let D2=X(3),P2={draw(t,n){var e=.59436*X(n+Gv(n/28,.75)),r=e/2,i=r*D2;t.moveTo(0,e),t.lineTo(0,-e),t.moveTo(-i,-r),t.lineTo(i,r),t.moveTo(-i,r),t.lineTo(i,-r)}};var L2={draw(t,n){var e=X(n/qv);t.moveTo(e,0),t.arc(0,0,e,0,Jv)}},I2={draw(t,n){var e=X(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}};let R2=X(1/3),O2=2*R2,B2={draw(t,n){var e=X(n/O2),r=e*R2;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},F2={draw(t,n){var e=.62625*X(n);t.moveTo(0,-e),t.lineTo(e,0),t.lineTo(0,e),t.lineTo(-e,0),t.closePath()}},Y2={draw(t,n){var e=.87559*X(n-Gv(n/7,2));t.moveTo(-e,0),t.lineTo(e,0),t.moveTo(0,e),t.lineTo(0,-e)}},z2={draw(t,n){var e=X(n),r=-e/2;t.rect(r,r,e,e)}},H2={draw(t,n){var e=.4431*X(n);t.moveTo(e,e),t.lineTo(e,-e),t.lineTo(-e,-e),t.lineTo(-e,e),t.closePath()}};var j2=Zv(qv/10)/Zv(7*qv/10);let V2=Zv(Jv/10)*j2,X2=-Uv(Jv/10)*j2,U2={draw(t,n){var e=X(.8908130915292852*n),r=V2*e,i=X2*e;t.moveTo(0,-e),t.lineTo(r,i);for(let u=1;u<5;++u){var o=Jv*u/5,a=Uv(o),o=Zv(o);t.lineTo(o*e,-a*e),t.lineTo(a*r-o*i,o*r+a*i)}t.closePath()}},W2=X(3),G2={draw(t,n){var e=-X(n/(3*W2));t.moveTo(0,2*e),t.lineTo(-W2*e,-e),t.lineTo(W2*e,-e),t.closePath()}},Z2=X(3),q2={draw(t,n){var e=.6824*X(n),r=e/2,i=e*Z2/2;t.moveTo(0,-e),t.lineTo(i,r),t.lineTo(-i,r),t.closePath()}},$2=-.5,J2=X(3)/2,K2=1/X(12),Q2=3*(K2/2+1),tm={draw(t,n){var e=X(n/Q2),r=e/2,i=e*K2,o=r,e=e*K2+e,a=-o,u=e;t.moveTo(r,i),t.lineTo(o,e),t.lineTo(a,u),t.lineTo($2*r-J2*i,J2*r+$2*i),t.lineTo($2*o-J2*e,J2*o+$2*e),t.lineTo($2*a-J2*u,J2*a+$2*u),t.lineTo($2*r+J2*i,$2*i-J2*r),t.lineTo($2*o+J2*e,$2*e-J2*o),t.lineTo($2*a+J2*u,$2*u-J2*a),t.closePath()}},nm={draw(t,n){var e=.6189*X(n-Gv(n/6,1.7));t.moveTo(-e,-e),t.lineTo(e,e),t.moveTo(-e,e),t.lineTo(e,-e)}};var j2=[L2,I2,B2,z2,U2,G2,tm],em=[L2,Y2,nm,q2,P2,H2,F2];function rm(){}function im(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function om(t){this._context=t}function am(t){this._context=t}function um(t){this._context=t}function cm(t,n){this._basis=new om(t),this._beta=n}om.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:im(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:im(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},am.prototype={areaStart:rm,areaEnd:rm,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:im(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},um.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:im(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},cm.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(0<e)for(var r,i=t[0],o=n[0],a=t[e]-i,u=n[e]-o,c=-1;++c<=e;)this._basis.point(this._beta*t[c]+(1-this._beta)*(i+(r=c/e)*a),this._beta*n[c]+(1-this._beta)*(o+r*u));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var lm=function n(e){function t(t){return 1===e?new om(t):new cm(t,e)}return t.beta=function(t){return n(+t)},t}(.85);function sm(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function fm(t,n){this._context=t,this._k=(1-n)/6}fm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:sm(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:sm(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var hm=function n(e){function t(t){return new fm(t,e)}return t.tension=function(t){return n(+t)},t}(0);function dm(t,n){this._context=t,this._k=(1-n)/6}dm.prototype={areaStart:rm,areaEnd:rm,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:sm(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var pm=function n(e){function t(t){return new dm(t,e)}return t.tension=function(t){return n(+t)},t}(0);function gm(t,n){this._context=t,this._k=(1-n)/6}gm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:sm(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var vm=function n(e){function t(t){return new gm(t,e)}return t.tension=function(t){return n(+t)},t}(0);function mm(t,n,e){var r,i,o=t._x1,a=t._y1,u=t._x2,c=t._y2;t._l01_a>U&&(r=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,i=3*t._l01_a*(t._l01_a+t._l12_a),o=(o*r-t._x0*t._l12_2a+t._x2*t._l01_2a)/i,a=(a*r-t._y0*t._l12_2a+t._y2*t._l01_2a)/i),t._l23_a>U&&(r=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,i=3*t._l23_a*(t._l23_a+t._l12_a),u=(u*r+t._x1*t._l23_2a-n*t._l12_2a)/i,c=(c*r+t._y1*t._l23_2a-e*t._l12_2a)/i),t._context.bezierCurveTo(o,a,u,c,t._x2,t._y2)}function ym(t,n){this._context=t,this._alpha=n}ym.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e,r;switch(t=+t,n=+n,this._point&&(e=this._x2-t,r=this._y2-n,this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))),this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:mm(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var bm=function n(e){function t(t){return e?new ym(t,e):new fm(t,0)}return t.alpha=function(t){return n(+t)},t}(.5);function _m(t,n){this._context=t,this._alpha=n}_m.prototype={areaStart:rm,areaEnd:rm,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){var e,r;switch(t=+t,n=+n,this._point&&(e=this._x2-t,r=this._y2-n,this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))),this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:mm(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var wm=function n(e){function t(t){return e?new _m(t,e):new dm(t,0)}return t.alpha=function(t){return n(+t)},t}(.5);function xm(t,n){this._context=t,this._alpha=n}xm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e,r;switch(t=+t,n=+n,this._point&&(e=this._x2-t,r=this._y2-n,this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))),this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:mm(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Mm=function n(e){function t(t){return e?new xm(t,e):new gm(t,0)}return t.alpha=function(t){return n(+t)},t}(.5);function Em(t){this._context=t}function Am(t){return t<0?-1:1}function Sm(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),a=(e-t._y1)/(i||r<0&&-0),r=(o*i+a*r)/(r+i);return(Am(o)+Am(a))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs(r))||0}function Tm(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function km(t,n,e){var r=t._x0,i=t._y0,o=t._x1,a=t._y1,u=(o-r)/3;t._context.bezierCurveTo(r+u,i+u*n,o-u,a-u*e,o,a)}function Cm(t){this._context=t}function Nm(t){this._context=new Dm(t)}function Dm(t){this._context=t}function Pm(t){this._context=t}function Lm(t){var n,e,r=t.length-1,i=new Array(r),o=new Array(r),a=new Array(r);for(o[i[0]=0]=2,a[0]=t[0]+2*t[1],n=1;n<r-1;++n)i[n]=1,o[n]=4,a[n]=4*t[n]+2*t[n+1];for(i[r-1]=2,o[r-1]=7,a[r-1]=8*t[r-1]+t[r],n=1;n<r;++n)e=i[n]/o[n-1],o[n]-=e,a[n]-=e*a[n-1];for(i[r-1]=a[r-1]/o[r-1],n=r-2;0<=n;--n)i[n]=(a[n]-i[n+1])/o[n];for(o[r-1]=(t[r]+i[r-1])/2,n=0;n<r-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}function Im(t,n){this._context=t,this._t=n}function Rm(t,n){if(1<(i=t.length))for(var e,r,i,o=1,a=t[n[0]],u=a.length;o<i;++o)for(r=a,a=t[n[o]],e=0;e<u;++e)a[e][1]+=a[e][0]=isNaN(r[e][1])?r[e][0]:r[e][1]}function Om(t){for(var n=t.length,e=new Array(n);0<=--n;)e[n]=n;return e}function Bm(t,n){return t[n]}function Fm(t){var n=[];return n.key=t,n}function Ym(t){var e=t.map(zm);return Om(t).sort(function(t,n){return e[t]-e[n]})}function zm(t){for(var n,e=-1,r=0,i=t.length,o=-1/0;++e<i;)(n=+t[e][1])>o&&(o=n,r=e);return r}function Hm(t){var e=t.map(jm);return Om(t).sort(function(t,n){return e[t]-e[n]})}function jm(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}Em.prototype={areaStart:rm,areaEnd:rm,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},Cm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:km(this,this._t0,Tm(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,km(this,Tm(this,e=Sm(this,t,n)),e);break;default:km(this,this._t0,e=Sm(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(Nm.prototype=Object.create(Cm.prototype)).point=function(t,n){Cm.prototype.point.call(this,n,t)},Dm.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}},Pm.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var r=Lm(t),i=Lm(n),o=0,a=1;a<e;++o,++a)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[a],n[a]);(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},Im.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),0<=this._line&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:var e;this._t<=0?(this._context.lineTo(this._x,n),this._context.lineTo(t,n)):(e=this._x*(1-this._t)+t*this._t,this._context.lineTo(e,this._y),this._context.lineTo(e,n))}this._x=t,this._y=n}};var Vm=t=>()=>t;function Xm(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function Um(t,n,e){this.k=t,this.x=n,this.y=e}Um.prototype={constructor:Um,scale:function(t){return 1===t?this:new Um(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new Um(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Wm=new Um(1,0,0);function Gm(t){for(;!t.__zoom;)if(!(t=t.parentNode))return Wm;return t.__zoom}function Zm(t){t.stopImmediatePropagation()}function qm(t){t.preventDefault(),t.stopImmediatePropagation()}function $m(t){return!(t.ctrlKey&&"wheel"!==t.type||t.button)}function Jm(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function Km(){return this.__zoom||Wm}function Qm(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function ty(){return navigator.maxTouchPoints||"ontouchstart"in this}function ny(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],a=t.invertY(n[1][1])-e[1][1];return t.translate(r<i?(r+i)/2:Math.min(0,r)||Math.max(0,i),o<a?(o+a)/2:Math.min(0,o)||Math.max(0,a))}Gm.prototype=Um.prototype,u.Adder=k,u.Delaunay=class ey{static from(t,n=function(t){return t[0]},e=function(t){return t[1]},r){return new ey("length"in t?function(t,n,e,r){var i=t.length,o=new Float64Array(2*i);for(let u=0;u<i;++u){var a=t[u];o[2*u]=n.call(r,a,u,t),o[2*u+1]=e.call(r,a,u,t)}return o}(t,n,e,r):Float64Array.from(function*(t,n,e,r){let i=0;for(var o of t)yield n.call(r,o,i,t),yield e.call(r,o,i,t),++i}(t,n,e,r)))}constructor(t){this._delaunator=new $a(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){let t=this._delaunator,e=this.points;if(t.hull&&2<t.hull.length&&function(t){var{triangles:n,coords:e}=t;for(let a=0;a<n.length;a+=3){var r=2*n[a],i=2*n[a+1],o=2*n[a+2];if(1e-10<(e[o]-e[r])*(e[1+i]-e[1+r])-(e[i]-e[r])*(e[1+o]-e[1+r]))return}return 1}(t)){this.collinear=Int32Array.from({length:e.length/2},(t,n)=>n).sort((t,n)=>e[2*t]-e[2*n]||e[2*t+1]-e[2*n+1]);var r=this.collinear[0],i=this.collinear[this.collinear.length-1],r=[e[2*r],e[2*r+1],e[2*i],e[2*i+1]],o=1e-8*Math.hypot(r[3]-r[1],r[2]-r[0]);for(let t=0,n=e.length/2;t<n;++t){u=e[2*t],c=e[2*t+1],l=o;var a=[u+Math.sin(u+c)*l,c+Math.cos(u-c)*l];e[2*t]=a[0],e[2*t+1]=a[1]}this._delaunator=new $a(e)}else delete this.collinear;var u,c,l,n=this.halfedges=this._delaunator.halfedges,s=this.hull=this._delaunator.hull,f=this.triangles=this._delaunator.triangles,h=this.inedges.fill(-1),d=this._hullIndex.fill(-1);for(let g=0,v=n.length;g<v;++g){var p=f[g%3==2?g-2:g+1];-1!==n[g]&&-1!==h[p]||(h[p]=g)}for(let m=0,y=s.length;m<y;++m)d[s[m]]=m;s.length<=2&&0<s.length&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=s[0],h[s[0]]=1,2===s.length)&&(h[s[1]]=0,this.triangles[1]=s[1],this.triangles[2]=s[1])}voronoi(t){return new eu(this,t)}*neighbors(e){var t,{inedges:n,hull:r,_hullIndex:i,halfedges:o,triangles:a,collinear:u}=this;if(u)0<(t=u.indexOf(e))&&(yield u[t-1]),t<u.length-1&&(yield u[t+1]);else{var c,l=n[e];if(-1!==l){let t=l,n;do{if(yield n=a[t],a[t=t%3==2?t-2:t+1]!==e)return;if(-1===(t=o[t]))return void((c=r[(i[e]+1)%r.length])!==n&&(yield c))}while(t!==l)}}}find(t,n,e=0){if((t=+t)!=t||(n=+n)!=n)return-1;var r=e;let i;for(;0<=(i=this._step(e,t,n))&&i!==e&&i!==r;)e=i;return i}_step(t,n,e){var{inedges:r,hull:i,_hullIndex:o,halfedges:a,triangles:u,points:c}=this;if(-1===r[t]||!c.length)return(t+1)%(c.length>>1);let l=t,s=iu(n-c[2*t],2)+iu(e-c[2*t+1],2);var f=r[t];let h=f;do{var d=u[h],p=iu(n-c[2*d],2)+iu(e-c[2*d+1],2);if(p<s&&(s=p,l=d),u[h=h%3==2?h-2:h+1]!==t)break;if(-1===(h=a[h])){if((h=i[(o[t]+1)%i.length])!==d&&iu(n-c[2*h],2)+iu(e-c[2*h+1],2)<s)return h;break}}while(h!==f);return l}render(t){var n=null==t?t=new tu:void 0,{points:e,halfedges:r,triangles:i}=this;for(let u=0,c=r.length;u<c;++u){var o,a=r[u];a<u||(o=2*i[u],a=2*i[a],t.moveTo(e[o],e[1+o]),t.lineTo(e[a],e[1+a]))}return this.renderHull(t),n&&n.value()}renderPoints(t,n){void 0!==n||t&&"function"==typeof t.moveTo||(n=t,t=null),n=null==n?2:+n;var e=null==t?t=new tu:void 0,r=this.points;for(let a=0,u=r.length;a<u;a+=2){var i=r[a],o=r[a+1];t.moveTo(i+n,o),t.arc(i,o,n,0,ru)}return e&&e.value()}renderHull(n){var t=null==n?n=new tu:void 0,{hull:e,points:r}=this;let i=2*e[0],o=e.length;n.moveTo(r[i],r[i+1]);for(let a=1;a<o;++a){let t=2*e[a];n.lineTo(r[t],r[1+t])}return n.closePath(),t&&t.value()}hullPolygon(){var t=new nu;return this.renderHull(t),t.value()}renderTriangle(t,n){var e=null==n?n=new tu:void 0,{points:r,triangles:i}=this,o=2*i[t*=3],a=2*i[t+1],i=2*i[t+2];return n.moveTo(r[o],r[1+o]),n.lineTo(r[a],r[1+a]),n.lineTo(r[i],r[1+i]),n.closePath(),e&&e.value()}*trianglePolygons(){var t=this.triangles;for(let n=0,e=t.length/3;n<e;++n)yield this.trianglePolygon(n)}trianglePolygon(t){var n=new nu;return this.renderTriangle(t,n),n.value()}},u.FormatSpecifier=uc,u.InternMap=rt,u.InternSet=it,u.Node=Wh,u.Path=fa,u.Voronoi=eu,u.ZoomTransform=Um,u.active=function(t,n){var e,r,i=t.__transition;if(i)for(r in n=null==n?null:n+"",i)if((e=i[r]).state>Oi&&e.name===n)return new eo([[t]],So,n,+r);return null},u.arc=function(){var C=t2,N=n2,D=b(0),P=null,L=e2,I=r2,R=i2,O=null,B=Qv(n);function n(){var t,n,e,r,i,o,a,u,c,l,s,f,h,d,p,g,v,m,y,b,_,w,x,M=+C.apply(this,arguments),E=+N.apply(this,arguments),A=L.apply(this,arguments)-$v,S=I.apply(this,arguments)-$v,T=Xv(S-A),k=A<S;if(O=O||(t=B()),E<M&&(n=E,E=M,M=n),E>U?T>Jv-U?(O.moveTo(E*Uv(A),E*Zv(A)),O.arc(0,0,E,A,S,!k),M>U&&(O.moveTo(M*Uv(S),M*Zv(S)),O.arc(0,0,M,S,A,k))):(r=n=A,i=e=S,a=o=T,h=(s=R.apply(this,arguments)/2)>U&&(P?+P.apply(this,arguments):X(M*M+E*E)),w=_=u=Gv(Xv(E-M)/2,+D.apply(this,arguments)),h>U&&(f=Kv(h/M*Zv(s)),h=Kv(h/E*Zv(s)),(o-=2*f)>U?(r+=f*=k?1:-1,i-=f):(o=0,r=i=(A+S)/2),(a-=2*h)>U?(n+=h*=k?1:-1,e-=h):(a=0,n=e=(A+S)/2)),s=E*Uv(n),f=E*Zv(n),h=M*Uv(i),A=M*Zv(i),u>U&&(d=E*Uv(e),p=E*Zv(e),g=M*Uv(r),v=M*Zv(r),T<qv)&&((S=function(t,n,e,r,i,o,a,u){var c=e-t,l=r-n,s=a-i,f=u-o,h=f*c-s*l;if(!(h*h<U))return[t+(h=(s*(n-o)-f*(t-i))/h)*c,n+h*l]}(s,f,g,v,d,p,h,A))?(T=s-S[0],b=f-S[1],m=d-S[0],y=p-S[1],T=1/Zv((1<(x=(T*m+b*y)/(X(T*T+b*b)*X(m*m+y*y)))?0:x<-1?qv:Math.acos(x))/2),b=X(S[0]*S[0]+S[1]*S[1]),_=Gv(u,(M-b)/(T-1)),w=Gv(u,(E-b)/(1+T))):_=w=0),a>U?w>U?(c=o2(g,v,s,f,E,w,k),l=o2(d,p,h,A,E,w,k),O.moveTo(c.cx+c.x01,c.cy+c.y01),w<u?O.arc(c.cx,c.cy,w,z(c.y01,c.x01),z(l.y01,l.x01),!k):(O.arc(c.cx,c.cy,w,z(c.y01,c.x01),z(c.y11,c.x11),!k),O.arc(0,0,E,z(c.cy+c.y11,c.cx+c.x11),z(l.cy+l.y11,l.cx+l.x11),!k),O.arc(l.cx,l.cy,w,z(l.y11,l.x11),z(l.y01,l.x01),!k))):(O.moveTo(s,f),O.arc(0,0,E,n,e,!k)):O.moveTo(s,f),M>U&&o>U?_>U?(c=o2(h,A,d,p,M,-_,k),l=o2(s,f,g,v,M,-_,k),O.lineTo(c.cx+c.x01,c.cy+c.y01),_<u?O.arc(c.cx,c.cy,_,z(c.y01,c.x01),z(l.y01,l.x01),!k):(O.arc(c.cx,c.cy,_,z(c.y01,c.x01),z(c.y11,c.x11),!k),O.arc(0,0,M,z(c.cy+c.y11,c.cx+c.x11),z(l.cy+l.y11,l.cx+l.x11),k),O.arc(l.cx,l.cy,_,z(l.y11,l.x11),z(l.y01,l.x01),!k))):O.arc(0,0,M,i,r,k):O.lineTo(h,A)):O.moveTo(0,0),O.closePath(),t)return O=null,t+""||null}return n.centroid=function(){var t=(+C.apply(this,arguments)+ +N.apply(this,arguments))/2,n=(+L.apply(this,arguments)+ +I.apply(this,arguments))/2-qv/2;return[Uv(n)*t,Zv(n)*t]},n.innerRadius=function(t){return arguments.length?(C="function"==typeof t?t:b(+t),n):C},n.outerRadius=function(t){return arguments.length?(N="function"==typeof t?t:b(+t),n):N},n.cornerRadius=function(t){return arguments.length?(D="function"==typeof t?t:b(+t),n):D},n.padRadius=function(t){return arguments.length?(P=null==t?null:"function"==typeof t?t:b(+t),n):P},n.startAngle=function(t){return arguments.length?(L="function"==typeof t?t:b(+t),n):L},n.endAngle=function(t){return arguments.length?(I="function"==typeof t?t:b(+t),n):I},n.padAngle=function(t){return arguments.length?(R="function"==typeof t?t:b(+t),n):R},n.context=function(t){return arguments.length?(O=null==t?null:t,n):O},n},u.area=d2,u.areaRadial=w2,u.ascending=l,u.autoType=function(t){for(var n in t){var e,r,i=t[n].trim();if(i)if("true"===i)i=!0;else if("false"===i)i=!1;else if("NaN"===i)i=NaN;else if(isNaN(e=+i)){if(!(r=i.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/)))continue;Au&&r[4]&&!r[7]&&(i=i.replace(/-/g,"/").replace(/T/," ")),i=new Date(i)}else i=e;else i=null;t[n]=i}return t},u.axisBottom=function(t){return fn(en,t)},u.axisLeft=function(t){return fn(rn,t)},u.axisRight=function(t){return fn(nn,t)},u.axisTop=function(t){return fn(tn,t)},u.bin=Pt,u.bisect=A,u.bisectCenter=E,u.bisectLeft=w,u.bisectRight=x,u.bisector=v,u.blob=function(t,n){return fetch(t,n).then(Su)},u.blur=function(t,n){if(!(0<=(n=+n)))throw new RangeError("invalid r");var e,r,i=t.length;if(0<=(i=Math.floor(i)))return i&&n&&(e=q(n),r=t.slice(),e(t,r,0,i,1),e(r,t,0,i,1),e(t,r,0,i,1)),t;throw new RangeError("invalid length")},u.blur2=S,u.blurImage=T,u.brush=function(){return $o(Yo)},u.brushSelection=function(t){var n=t.__brush;return n?n.dim.output(n.selection):null},u.brushX=function(){return $o(Bo)},u.brushY=function(){return $o(Fo)},u.buffer=function(t,n){return fetch(t,n).then(Tu)},u.chord=function(){return oa(!1,!1)},u.chordDirected=function(){return oa(!0,!1)},u.chordTranspose=function(){return oa(!1,!0)},u.cluster=function(){var u=Bh,c=1,l=1,s=!1;function n(n){var r,i=0,t=(n.eachAfter(function(t){var n,e=t.children;e?(t.x=(n=e).reduce(Fh,0)/n.length,t.y=1+e.reduce(Yh,0)):(t.x=r?i+=u(t,r):0,t.y=0,r=t)}),function(t){for(var n;n=t.children;)t=n[0];return t}(n)),e=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(n),o=t.x-u(t,e)/2,a=e.x+u(e,t)/2;return n.eachAfter(s?function(t){t.x=(t.x-n.x)*c,t.y=(n.y-t.y)*l}:function(t){t.x=(t.x-o)/(a-o)*c,t.y=(1-(n.y?t.y/n.y:1))*l})}return n.separation=function(t){return arguments.length?(u=t,n):u},n.size=function(t){return arguments.length?(s=!1,c=+t[0],l=+t[1],n):s?null:[c,l]},n.nodeSize=function(t){return arguments.length?(s=!0,c=+t[0],l=+t[1],n):s?[c,l]:null},n},u.color=Be,u.contourDensity=function(){var s=Ia,f=Ra,h=Oa,r=960,i=500,d=20,p=2,g=3*d,v=r+2*g>>p,m=i+2*g>>p,o=Aa(20);function a(t){var n,e=new Float32Array(v*m),r=Math.pow(2,-p),i=-1;for(n of t){var o,a,u=(s(n,++i,t)+g)*r,c=(f(n,i,t)+g)*r,l=+h(n,i,t);l&&0<=u&&u<v&&0<=c&&c<m&&(e[(o=Math.floor(u))+(a=Math.floor(c))*v]+=(1-(u=u-o-.5))*(1-(c=c-a-.5))*l,e[o+1+a*v]+=u*(1-c)*l,e[o+1+(a+1)*v]+=u*c*l,e[o+(a+1)*v]+=(1-u)*c*l)}return S({data:e,width:v,height:m},d*r),e}function n(t){var n=a(t),e=o(n),r=Math.pow(2,2*p);return Array.isArray(e)||(e=Tt(Number.MIN_VALUE,Lt(n)/r,e)),Ca().size([v,m]).thresholds(e.map(t=>t*r))(n).map((t,n)=>(t.value=+e[n],u(t)))}function u(t){return t.coordinates.forEach(e),t}function e(t){t.forEach(c)}function c(t){t.forEach(l)}function l(t){t[0]=t[0]*Math.pow(2,p)-g,t[1]=t[1]*Math.pow(2,p)-g}function y(){return v=r+2*(g=3*d)>>p,m=i+2*g>>p,n}return n.contours=function(t){var e=a(t),r=Ca().size([v,m]),i=Math.pow(2,2*p),n=t=>{var n=u(r.contour(e,(t=+t)*i));return n.value=t,n};return Object.defineProperty(n,"max",{get:()=>Lt(e)/i}),n},n.x=function(t){return arguments.length?(s="function"==typeof t?t:Aa(+t),n):s},n.y=function(t){return arguments.length?(f="function"==typeof t?t:Aa(+t),n):f},n.weight=function(t){return arguments.length?(h="function"==typeof t?t:Aa(+t),n):h},n.size=function(t){if(!arguments.length)return[r,i];var n=+t[0],e=+t[1];if(0<=n&&0<=e)return r=n,i=e,y();throw new Error("invalid size")},n.cellSize=function(t){if(!arguments.length)return 1<<p;if(1<=(t=+t))return p=Math.floor(Math.log(t)/Math.LN2),y();throw new Error("invalid cell size")},n.thresholds=function(t){return arguments.length?(o="function"==typeof t?t:Array.isArray(t)?Aa(Ma.call(t)):Aa(t),n):o},n.bandwidth=function(t){if(!arguments.length)return Math.sqrt(d*(d+1));if(0<=(t=+t))return d=(Math.sqrt(4*t*t+1)-1)/2,y();throw new Error("invalid bandwidth")},n},u.contours=Ca,u.count=$,u.create=function(t){return H(bn(t).call(document.documentElement))},u.creator=bn,u.cross=function(...e){var n,r="function"==typeof e[e.length-1]&&(n=e.pop(),t=>n(...t)),i=(e=e.map(Q)).map(J),o=e.length-1,a=new Array(1+o).fill(0),u=[];if(o<0||i.some(K))return u;for(;;){u.push(a.map((t,n)=>e[n][t]));let t=o;for(;++a[t]===i[t];){if(0===t)return r?u.map(r):u;a[t--]=0}}},u.csv=Du,u.csvFormat=pu,u.csvFormatBody=gu,u.csvFormatRow=mu,u.csvFormatRows=vu,u.csvFormatValue=t,u.csvParse=hu,u.csvParseRows=du,u.cubehelix=Tr,u.cumsum=function(n,e){var r=0,i=0;return Float64Array.from(n,void 0===e?t=>r+=+t||0:t=>r+=+e(t,i++,n)||0)},u.curveBasis=function(t){return new om(t)},u.curveBasisClosed=function(t){return new am(t)},u.curveBasisOpen=function(t){return new um(t)},u.curveBumpX=A2,u.curveBumpY=S2,u.curveBundle=lm,u.curveCardinal=hm,u.curveCardinalClosed=pm,u.curveCardinalOpen=vm,u.curveCatmullRom=bm,u.curveCatmullRomClosed=wm,u.curveCatmullRomOpen=Mm,u.curveLinear=l2,u.curveLinearClosed=function(t){return new Em(t)},u.curveMonotoneX=function(t){return new Cm(t)},u.curveMonotoneY=function(t){return new Nm(t)},u.curveNatural=function(t){return new Pm(t)},u.curveStep=function(t){return new Im(t,.5)},u.curveStepAfter=function(t){return new Im(t,1)},u.curveStepBefore=function(t){return new Im(t,0)},u.descending=g,u.deviation=nt,u.difference=function(t,...n){t=new it(t);for(var e of n)for(var r of e)t.delete(r);return t},u.disjoint=function(t,n){var e,r,i,o=n[Symbol.iterator](),a=new it;for(e of t){if(a.has(e))return!1;for(;({value:r,done:i}=o.next())&&!i;){if(Object.is(e,r))return!1;a.add(r)}}return!0},u.dispatch=dn,u.drag=function(){var r,i,o,a,u=ve,c=me,e=ye,n=be,g={},v=dn("start","drag","end"),m=0,l=0;function y(t){t.on("mousedown.drag",s).filter(n).on("touchstart.drag",d).on("touchmove.drag",p,ce).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function s(t,n){var e;!a&&u.call(this,t,n)&&(e=_(this,c.call(this,t,n),t,n,"mouse"))&&(H(t.view).on("mousemove.drag",f,le).on("mouseup.drag",h,le),he(t.view),se(t),o=!1,r=t.clientX,i=t.clientY,e("start",t))}function f(t){var n,e;fe(t),o||(n=t.clientX-r,e=t.clientY-i,o=l<n*n+e*e),g.mouse("drag",t)}function h(t){H(t.view).on("mousemove.drag mouseup.drag",null),de(t.view,o),fe(t),g.mouse("end",t)}function d(t,n){if(u.call(this,t,n))for(var e,r=t.changedTouches,i=c.call(this,t,n),o=r.length,a=0;a<o;++a)(e=_(this,i,t,n,r[a].identifier,r[a]))&&(se(t),e("start",t,r[a]))}function p(t){for(var n,e=t.changedTouches,r=e.length,i=0;i<r;++i)(n=g[e[i].identifier])&&(fe(t),n("drag",t,e[i]))}function b(t){var n,e,r=t.changedTouches,i=r.length;for(a&&clearTimeout(a),a=setTimeout(function(){a=null},500),n=0;n<i;++n)(e=g[r[n].identifier])&&(se(t),e("end",t,r[n]))}function _(a,u,t,c,l,n){var s,f,h,d=v.copy(),p=ue(n||t,u);if(null!=(h=e.call(a,new ge("beforestart",{sourceEvent:t,target:y,identifier:l,active:m,x:p[0],y:p[1],dx:0,dy:0,dispatch:d}),c)))return s=h.x-p[0]||0,f=h.y-p[1]||0,function t(n,e,r){var i,o=p;switch(n){case"start":g[l]=t,i=m++;break;case"end":delete g[l],--m;case"drag":p=ue(r||e,u),i=m}d.call(n,a,new ge(n,{sourceEvent:e,subject:h,target:y,identifier:l,active:i,x:p[0]+s,y:p[1]+f,dx:p[0]-o[0],dy:p[1]-o[1],dispatch:d}),c)}}return y.filter=function(t){return arguments.length?(u="function"==typeof t?t:pe(!!t),y):u},y.container=function(t){return arguments.length?(c="function"==typeof t?t:pe(t),y):c},y.subject=function(t){return arguments.length?(e="function"==typeof t?t:pe(t),y):e},y.touchable=function(t){return arguments.length?(n="function"==typeof t?t:pe(!!t),y):n},y.on=function(){var t=v.on.apply(v,arguments);return t===v?y:t},y.clickDistance=function(t){return arguments.length?(l=(t=+t)*t,y):Math.sqrt(l)},y},u.dragDisable=he,u.dragEnable=de,u.dsv=function(t,n,e,r){3===arguments.length&&"function"==typeof e&&(r=e,e=void 0);var i=fu(t);return Cu(n,e).then(function(t){return i.parse(t,r)})},u.dsvFormat=fu,u.easeBack=yo,u.easeBackIn=bo,u.easeBackInOut=yo,u.easeBackOut=_o,u.easeBounce=mo,u.easeBounceIn=function(t){return 1-mo(1-t)},u.easeBounceInOut=function(t){return((t*=2)<=1?1-mo(1-t):mo(t-1)+1)/2},u.easeBounceOut=mo,u.easeCircle=go,u.easeCircleIn=function(t){return 1-Math.sqrt(1-t*t)},u.easeCircleInOut=go,u.easeCircleOut=function(t){return Math.sqrt(1- --t*t)},u.easeCubic=ao,u.easeCubicIn=function(t){return t*t*t},u.easeCubicInOut=ao,u.easeCubicOut=function(t){return--t*t*t+1},u.easeElastic=Mo,u.easeElasticIn=xo,u.easeElasticInOut=Eo,u.easeElasticOut=Mo,u.easeExp=po,u.easeExpIn=function(t){return ho(1-+t)},u.easeExpInOut=po,u.easeExpOut=function(t){return 1-ho(t)},u.easeLinear=t=>+t,u.easePoly=co,u.easePolyIn=io,u.easePolyInOut=co,u.easePolyOut=uo,u.easeQuad=oo,u.easeQuadIn=function(t){return t*t},u.easeQuadInOut=oo,u.easeQuadOut=function(t){return t*(2-t)},u.easeSin=fo,u.easeSinIn=function(t){return 1==+t?1:1-Math.cos(t*so)},u.easeSinInOut=fo,u.easeSinOut=function(t){return Math.sin(t*so)},u.every=function(t,n){if("function"!=typeof n)throw new TypeError("test is not a function");let e=-1;for(var r of t)if(!n(r,++e,t))return!1;return!0},u.extent=et,u.fcumsum=function(n,e){let r=new k,i=-1;return Float64Array.from(n,void 0===e?t=>r.add(+t||0):t=>r.add(+e(t,++i,n)||0))},u.filter=function(t,n){if("function"!=typeof n)throw new TypeError("test is not a function");var e,r=[];let i=-1;for(e of t)n(e,++i,t)&&r.push(e);return r},u.flatGroup=function(t,...n){return ht(ft(t,...n),n)},u.flatRollup=function(t,n,...e){return ht(pt(t,n,...e),e)},u.forceCenter=function(o,a){var u,c=1;function n(){for(var t,n=u.length,e=0,r=0,i=0;i<n;++i)e+=(t=u[i]).x,r+=t.y;for(e=(e/n-o)*c,r=(r/n-a)*c,i=0;i<n;++i)(t=u[i]).x-=e,t.y-=r}return null==o&&(o=0),null==a&&(a=0),n.initialize=function(t){u=t},n.x=function(t){return arguments.length?(o=+t,n):o},n.y=function(t){return arguments.length?(a=+t,n):a},n.strength=function(t){return arguments.length?(c=+t,n):c},n},u.forceCollide=function(r){var o,a,v,m=1,u=1;function n(){for(var t,n,f,h,d,p,g,e=o.length,r=0;r<u;++r)for(n=ju(o,Wu,Gu).visitAfter(c),t=0;t<e;++t)f=o[t],p=a[f.index],g=p*p,h=f.x+f.vx,d=f.y+f.vy,n.visit(i);function i(t,n,e,r,i){var o,a,u,c=t.data,l=t.r,s=p+l;if(!c)return h+s<n||r<h-s||d+s<e||i<d-s;c.index>f.index&&(u=(o=h-c.x-c.vx)*o+(a=d-c.y-c.vy)*a)<s*s&&(0===o&&(u+=(o=Uu(v))*o),0===a&&(u+=(a=Uu(v))*a),u=(s-(u=Math.sqrt(u)))/u*m,f.vx+=(o*=u)*(s=(l*=l)/(g+l)),f.vy+=(a*=u)*s,c.vx-=o*(s=1-s),c.vy-=a*s)}}function c(t){if(t.data)return t.r=a[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function e(){if(o){var t,n,e=o.length;for(a=new Array(e),t=0;t<e;++t)n=o[t],a[n.index]=+r(n,t,o)}}return"function"!=typeof r&&(r=y(null==r?1:+r)),n.initialize=function(t,n){o=t,v=n,e()},n.iterations=function(t){return arguments.length?(u=+t,n):u},n.strength=function(t){return arguments.length?(m=+t,n):m},n.radius=function(t){return arguments.length?(r="function"==typeof t?t:y(+t),e(),n):r},n},u.forceLink=function(l){var s,f,o,a,h,d,u=Zu,e=function(t){return 1/Math.min(a[t.source.index],a[t.target.index])},r=y(30),p=1;function n(t){for(var n=0,e=l.length;n<p;++n)for(var r,i,o,a,u,c=0;c<e;++c)r=(i=l[c]).source,o=(i=i.target).x+i.vx-r.x-r.vx||Uu(d),a=i.y+i.vy-r.y-r.vy||Uu(d),o*=u=((u=Math.sqrt(o*o+a*a))-f[c])/u*t*s[c],a*=u,i.vx-=o*(u=h[c]),i.vy-=a*u,r.vx+=o*(u=1-u),r.vy+=a*u}function i(){if(o){var t,n=o.length,e=l.length,r=new Map(o.map((t,n)=>[u(t,n,o),t])),i=0;for(a=new Array(n);i<e;++i)(t=l[i]).index=i,"object"!=typeof t.source&&(t.source=qu(r,t.source)),"object"!=typeof t.target&&(t.target=qu(r,t.target)),a[t.source.index]=(a[t.source.index]||0)+1,a[t.target.index]=(a[t.target.index]||0)+1;for(i=0,h=new Array(e);i<e;++i)t=l[i],h[i]=a[t.source.index]/(a[t.source.index]+a[t.target.index]);s=new Array(e),c(),f=new Array(e),g()}}function c(){if(o)for(var t=0,n=l.length;t<n;++t)s[t]=+e(l[t],t,l)}function g(){if(o)for(var t=0,n=l.length;t<n;++t)f[t]=+r(l[t],t,l)}return null==l&&(l=[]),n.initialize=function(t,n){o=t,d=n,i()},n.links=function(t){return arguments.length?(l=t,i(),n):l},n.id=function(t){return arguments.length?(u=t,n):u},n.iterations=function(t){return arguments.length?(p=+t,n):p},n.strength=function(t){return arguments.length?(e="function"==typeof t?t:y(+t),c(),n):e},n.distance=function(t){return arguments.length?(r="function"==typeof t?t:y(+t),g(),n):r},n},u.forceManyBody=function(){var i,c,l,s,f,r=y(-30),h=1,d=1/0,p=.81;function n(t){var n,e=i.length,r=ju(i,Qu,tc).visitAfter(o);for(s=t,n=0;n<e;++n)c=i[n],r.visit(a)}function e(){if(i){var t,n,e=i.length;for(f=new Array(e),t=0;t<e;++t)n=i[t],f[n.index]=+r(n,t,i)}}function o(t){var n,e,r,i,o,a=0,u=0;if(t.length){for(r=i=o=0;o<4;++o)(n=t[o])&&(e=Math.abs(n.value))&&(a+=n.value,u+=e,r+=e*n.x,i+=e*n.y);t.x=r/u,t.y=i/u}else for((n=t).x=n.data.x,n.y=n.data.y;a+=f[n.data.index],n=n.next;);t.value=a}function a(t,n,e,r){if(!t.value)return!0;var i=t.x-c.x,o=t.y-c.y,a=r-n,u=i*i+o*o;if(a*a/p<u)return u<d&&(0===i&&(u+=(i=Uu(l))*i),0===o&&(u+=(o=Uu(l))*o),u<h&&(u=Math.sqrt(h*u)),c.vx+=i*t.value*s/u,c.vy+=o*t.value*s/u),!0;if(!(t.length||d<=u))for((t.data!==c||t.next)&&(0===i&&(u+=(i=Uu(l))*i),0===o&&(u+=(o=Uu(l))*o),u<h)&&(u=Math.sqrt(h*u));t.data!==c&&(a=f[t.data.index]*s/u,c.vx+=i*a,c.vy+=o*a),t=t.next;);}return n.initialize=function(t,n){i=t,l=n,e()},n.strength=function(t){return arguments.length?(r="function"==typeof t?t:y(+t),e(),n):r},n.distanceMin=function(t){return arguments.length?(h=t*t,n):Math.sqrt(h)},n.distanceMax=function(t){return arguments.length?(d=t*t,n):Math.sqrt(d)},n.theta=function(t){return arguments.length?(p=t*t,n):Math.sqrt(p)},n},u.forceRadial=function(e,u,c){var l,s,f,r=y(.1);function n(t){for(var n=0,e=l.length;n<e;++n){var r=l[n],i=r.x-u||1e-6,o=r.y-c||1e-6,a=Math.sqrt(i*i+o*o),a=(f[n]-a)*s[n]*t/a;r.vx+=i*a,r.vy+=o*a}}function i(){if(l){var t,n=l.length;for(s=new Array(n),f=new Array(n),t=0;t<n;++t)f[t]=+e(l[t],t,l),s[t]=isNaN(f[t])?0:+r(l[t],t,l)}}return"function"!=typeof e&&(e=y(+e)),null==u&&(u=0),null==c&&(c=0),n.initialize=function(t){l=t,i()},n.strength=function(t){return arguments.length?(r="function"==typeof t?t:y(+t),i(),n):r},n.radius=function(t){return arguments.length?(e="function"==typeof t?t:y(+t),i(),n):e},n.x=function(t){return arguments.length?(u=+t,n):u},n.y=function(t){return arguments.length?(c=+t,n):c},n},u.forceSimulation=function(c){var o,a=1,n=.001,u=1-Math.pow(n,1/300),l=0,s=.6,f=new Map,t=Ti(i),e=dn("tick","end"),r=function(){let t=1;return()=>(t=($u*t+Ju)%Ku)/Ku}();function i(){h(),e.call("tick",o),a<n&&(t.stop(),e.call("end",o))}function h(t){var n,e,r=c.length;void 0===t&&(t=1);for(var i=0;i<t;++i)for(a+=(l-a)*u,f.forEach(function(t){t(a)}),n=0;n<r;++n)null==(e=c[n]).fx?e.x+=e.vx*=s:(e.x=e.fx,e.vx=0),null==e.fy?e.y+=e.vy*=s:(e.y=e.fy,e.vy=0);return o}function d(){for(var t,n,e,r=0,i=c.length;r<i;++r)(e=c[r]).index=r,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),(isNaN(e.x)||isNaN(e.y))&&(t=10*Math.sqrt(.5+r),n=r*nc,e.x=t*Math.cos(n),e.y=t*Math.sin(n)),(isNaN(e.vx)||isNaN(e.vy))&&(e.vx=e.vy=0)}function p(t){return t.initialize&&t.initialize(c,r),t}return null==c&&(c=[]),d(),o={tick:h,restart:function(){return t.restart(i),o},stop:function(){return t.stop(),o},nodes:function(t){return arguments.length?(c=t,d(),f.forEach(p),o):c},alpha:function(t){return arguments.length?(a=+t,o):a},alphaMin:function(t){return arguments.length?(n=+t,o):n},alphaDecay:function(t){return arguments.length?(u=+t,o):+u},alphaTarget:function(t){return arguments.length?(l=+t,o):l},velocityDecay:function(t){return arguments.length?(s=1-t,o):1-s},randomSource:function(t){return arguments.length?(r=t,f.forEach(p),o):r},force:function(t,n){return 1<arguments.length?(null==n?f.delete(t):f.set(t,p(n)),o):f.get(t)},find:function(t,n,e){var r,i,o,a=0,u=c.length;for(null==e?e=1/0:e*=e,a=0;a<u;++a)(r=(r=t-(i=c[a]).x)*r+(r=n-i.y)*r)<e&&(o=i,e=r);return o},on:function(t,n){return 1<arguments.length?(e.on(t,n),o):e.on(t)}}},u.forceX=function(e){var i,o,a,r=y(.1);function n(t){for(var n,e=0,r=i.length;e<r;++e)(n=i[e]).vx+=(a[e]-n.x)*o[e]*t}function u(){if(i){var t,n=i.length;for(o=new Array(n),a=new Array(n),t=0;t<n;++t)o[t]=isNaN(a[t]=+e(i[t],t,i))?0:+r(i[t],t,i)}}return"function"!=typeof e&&(e=y(null==e?0:+e)),n.initialize=function(t){i=t,u()},n.strength=function(t){return arguments.length?(r="function"==typeof t?t:y(+t),u(),n):r},n.x=function(t){return arguments.length?(e="function"==typeof t?t:y(+t),u(),n):e},n},u.forceY=function(e){var i,o,a,r=y(.1);function n(t){for(var n,e=0,r=i.length;e<r;++e)(n=i[e]).vy+=(a[e]-n.y)*o[e]*t}function u(){if(i){var t,n=i.length;for(o=new Array(n),a=new Array(n),t=0;t<n;++t)o[t]=isNaN(a[t]=+e(i[t],t,i))?0:+r(i[t],t,i)}}return"function"!=typeof e&&(e=y(null==e?0:+e)),n.initialize=function(t){i=t,u()},n.strength=function(t){return arguments.length?(r="function"==typeof t?t:y(+t),u(),n):r},n.y=function(t){return arguments.length?(e="function"==typeof t?t:y(+t),u(),n):e},n},u.formatDefaultLocale=gc,u.formatLocale=pc,u.formatSpecifier=ac,u.fsum=function(n,e){var r=new k;if(void 0===e)for(var t of n)(t=+t)&&r.add(t);else{let t=-1;for(var i of n)(i=+e(i,++t,n))&&r.add(i)}return+r},u.geoAlbers=gh,u.geoAlbersUsa=function(){var n,e,i,o,a,r,u=gh(),c=ph().rotate([154,0]).center([-2,58.5]).parallels([55,65]),l=ph().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s={point:function(t,n){r=[t,n]}};function f(t){var n=t[0],e=t[1];return r=null,i.point(n,e),r||(o.point(n,e),r)||(a.point(n,e),r)}function h(){return n=e=null,f}return f.invert=function(t){var n=u.scale(),e=u.translate(),r=(t[0]-e[0])/n,e=(t[1]-e[1])/n;return(.12<=e&&e<.234&&-.425<=r&&r<-.214?c:.166<=e&&e<.234&&-.214<=r&&r<-.115?l:u).invert(t)},f.stream=function(t){return n&&e===t?n:(r=[u.stream(e=t),c.stream(t),l.stream(t)],i=r.length,n={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}});var r,i},f.precision=function(t){return arguments.length?(u.precision(t),c.precision(t),l.precision(t),h()):u.precision()},f.scale=function(t){return arguments.length?(u.scale(t),c.scale(.35*t),l.scale(t),f.translate(u.translate())):u.scale()},f.translate=function(t){var n,e,r;return arguments.length?(n=u.scale(),e=+t[0],r=+t[1],i=u.translate(t).clipExtent([[e-.455*n,r-.238*n],[e+.455*n,r+.238*n]]).stream(s),o=c.translate([e-.307*n,r+.201*n]).clipExtent([[e-.425*n+N,r+.12*n+N],[e-.214*n-N,r+.234*n-N]]).stream(s),a=l.translate([e-.205*n,r+.212*n]).clipExtent([[e-.214*n+N,r+.166*n+N],[e-.115*n-N,r+.234*n-N]]).stream(s),h()):u.translate()},f.fitExtent=function(t,n){return nh(f,t,n)},f.fitSize=function(t,n){return eh(f,t,n)},f.fitWidth=function(t,n){return rh(f,t,n)},f.fitHeight=function(t,n){return ih(f,t,n)},f.scale(1070)},u.geoArea=function(t){return nl=new k,Yc(t,el),2*nl},u.geoAzimuthalEqualArea=function(){return sh(yh).scale(124.75).clipAngle(179.999)},u.geoAzimuthalEqualAreaRaw=yh,u.geoAzimuthalEquidistant=function(){return sh(bh).scale(79.4188).clipAngle(179.999)},u.geoAzimuthalEquidistantRaw=bh,u.geoBounds=function(t){var n,e,r,i,o,a,u;if(Wc=h=-(f=Uc=1/0),Kc=[],Yc(t,Nl),e=Kc.length){for(Kc.sort(Yl),n=1,o=[r=Kc[0]];n<e;++n)zl(r,(i=Kc[n])[0])||zl(r,i[1])?(Fl(r[0],i[1])>Fl(r[0],r[1])&&(r[1]=i[1]),Fl(i[0],r[1])>Fl(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(a=-1/0,n=0,r=o[e=o.length-1];n<=e;r=i,++n)(u=Fl(r[1],(i=o[n])[0]))>a&&(a=u,f=i[0],h=r[1])}return Kc=Qc=null,f===1/0||Uc===1/0?[[NaN,NaN],[NaN,NaN]]:[[f,Uc],[h,Wc]]},u.geoCentroid=function(t){pl=gl=vl=ml=yl=bl=_l=wl=0,xl=new k,Ml=new k,El=new k,Yc(t,Hl);var n=+xl,e=+Ml,r=+El,i=Sc(n,e,r);return i<bc&&(n=bl,e=_l,r=wl,gl<N&&(n=vl,e=ml,r=yl),(i=Sc(n,e,r))<bc)?[NaN,NaN]:[Mc(e,n)*I,Pc(r/i)*I]},u.geoCircle=function(){var r,i,o=Kl([0,0]),a=Kl(90),u=Kl(2),c={point:function(t,n){r.push(t=i(t,n)),t[0]*=I,t[1]*=I}};function n(){var t=o.apply(this,arguments),n=a.apply(this,arguments)*R,e=u.apply(this,arguments)*R;return r=[],i=ns(-t[0]*R,-t[1]*R,0).invert,as(c,n,e,1),t={type:"Polygon",coordinates:[r]},r=i=null,t}return n.center=function(t){return arguments.length?(o="function"==typeof t?t:Kl([+t[0],+t[1]]),n):o},n.radius=function(t){return arguments.length?(a="function"==typeof t?t:Kl(+t),n):a},n.precision=function(t){return arguments.length?(u="function"==typeof t?t:Kl(+t),n):u},n},u.geoClipAntimeridian=ys,u.geoClipCircle=bs,u.geoClipExtent=function(){var n,e,r,i=0,o=0,a=960,u=500;return r={stream:function(t){return n&&e===t?n:n=Ss(i,o,a,u)(e=t)},extent:function(t){return arguments.length?(i=+t[0][0],o=+t[0][1],a=+t[1][0],u=+t[1][1],n=e=null,r):[[i,o],[a,u]]}}},u.geoClipRectangle=Ss,u.geoConicConformal=function(){return hh(Mh).scale(109.5).parallels([30,30])},u.geoConicConformalRaw=Mh,u.geoConicEqualArea=ph,u.geoConicEqualAreaRaw=dh,u.geoConicEquidistant=function(){return hh(Ah).scale(131.154).center([0,13.9389])},u.geoConicEquidistantRaw=Ah,u.geoContains=function(t,n){return(t&&Rs.hasOwnProperty(t.type)?Rs[t.type]:Bs)(t,n)},u.geoDistance=Is,u.geoEqualEarth=function(){return sh(Dh).scale(177.158)},u.geoEqualEarthRaw=Dh,u.geoEquirectangular=function(){return sh(Eh).scale(152.63)},u.geoEquirectangularRaw=Eh,u.geoGnomonic=function(){return sh(Ph).scale(144.049).clipAngle(60)},u.geoGnomonicRaw=Ph,u.geoGraticule=Us,u.geoGraticule10=function(){return Us()()},u.geoIdentity=function(){var i,o,n,e,r,a,u,c=1,l=0,s=0,f=1,h=1,d=0,p=null,g=1,v=1,m=Kf({point:function(t,n){var e=_([t,n]);this.stream.point(e[0],e[1])}}),y=$s;function b(){return g=c*f,v=c*h,a=u=null,_}function _(t){var n,e=t[0]*g,r=t[1]*v;return d&&(n=r*i-e*o,e=e*i+r*o,r=n),[e+l,r+s]}return _.invert=function(t){var n,e=t[0]-l,r=t[1]-s;return d&&(n=r*i+e*o,e=e*i-r*o,r=n),[e/g,r/v]},_.stream=function(t){return a&&u===t?a:a=m(y(u=t))},_.postclip=function(t){return arguments.length?(y=t,p=n=e=r=null,b()):y},_.clipExtent=function(t){return arguments.length?(y=null==t?(p=n=e=r=null,$s):Ss(p=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),b()):null==p?null:[[p,n],[e,r]]},_.scale=function(t){return arguments.length?(c=+t,b()):c},_.translate=function(t){return arguments.length?(l=+t[0],s=+t[1],b()):[l,s]},_.angle=function(t){return arguments.length?(o=F(d=t%360*R),i=B(d),b()):d*I},_.reflectX=function(t){return arguments.length?(f=t?-1:1,b()):f<0},_.reflectY=function(t){return arguments.length?(h=t?-1:1,b()):h<0},_.fitExtent=function(t,n){return nh(_,t,n)},_.fitSize=function(t,n){return eh(_,t,n)},_.fitWidth=function(t,n){return rh(_,t,n)},_.fitHeight=function(t,n){return ih(_,t,n)},_},u.geoInterpolate=function(t,n){var e=t[0]*R,r=t[1]*R,i=n[0]*R,o=n[1]*R,a=B(r),u=F(r),c=B(o),l=F(o),s=a*B(e),f=a*F(e),h=c*B(i),d=c*F(i),p=2*Pc(Y(Lc(o-r)+a*c*Lc(i-e))),g=F(p);return(o=p?function(t){var n=F(t*=p)/g,e=F(p-t)/g,r=e*s+n*h,i=e*f+n*d,e=e*u+n*l;return[Mc(i,r)*I,Mc(e,Y(r*r+i*i))*I]}:function(){return[e*I,r*I]}).distance=p,o},u.geoLength=Ds,u.geoMercator=function(){return wh(_h).scale(961/wc)},u.geoMercatorRaw=_h,u.geoNaturalEarth1=function(){return sh(Lh).scale(175.295)},u.geoNaturalEarth1Raw=Lh,u.geoOrthographic=function(){return sh(Ih).scale(249.5).clipAngle(90+N)},u.geoOrthographicRaw=Ih,u.geoPath=function(n,e){let r=3,i=4.5,o,a;function u(t){return t&&("function"==typeof i&&a.pointRadius(+i.apply(this,arguments)),Yc(t,o(a))),a.result()}return u.area=function(t){return Yc(t,o(of)),of.result()},u.measure=function(t){return Yc(t,o(Uf)),Uf.result()},u.bounds=function(t){return Yc(t,o(pf)),pf.result()},u.centroid=function(t){return Yc(t,o(If)),If.result()},u.projection=function(t){return arguments.length?(o=null==t?(n=null,$s):(n=t).stream,u):n},u.context=function(t){return arguments.length?(a=null==t?(e=null,new $f(r)):new Rf(e=t),"function"!=typeof i&&a.pointRadius(i),u):e},u.pointRadius=function(t){return arguments.length?(i="function"==typeof t?t:(a.pointRadius(+t),+t),u):i},u.digits=function(t){if(!arguments.length)return r;if(null==t)r=null;else{var n=Math.floor(t);if(!(0<=n))throw new RangeError("invalid digits: "+t);r=n}return null===e&&(a=new $f(r)),u},u.projection(n).digits(r).context(e)},u.geoProjection=sh,u.geoProjectionMutator=fh,u.geoRotation=os,u.geoStereographic=function(){return sh(Rh).scale(250).clipAngle(142)},u.geoStereographicRaw=Rh,u.geoStream=Yc,u.geoTransform=function(t){return{stream:Kf(t)}},u.geoTransverseMercator=function(){var t=wh(Oh),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],2<t.length?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)},u.geoTransverseMercatorRaw=Oh,u.gray=function(t,n){return new fr(t,0,0,null==n?1:n)},u.greatest=Yt,u.greatestIndex=function(t,n=l){if(1===n.length)return It(t,n);let e,r=-1,i=-1;for(var o of t)++i,(r<0?0===n(o,o):0<n(o,e))&&(e=o,r=i);return r},u.group=st,u.groupSort=function(t,i,n){return(2!==i.length?yt(dt(t,i,n),([t,n],[e,r])=>l(n,r)||l(t,e)):yt(st(t,n),([t,n],[e,r])=>i(n,r)||l(t,e))).map(([t])=>t)},u.groups=ft,u.hcl=mr,u.hierarchy=Hh,u.histogram=Pt,u.hsl=qe,u.html=Ou,u.image=function(i,o){return new Promise(function(t,n){var e,r=new Image;for(e in o)r[e]=o[e];r.onerror=n,r.onload=function(){t(r)},r.src=i})},u.index=function(t,...n){return vt(t,lt,gt,n)},u.indexes=function(t,...n){return vt(t,Array.from,gt,n)},u.interpolate=Zr,u.interpolateArray=function(t,n){return(zr(n)?Yr:Hr)(t,n)},u.interpolateBasis=Nr,u.interpolateBasisClosed=Dr,u.interpolateBlues=xv,u.interpolateBrBG=Ig,u.interpolateBuGn=Kg,u.interpolateBuPu=tv,u.interpolateCividis=function(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-t*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-2710.57*t)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-67.37*t)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-2475.67*t)))))))+")"},u.interpolateCool=Iv,u.interpolateCubehelix=si,u.interpolateCubehelixDefault=Pv,u.interpolateCubehelixLong=fi,u.interpolateDate=jr,u.interpolateDiscrete=function(n){var e=n.length;return function(t){return n[Math.max(0,Math.min(e-1,Math.floor(t*e)))]}},u.interpolateGnBu=ev,u.interpolateGreens=Ev,u.interpolateGreys=Sv,u.interpolateHcl=ui,u.interpolateHclLong=ci,u.interpolateHsl=Se,u.interpolateHslLong=Ae,u.interpolateHue=function(t,n){var e=Ir(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}},u.interpolateInferno=jv,u.interpolateLab=function(n,t){var e=c((n=sr(n)).l,(t=sr(t)).l),r=c(n.a,t.a),i=c(n.b,t.b),o=c(n.opacity,t.opacity);return function(t){return n.l=e(t),n.a=r(t),n.b=i(t),n.opacity=o(t),n+""}},u.interpolateMagma=Hv,u.interpolateNumber=Vr,u.interpolateNumberArray=Yr,u.interpolateObject=Xr,u.interpolateOrRd=iv,u.interpolateOranges=p,u.interpolatePRGn=Og,u.interpolatePiYG=Fg,u.interpolatePlasma=Vv,u.interpolatePuBu=cv,u.interpolatePuBuGn=av,u.interpolatePuOr=zg,u.interpolatePuRd=sv,u.interpolatePurples=kv,u.interpolateRainbow=function(t){(t<0||1<t)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return Rv.h=360*t-100,Rv.s=1.5-1.5*n,Rv.l=.8-.9*n,Rv+""},u.interpolateRdBu=jg,u.interpolateRdGy=Xg,u.interpolateRdPu=hv,u.interpolateRdYlBu=Wg,u.interpolateRdYlGn=Zg,u.interpolateReds=Nv,u.interpolateRgb=Or,u.interpolateRgbBasis=Fr,u.interpolateRgbBasisClosed=Ee,u.interpolateRound=qr,u.interpolateSinebow=function(t){var n;return t=(.5-t)*Math.PI,Ov.r=255*(n=Math.sin(t))*n,Ov.g=255*(n=Math.sin(t+Bv))*n,Ov.b=255*(n=Math.sin(t+Fv))*n,Ov+""},u.interpolateSpectral=$g,u.interpolateString=Gr,u.interpolateTransformCss=ni,u.interpolateTransformSvg=ei,u.interpolateTurbo=function(t){return t=Math.max(0,Math.min(1,t)),"rgb("+Math.max(0,Math.min(255,Math.round(34.61+t*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-14825.05*t)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+707.56*t)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-6838.66*t)))))))+")"},u.interpolateViridis=zv,u.interpolateWarm=Lv,u.interpolateYlGn=vv,u.interpolateYlGnBu=pv,u.interpolateYlOrBr=yv,u.interpolateYlOrRd=_v,u.interpolateZoom=ii,u.interrupt=Wi,u.intersection=function(t,...n){t=new it(t),n=n.map($t);t:for(var e of t)for(var r of n)if(!r.has(e)){t.delete(e);continue t}return t},u.interval=function(t,n,e){var o=new Si,a=n;return null==n?o.restart(t,n,e):(o._restart=o.restart,o.restart=function(e,r,i){r=+r,i=null==i?Ei():+i,o._restart(function t(n){n+=a,o._restart(t,a+=r,i),e(n)},r,i)},o.restart(t,n,e)),o},u.isoFormat=dg,u.isoParse=hg,u.json=function(t,n){return fetch(t,n).then(Lu)},u.lab=sr,u.lch=function(t,n,e,r){return 1===arguments.length?vr(t):new yr(e,n,t,null==r?1:r)},u.least=function(n,e=l){let r,i=!1;if(1===e.length){let t;for(var o of n){var a=e(o);(i?l(a,t)<0:0===l(a,a))&&(r=o,t=a,i=!0)}}else for(var t of n)(i?e(t,r)<0:0===e(t,t))&&(r=t,i=!0);return r},u.leastIndex=Ut,u.line=h2,u.lineRadial=_2,u.link=N2,u.linkHorizontal=function(){return N2(A2)},u.linkRadial=function(){var t=N2(T2);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t},u.linkVertical=function(){return N2(S2)},u.local=ie,u.map=function(e,r){if("function"!=typeof e[Symbol.iterator])throw new TypeError("values is not iterable");if("function"!=typeof r)throw new TypeError("mapper is not a function");return Array.from(e,(t,n)=>r(t,n,e))},u.matcher=An,u.max=Lt,u.maxIndex=It,u.mean=function(n,e){let r=0,i=0;if(void 0===e)for(var t of n)null!=t&&(t=+t)>=t&&(++r,i+=t);else{let t=-1;for(var o of n)null!=(o=e(o,++t,n))&&(o=+o)>=o&&(++r,i+=o)}if(r)return i/r},u.median=function(t,n){return zt(t,.5,n)},u.medianIndex=function(t,n){return jt(t,.5,n)},u.merge=Vt,u.min=Rt,u.minIndex=Ot,u.mode=function(n,e){var t,r,i=new rt;if(void 0===e)for(var o of n)null!=o&&o>=o&&i.set(o,(i.get(o)||0)+1);else{let t=-1;for(var a of n)null!=(a=e(a,++t,n))&&a>=a&&i.set(a,(i.get(a)||0)+1)}let u,c=0;for([t,r]of i)r>c&&(c=r,u=t);return u},u.namespace=yn,u.namespaces=mn,u.nice=Nt,u.now=Ei,u.pack=function(){var e=null,r=1,i=1,o=qh;function n(t){var n=t0();return t.x=r/2,t.y=i/2,e?t.eachBefore(d0(e)).eachAfter(p0(o,.5,n)).eachBefore(g0(1)):t.eachBefore(d0(h0)).eachAfter(p0(qh,1,n)).eachAfter(p0(o,t.r/Math.min(r,i),n)).eachBefore(g0(Math.min(r,i)/(2*t.r))),t}return n.radius=function(t){return arguments.length?(e=Gh(t),n):e},n.size=function(t){return arguments.length?(r=+t[0],i=+t[1],n):[r,i]},n.padding=function(t){return arguments.length?(o="function"==typeof t?t:$h(+t),n):o},n},u.packEnclose=function(t){return n0(t,t0())},u.packSiblings=function(t){return f0(t,t0()),t},u.pairs=function(t,n=function(t,n){return[t,n]}){var e,r=[];let i,o=!1;for(e of t)o&&r.push(n(i,e)),i=e,o=!0;return r},u.partition=function(){var e=1,r=1,u=0,i=!1;function n(t){var o,a,n=t.height+1;return t.x0=t.y0=u,t.x1=e,t.y1=r/n,t.eachBefore((o=r,a=n,function(t){t.children&&m0(t,t.x0,o*(t.depth+1)/a,t.x1,o*(t.depth+2)/a);var n=t.x0,e=t.y0,r=t.x1-u,i=t.y1-u;r<n&&(n=r=(n+r)/2),i<e&&(e=i=(e+i)/2),t.x0=n,t.y0=e,t.x1=r,t.y1=i})),i&&t.eachBefore(v0),t}return n.round=function(t){return arguments.length?(i=!!t,n):i},n.size=function(t){return arguments.length?(e=+t[0],r=+t[1],n):[e,r]},n.padding=function(t){return arguments.length?(u=+t,n):u},n},u.path=ha,u.pathRound=function(t=3){return new fa(+t)},u.permute=mt,u.pie=function(){var d=g2,p=p2,g=null,v=b(0),m=b(Jv),y=b(0);function n(e){for(var t,n,r,i=(e=u2(e)).length,o=0,a=new Array(i),u=new Array(i),c=+v.apply(this,arguments),l=Math.min(Jv,Math.max(-Jv,m.apply(this,arguments)-c)),s=Math.min(Math.abs(l)/i,y.apply(this,arguments)),f=s*(l<0?-1:1),h=0;h<i;++h)0<(r=u[a[h]=h]=+d(e[h],h,e))&&(o+=r);for(null!=p?a.sort(function(t,n){return p(u[t],u[n])}):null!=g&&a.sort(function(t,n){return g(e[t],e[n])}),h=0,t=o?(l-i*f)/o:0;h<i;++h,c=n)n=a[h],r=u[n],u[n]={data:e[n],index:h,value:r,startAngle:c,endAngle:n=c+(0<r?r*t:0)+f,padAngle:s};return u}return n.value=function(t){return arguments.length?(d="function"==typeof t?t:b(+t),n):d},n.sortValues=function(t){return arguments.length?(p=t,g=null,n):p},n.sort=function(t){return arguments.length?(g=t,p=null,n):g},n.startAngle=function(t){return arguments.length?(v="function"==typeof t?t:b(+t),n):v},n.endAngle=function(t){return arguments.length?(m="function"==typeof t?t:b(+t),n):m},n.padAngle=function(t){return arguments.length?(y="function"==typeof t?t:b(+t),n):y},n},u.piecewise=hi,u.pointRadial=x2,u.pointer=ue,u.pointers=function(t,n){return t.target&&(t=ae(t),void 0===n&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,t=>ue(t,n))},u.polygonArea=function(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2},u.polygonCentroid=function(t){for(var n,e,r=-1,i=t.length,o=0,a=0,u=t[i-1],c=0;++r<i;)n=u,u=t[r],c+=e=n[0]*u[1]-u[0]*n[1],o+=(n[0]+u[0])*e,a+=(n[1]+u[1])*e;return[o/(c*=3),a/c]},u.polygonContains=function(t,n){for(var e,r,i=t.length,o=t[i-1],a=n[0],u=n[1],c=o[0],l=o[1],s=!1,f=0;f<i;++f)e=(o=t[f])[0],u<(r=o[1])!=u<l&&a<(c-e)*(u-r)/(l-r)+e&&(s=!s),c=e,l=r;return s},u.polygonHull=function(t){if((n=t.length)<3)return null;for(var n,e=new Array(n),r=new Array(n),i=0;i<n;++i)e[i]=[+t[i][0],+t[i][1],i];for(e.sort(P0),i=0;i<n;++i)r[i]=[e[i][0],-e[i][1]];var o=L0(e),a=L0(r),u=a[0]===o[0],c=a[a.length-1]===o[o.length-1],l=[];for(i=o.length-1;0<=i;--i)l.push(t[e[o[i]][2]]);for(i=+u;i<a.length-c;++i)l.push(t[e[a[i]][2]]);return l},u.polygonLength=function(t){for(var n,e,r=-1,i=t.length,o=t[i-1],a=o[0],u=o[1],c=0;++r<i;)n=a,e=u,n-=a=(o=t[r])[0],e-=u=o[1],c+=Math.hypot(n,e);return c},u.precisionFixed=vc,u.precisionPrefix=mc,u.precisionRound=yc,u.quadtree=ju,u.quantile=zt,u.quantileIndex=jt,u.quantileSorted=Ht,u.quantize=function(t,n){for(var e=new Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e},u.quickselect=Bt,u.radialArea=w2,u.radialLine=_2,u.randomBates=Y0,u.randomBernoulli=j0,u.randomBeta=U0,u.randomBinomial=W0,u.randomCauchy=Z0,u.randomExponential=z0,u.randomGamma=X0,u.randomGeometric=V0,u.randomInt=R0,u.randomIrwinHall=F0,u.randomLcg=function(t=Math.random()){let n=0|(0<=t&&t<1?t/$0:Math.abs(t));return()=>(n=1664525*n+1013904223|0,$0*(n>>>0))},u.randomLogNormal=B0,u.randomLogistic=q0,u.randomNormal=O0,u.randomPareto=H0,u.randomPoisson=r,u.randomUniform=I0,u.randomWeibull=G0,u.range=Xt,u.rank=function(t,e=l){if("function"!=typeof t[Symbol.iterator])throw new TypeError("values is not iterable");let r=Array.from(t),i=new Float64Array(r.length),o=(2!==e.length&&(r=r.map(e),e=l),(t,n)=>e(r[t],r[n])),a,u;return(t=Uint32Array.from(r,(t,n)=>n)).sort(e===l?(t,n)=>_t(r[t],r[n]):bt(o)),t.forEach((t,n)=>{var e=o(t,void 0===a?t:a);0<=e?((void 0===a||0<e)&&(a=t,u=n),i[t]=u):i[t]=NaN}),i},u.reduce=function(t,n,e){if("function"!=typeof n)throw new TypeError("reducer is not a function");var r=t[Symbol.iterator]();let i,o,a=-1;if(arguments.length<3){if({done:i,value:e}=r.next(),i)return;++a}for(;{done:i,value:o}=r.next(),!i;)e=n(e,o,++a,t);return e},u.reverse=function(t){if("function"!=typeof t[Symbol.iterator])throw new TypeError("values is not iterable");return Array.from(t).reverse()},u.rgb=He,u.ribbon=function(){return xa()},u.ribbonArrow=function(){return xa(wa)},u.rollup=dt,u.rollups=pt,u.scaleBand=n1,u.scaleDiverging=function t(){var n=h1(_g()(i1));return n.copy=function(){return yg(n,t())},K0.apply(n,arguments)},u.scaleDivergingLog=function t(){var n=_1(_g()).domain([.1,1,10]);return n.copy=function(){return yg(n,t()).base(n.base())},K0.apply(n,arguments)},u.scaleDivergingPow=wg,u.scaleDivergingSqrt=function(){return wg.apply(null,arguments).exponent(.5)},u.scaleDivergingSymlog=function t(){var n=M1(_g());return n.copy=function(){return yg(n,t()).constant(n.constant())},K0.apply(n,arguments)},u.scaleIdentity=function t(n){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return(r.invert=r).domain=r.range=function(t){return arguments.length?(n=Array.from(t,e1),r):n.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return t(n).unknown(e)},n=arguments.length?Array.from(n,e1):[0,1],h1(r)},u.scaleImplicit=Q0,u.scaleLinear=function t(){var n=s1();return n.copy=function(){return c1(n,t())},J0.apply(n,arguments),h1(n)},u.scaleLog=function t(){let n=_1(l1()).domain([1,10]);return n.copy=()=>c1(n,t()).base(n.base()),J0.apply(n,arguments),n},u.scaleOrdinal=t1,u.scalePoint=function(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(n1.apply(null,arguments).paddingInner(1))},u.scalePow=k1,u.scaleQuantile=function t(){var n,e=[],r=[],i=[];function o(){var t=0,n=Math.max(1,r.length);for(i=new Array(n-1);++t<n;)i[t-1]=Ht(e,t/n);return a}function a(t){return null==t||isNaN(t=+t)?n:r[A(i,t)]}return a.invertExtent=function(t){var n=r.indexOf(t);return n<0?[NaN,NaN]:[0<n?i[n-1]:e[0],n<i.length?i[n]:e[e.length-1]]},a.domain=function(t){if(!arguments.length)return e.slice();e=[];for(var n of t)null==n||isNaN(n=+n)||e.push(n);return e.sort(l),o()},a.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},a.unknown=function(t){return arguments.length?(n=t,a):n},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(e).range(r).unknown(n)},J0.apply(a,arguments)},u.scaleQuantize=function t(){var n,e=0,r=1,i=1,o=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[A(o,t,0,i)]:n}function c(){var t=-1;for(o=new Array(i);++t<i;)o[t]=((t+1)*r-(t-i)*e)/(i+1);return u}return u.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,c()):[e,r]},u.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var n=a.indexOf(t);return n<0?[NaN,NaN]:n<1?[e,o[0]]:i<=n?[o[i-1],r]:[o[n-1],o[n]]},u.unknown=function(t){return arguments.length&&(n=t),u},u.thresholds=function(){return o.slice()},u.copy=function(){return t().domain([e,r]).range(a).unknown(n)},J0.apply(h1(u),arguments)},u.scaleRadial=function t(){var e,r=s1(),n=[0,1],i=!1;function o(t){t=r(t);var n=Math.sign(t)*Math.sqrt(Math.abs(t));return isNaN(n)?e:i?Math.round(n):n}return o.invert=function(t){return r.invert(C1(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,e1)).map(C1)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},J0.apply(o,arguments),h1(o)},u.scaleSequential=function t(){var n=h1(mg()(i1));return n.copy=function(){return yg(n,t())},K0.apply(n,arguments)},u.scaleSequentialLog=function t(){var n=_1(mg()).domain([1,10]);return n.copy=function(){return yg(n,t()).base(n.base())},K0.apply(n,arguments)},u.scaleSequentialPow=bg,u.scaleSequentialQuantile=function t(){var r=[],e=i1;function i(t){if(null!=t&&!isNaN(t=+t))return e((A(r,t,1)-1)/(r.length-1))}return i.domain=function(t){if(!arguments.length)return r.slice();r=[];for(var n of t)null==n||isNaN(n=+n)||r.push(n);return r.sort(l),i},i.interpolator=function(t){return arguments.length?(e=t,i):e},i.range=function(){return r.map((t,n)=>e(n/(r.length-1)))},i.quantiles=function(e){return Array.from({length:e+1},(t,n)=>zt(r,n/e))},i.copy=function(){return t(e).domain(r)},K0.apply(i,arguments)},u.scaleSequentialSqrt=function(){return bg.apply(null,arguments).exponent(.5)},u.scaleSequentialSymlog=function t(){var n=M1(mg());return n.copy=function(){return yg(n,t()).constant(n.constant())},K0.apply(n,arguments)},u.scaleSqrt=function(){return k1.apply(null,arguments).exponent(.5)},u.scaleSymlog=function t(){var n=M1(l1());return n.copy=function(){return c1(n,t()).constant(n.constant())},J0.apply(n,arguments)},u.scaleThreshold=function t(){var n,e=[.5],r=[0,1],i=1;function o(t){return null!=t&&t<=t?r[A(e,t,0,i)]:n}return o.domain=function(t){return arguments.length?(e=Array.from(t),i=Math.min(e.length,r.length-1),o):e.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),i=Math.min(e.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t().domain(e).range(r).unknown(n)},J0.apply(o,arguments)},u.scaleTime=function(){return J0.apply(vg(Vd,Xd,Od,Pd,rd,$1,W1,j1,z1,u.timeFormat).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},u.scaleUtc=function(){return J0.apply(vg(Hd,jd,Fd,Id,yd,K1,Z1,X1,z1,u.utcFormat).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},u.scan=function(t,n){var e=Ut(t,n);return e<0?void 0:e},u.schemeAccent=Mg,u.schemeBlues=wv,u.schemeBrBG=Lg,u.schemeBuGn=Jg,u.schemeBuPu=Qg,u.schemeCategory10=xg,u.schemeDark2=Eg,u.schemeGnBu=nv,u.schemeGreens=Mv,u.schemeGreys=Av,u.schemeObservable10=Ag,u.schemeOrRd=rv,u.schemeOranges=Dv,u.schemePRGn=Rg,u.schemePaired=Sg,u.schemePastel1=Tg,u.schemePastel2=kg,u.schemePiYG=Bg,u.schemePuBu=uv,u.schemePuBuGn=ov,u.schemePuOr=Yg,u.schemePuRd=lv,u.schemePurples=Tv,u.schemeRdBu=Hg,u.schemeRdGy=Vg,u.schemeRdPu=fv,u.schemeRdYlBu=Ug,u.schemeRdYlGn=Gg,u.schemeReds=Cv,u.schemeSet1=Cg,u.schemeSet2=Ng,u.schemeSet3=Dg,u.schemeSpectral=qg,u.schemeTableau10=Pg,u.schemeYlGn=gv,u.schemeYlGnBu=dv,u.schemeYlOrBr=mv,u.schemeYlOrRd=bv,u.select=H,u.selectAll=function(t){return"string"==typeof t?new M([document.querySelectorAll(t)],[document.documentElement]):new M([xn(t)],ne)},u.selection=ee,u.selector=wn,u.selectorAll=En,u.shuffle=Wt,u.shuffler=Gt,u.some=function(t,n){if("function"!=typeof n)throw new TypeError("test is not a function");let e=-1;for(var r of t)if(n(r,++e,t))return!0;return!1},u.sort=yt,u.stack=function(){var u=b([]),c=Om,l=Rm,s=Bm;function n(t){var n,e,r,i=Array.from(u.apply(this,arguments),Fm),o=i.length,a=-1;for(r of t)for(n=0,++a;n<o;++n)(i[n][a]=[0,+s(r,i[n].key,a,t)]).data=r;for(n=0,e=u2(c(i));n<o;++n)i[e[n]].index=n;return l(i,e),i}return n.keys=function(t){return arguments.length?(u="function"==typeof t?t:b(Array.from(t)),n):u},n.value=function(t){return arguments.length?(s="function"==typeof t?t:b(+t),n):s},n.order=function(t){return arguments.length?(c=null==t?Om:"function"==typeof t?t:b(Array.from(t)),n):c},n.offset=function(t){return arguments.length?(l=null==t?Rm:t,n):l},n},u.stackOffsetDiverging=function(t,n){if(0<(u=t.length))for(var e,r,i,o,a,u,c=0,l=t[n[0]].length;c<l;++c)for(e=o=a=0;e<u;++e)0<(i=(r=t[n[e]][c])[1]-r[0])?(r[0]=o,r[1]=o+=i):i<0?(r[1]=a,r[0]=a+=i):(r[0]=0,r[1]=i)},u.stackOffsetExpand=function(t,n){if(0<(r=t.length)){for(var e,r,i,o=0,a=t[0].length;o<a;++o){for(i=e=0;e<r;++e)i+=t[e][o][1]||0;if(i)for(e=0;e<r;++e)t[e][o][1]/=i}Rm(t,n)}},u.stackOffsetNone=Rm,u.stackOffsetSilhouette=function(t,n){if(0<(e=t.length)){for(var e,r=0,i=t[n[0]],o=i.length;r<o;++r){for(var a=0,u=0;a<e;++a)u+=t[a][r][1]||0;i[r][1]+=i[r][0]=-u/2}Rm(t,n)}},u.stackOffsetWiggle=function(t,n){if(0<(i=t.length)&&0<(r=(e=t[n[0]]).length)){for(var e,r,i,o=0,a=1;a<r;++a){for(var u=0,c=0,l=0;u<i;++u){for(var s=t[n[u]],f=s[a][1]||0,h=(f-(s[a-1][1]||0))/2,d=0;d<u;++d){var p=t[n[d]];h+=(p[a][1]||0)-(p[a-1][1]||0)}c+=f,l+=h*f}e[a-1][1]+=e[a-1][0]=o,c&&(o-=l/c)}e[a-1][1]+=e[a-1][0]=o,Rm(t,n)}},u.stackOrderAppearance=Ym,u.stackOrderAscending=Hm,u.stackOrderDescending=function(t){return Hm(t).reverse()},u.stackOrderInsideOut=function(t){for(var n,e=t.length,r=t.map(jm),i=Ym(t),o=0,a=0,u=[],c=[],l=0;l<e;++l)n=i[l],(o<a?(o+=r[n],u):(a+=r[n],c)).push(n);return c.reverse().concat(u)},u.stackOrderNone=Om,u.stackOrderReverse=function(t){return Om(t).reverse()},u.stratify=function(){var p,g=w0,v=x0;function n(i){var n,t,e,r,o,a,u,c,l=Array.from(i),s=g,f=v,h=new Map;if(null!=p){let e=l.map((t,n)=>{var t=p(t,n,i),e=(t=""+t).length;return"/"===(t=E0(t,e-1)&&!E0(t,e-2)?t.slice(0,-1):t)[0]?t:"/"+t}),r=e.map(M0);var d=new Set(e).add("");for(let t of r)d.has(t)||(d.add(t),e.push(t),r.push(M0(t)),l.push(_0));s=(t,n)=>e[n],f=(t,n)=>r[n]}for(e=0,n=l.length;e<n;++e)t=l[e],a=l[e]=new Wh(t),null!=(u=s(t,e,i))&&(u+="")&&(c=a.id=u,h.set(c,h.has(c)?b0:a)),null!=(u=f(t,e,i))&&(u+="")&&(a.parent=u);for(e=0;e<n;++e)if(u=(a=l[e]).parent){if(!(o=h.get(u)))throw new Error("missing: "+u);if(o===b0)throw new Error("ambiguous: "+u);o.children?o.children.push(a):o.children=[a],a.parent=o}else{if(r)throw new Error("multiple roots");r=a}if(!r)throw new Error("no root");if(null!=p){for(;r.data===_0&&1===r.children.length;)r=r.children[0],--n;for(let t=l.length-1;0<=t&&(a=l[t]).data===_0;--t)a.data=null}if(r.parent=y0,r.eachBefore(function(t){t.depth=t.parent.depth+1,--n}).eachBefore(Uh),r.parent=null,0<n)throw new Error("cycle");return r}return n.id=function(t){return arguments.length?(g=Gh(t),n):g},n.parentId=function(t){return arguments.length?(v=Gh(t),n):v},n.path=function(t){return arguments.length?(p=Gh(t),n):p},n},u.style=Fn,u.subset=function(t,n){return Jt(n,t)},u.sum=function(n,e){let r=0;if(void 0===e)for(var t of n)(t=+t)&&(r+=t);else{let t=-1;for(var i of n)(i=+e(i,++t,n))&&(r+=i)}return r},u.superset=Jt,u.svg=Bu,u.symbol=function(n,e){let r=null,i=Qv(o);function o(){let t;if(r=r||(t=i()),n.apply(this,arguments).draw(r,+e.apply(this,arguments)),t)return r=null,t+""||null}return n="function"==typeof n?n:b(n||L2),e="function"==typeof e?e:b(void 0===e?64:+e),o.type=function(t){return arguments.length?(n="function"==typeof t?t:b(t),o):n},o.size=function(t){return arguments.length?(e="function"==typeof t?t:b(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o},u.symbolAsterisk=P2,u.symbolCircle=L2,u.symbolCross=I2,u.symbolDiamond=B2,u.symbolDiamond2=F2,u.symbolPlus=Y2,u.symbolSquare=z2,u.symbolSquare2=H2,u.symbolStar=U2,u.symbolTimes=nm,u.symbolTriangle=G2,u.symbolTriangle2=q2,u.symbolWye=tm,u.symbolX=nm,u.symbols=j2,u.symbolsFill=j2,u.symbolsStroke=em,u.text=Cu,u.thresholdFreedmanDiaconis=function(t,n,e){var r=$(t),i=zt(t,.75)-zt(t,.25);return r&&i?Math.ceil((e-n)/(2*i*Math.pow(r,-1/3))):1},u.thresholdScott=function(t,n,e){var r=$(t),i=nt(t);return r&&i?Math.ceil((e-n)*Math.cbrt(r)/(3.49*i)):1},u.thresholdSturges=Dt,u.tickFormat=f1,u.tickIncrement=kt,u.tickStep=Ct,u.ticks=Tt,u.timeDay=$1,u.timeDays=J1,u.timeFormatDefaultLocale=fg,u.timeFormatLocale=Zd,u.timeFriday=cd,u.timeFridays=gd,u.timeHour=W1,u.timeHours=G1,u.timeInterval=i,u.timeMillisecond=P1,u.timeMilliseconds=L1,u.timeMinute=j1,u.timeMinutes=V1,u.timeMonday=id,u.timeMondays=fd,u.timeMonth=Pd,u.timeMonths=Ld,u.timeSaturday=ld,u.timeSaturdays=vd,u.timeSecond=z1,u.timeSeconds=H1,u.timeSunday=rd,u.timeSundays=sd,u.timeThursday=ud,u.timeThursdays=pd,u.timeTickInterval=Xd,u.timeTicks=Vd,u.timeTuesday=od,u.timeTuesdays=hd,u.timeWednesday=ad,u.timeWednesdays=dd,u.timeWeek=rd,u.timeWeeks=sd,u.timeYear=Od,u.timeYears=Bd,u.timeout=Pi,u.timer=Ti,u.timerFlush=ki,u.transition=ro,u.transpose=Zt,u.tree=function(){var h=A0,c=1,l=1,s=null;function n(t){var n,e,r,i,o,a,u=function(t){for(var n,e,r,i,o,a=new k0(t,0),u=[a];n=u.pop();)if(r=n._.children)for(n.children=new Array(o=r.length),i=o-1;0<=i;--i)u.push(e=n.children[i]=new k0(r[i],i)),e.parent=n;return(a.parent=new k0(null,0)).children=[a],a}(t);return u.eachAfter(f),u.parent.m=-u.z,u.eachBefore(d),s?t.eachBefore(p):((r=e=n=t).eachBefore(function(t){t.x<n.x&&(n=t),t.x>e.x&&(e=t),t.depth>r.depth&&(r=t)}),u=n===e?1:h(n,e)/2,i=u-n.x,o=c/(e.x+u+i),a=l/(r.depth||1),t.eachBefore(function(t){t.x=(t.x+i)*o,t.y=t.depth*a})),t}function f(t){var n=t.children,e=t.parent.children,r=t.i?e[t.i-1]:null;if(n){for(var i,o=0,a=0,u=t.children,c=u.length;0<=--c;)(i=u[c]).z+=o,i.m+=o,o+=i.s+(a+=i.c);n=(n[0].z+n[n.length-1].z)/2;r?(t.z=r.z+h(t._,r._),t.m=t.z-n):t.z=n}else r&&(t.z=r.z+h(t._,r._));t.parent.A=function(t,n,e){if(n){for(var r,i=t,o=t,a=n,u=i.parent.children[0],c=i.m,l=o.m,s=a.m,f=u.m;a=T0(a),i=S0(i),a&&i;)u=S0(u),(o=T0(o)).a=t,0<(r=a.z+s-i.z-c+h(a._,i._))&&(function(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}(function(t,n,e){return t.a.parent===n.parent?t.a:e}(a,t,e),t,r),c+=r,l+=r),s+=a.m,c+=i.m,f+=u.m,l+=o.m;a&&!T0(o)&&(o.t=a,o.m+=s-l),i&&!S0(u)&&(u.t=i,u.m+=c-f,e=t)}return e}(t,r,t.parent.A||e[0])}function d(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function p(t){t.x*=c,t.y=t.depth*l}return n.separation=function(t){return arguments.length?(h=t,n):h},n.size=function(t){return arguments.length?(s=!1,c=+t[0],l=+t[1],n):s?null:[c,l]},n.nodeSize=function(t){return arguments.length?(s=!0,c=+t[0],l=+t[1],n):s?[c,l]:null},n},u.treemap=function(){var a=D0,n=!1,e=1,r=1,u=[0],c=qh,l=qh,s=qh,f=qh,h=qh;function i(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(o),u=[0],n&&t.eachBefore(v0),t}function o(t){var n=u[t.depth],e=t.x0+n,r=t.y0+n,i=t.x1-n,o=t.y1-n;i<e&&(e=i=(e+i)/2),o<r&&(r=o=(r+o)/2),t.x0=e,t.y0=r,t.x1=i,t.y1=o,t.children&&(n=u[t.depth+1]=c(t)/2,e+=h(t)-n,r+=l(t)-n,(i-=s(t)-n)<e&&(e=i=(e+i)/2),(o-=f(t)-n)<r&&(r=o=(r+o)/2),a(t,e,r,i,o))}return i.round=function(t){return arguments.length?(n=!!t,i):n},i.size=function(t){return arguments.length?(e=+t[0],r=+t[1],i):[e,r]},i.tile=function(t){return arguments.length?(a=Zh(t),i):a},i.padding=function(t){return arguments.length?i.paddingInner(t).paddingOuter(t):i.paddingInner()},i.paddingInner=function(t){return arguments.length?(c="function"==typeof t?t:$h(+t),i):c},i.paddingOuter=function(t){return arguments.length?i.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):i.paddingTop()},i.paddingTop=function(t){return arguments.length?(l="function"==typeof t?t:$h(+t),i):l},i.paddingRight=function(t){return arguments.length?(s="function"==typeof t?t:$h(+t),i):s},i.paddingBottom=function(t){return arguments.length?(f="function"==typeof t?t:$h(+t),i):f},i.paddingLeft=function(t){return arguments.length?(h="function"==typeof t?t:$h(+t),i):h},i},u.treemapBinary=function(t,n,e,r,i){var o,a,g=t.children,u=g.length,v=new Array(u+1);for(v[0]=a=o=0;o<u;++o)v[o+1]=a+=g[o].value;!function t(n,e,r,i,o,a,u){if(e-1<=n)return(c=g[n]).x0=i,c.y0=o,c.x1=a,void(c.y1=u);var c=v[n],l=r/2+c,s=n+1,f=e-1;for(;s<f;){var h=s+f>>>1;v[h]<l?s=1+h:f=h}l-v[s-1]<v[s]-l&&n+1<s&&--s;var c=v[s]-c,d=r-c;{var p;u-o<a-i?(t(n,s,c,i,o,p=r?(i*d+a*c)/r:a,u),t(s,e,d,p,o,a,u)):(t(n,s,c,i,o,a,p=r?(o*d+u*c)/r:u),t(s,e,d,i,p,a,u))}}(0,u,t.value,n,e,r,i)},u.treemapDice=m0,u.treemapResquarify=n,u.treemapSlice=C0,u.treemapSliceDice=function(t,n,e,r,i){(1&t.depth?C0:m0)(t,n,e,r,i)},u.treemapSquarify=D0,u.tsv=Pu,u.tsvFormat=wu,u.tsvFormatBody=xu,u.tsvFormatRow=Eu,u.tsvFormatRows=Mu,u.tsvFormatValue=yu,u.tsvParse=bu,u.tsvParseRows=_u,u.union=function(...t){var n,e=new it;for(n of t)for(var r of n)e.add(r);return e},u.unixDay=td,u.unixDays=nd,u.utcDay=K1,u.utcDays=Q1,u.utcFriday=Md,u.utcFridays=Nd,u.utcHour=Z1,u.utcHours=q1,u.utcMillisecond=P1,u.utcMilliseconds=L1,u.utcMinute=X1,u.utcMinutes=U1,u.utcMonday=bd,u.utcMondays=Sd,u.utcMonth=Id,u.utcMonths=Rd,u.utcSaturday=Ed,u.utcSaturdays=Dd,u.utcSecond=z1,u.utcSeconds=H1,u.utcSunday=yd,u.utcSundays=Ad,u.utcThursday=xd,u.utcThursdays=Cd,u.utcTickInterval=jd,u.utcTicks=Hd,u.utcTuesday=_d,u.utcTuesdays=Td,u.utcWednesday=wd,u.utcWednesdays=kd,u.utcWeek=yd,u.utcWeeks=Ad,u.utcYear=Fd,u.utcYears=Yd,u.variance=tt,u.version="7.9.0",u.window=Bn,u.xml=Ru,u.zip=function(){return Zt(arguments)},u.zoom=function(){var l,s,c,f=$m,h=Jm,p=ny,a=Qm,n=ty,u=[0,1/0],g=[[-1/0,-1/0],[1/0,1/0]],d=250,v=ii,e=dn("start","zoom","end"),m=500,y=150,b=0,_=10;function w(t){t.property("__zoom",Km).on("wheel.zoom",i,{passive:!1}).on("mousedown.zoom",o).on("dblclick.zoom",T).filter(n).on("touchstart.zoom",k).on("touchmove.zoom",C).on("touchend.zoom touchcancel.zoom",N).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(t,n){return(n=Math.max(u[0],Math.min(u[1],n)))===t.k?t:new Um(n,t.x,t.y)}function M(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new Um(t.k,r,i)}function E(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function A(t,e,c,l){t.on("start.zoom",function(){S(this,arguments).event(l).start()}).on("interrupt.zoom end.zoom",function(){S(this,arguments).event(l).end()}).tween("zoom",function(){var t=arguments,r=S(this,t).event(l),n=h.apply(this,t),i=null==c?E(n):"function"==typeof c?c.apply(this,t):c,o=Math.max(n[1][0]-n[0][0],n[1][1]-n[0][1]),n=this.__zoom,a="function"==typeof e?e.apply(this,t):e,u=v(n.invert(i).concat(o/n.k),a.invert(i).concat(o/a.k));return function(t){var n,e;t=1===t?a:(n=u(t),new Um(e=o/n[2],i[0]-n[0]*e,i[1]-n[1]*e)),r.zoom(null,t)}})}function S(t,n,e){return!e&&t.__zooming||new r(t,n)}function r(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=h.apply(t,n),this.taps=0}function i(t,...n){if(f.apply(this,arguments)){var e=S(this,n).event(t),r=this.__zoom,i=Math.max(u[0],Math.min(u[1],r.k*Math.pow(2,a.apply(this,arguments)))),o=ue(t);if(e.wheel)e.mouse[0][0]===o[0]&&e.mouse[0][1]===o[1]||(e.mouse[1]=r.invert(e.mouse[0]=o)),clearTimeout(e.wheel);else{if(r.k===i)return;e.mouse=[o,r.invert(o)],Wi(this),e.start()}qm(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},y),e.zoom("mouse",p(M(x(r,i),e.mouse[0],e.mouse[1]),e.extent,g))}}function o(t,...n){var r,i,e,o,a,u;!c&&f.apply(this,arguments)&&(r=t.currentTarget,i=S(this,n,!0).event(t),e=H(t.view).on("mousemove.zoom",function(t){{var n,e;qm(t),i.moved||(n=t.clientX-a,e=t.clientY-u,i.moved=b<n*n+e*e)}i.event(t).zoom("mouse",p(M(i.that.__zoom,i.mouse[0]=ue(t,r),i.mouse[1]),i.extent,g))},!0).on("mouseup.zoom",function(t){e.on("mousemove.zoom mouseup.zoom",null),de(t.view,i.moved),qm(t),i.event(t).end()},!0),o=ue(t,r),a=t.clientX,u=t.clientY,he(t.view),Zm(t),i.mouse=[o,this.__zoom.invert(o)],Wi(this),i.start())}function T(t,...n){var e,r,i,o;f.apply(this,arguments)&&(o=this.__zoom,e=ue(t.changedTouches?t.changedTouches[0]:t,this),r=o.invert(e),i=o.k*(t.shiftKey?.5:2),o=p(M(x(o,i),e,r),h.apply(this,n),g),qm(t),0<d?H(this).transition().duration(d).call(A,o,e,t):H(this).call(w.transform,o,e,t))}function k(t,...n){if(f.apply(this,arguments)){var e,r,i,o,a=t.touches,u=a.length,c=S(this,n,t.changedTouches.length===u).event(t);for(Zm(t),r=0;r<u;++r)o=[o=ue(i=a[r],this),this.__zoom.invert(o),i.identifier],c.touch0?c.touch1||c.touch0[2]===o[2]||(c.touch1=o,c.taps=0):(c.touch0=o,e=!0,c.taps=1+!!l);l=l&&clearTimeout(l),e&&(c.taps<2&&(s=o[0],l=setTimeout(function(){l=null},m)),Wi(this),c.start())}}function C(t,...n){if(this.__zooming){var e,r=S(this,n).event(t),i=t.changedTouches,o=i.length;for(qm(t),e=0;e<o;++e)d=ue(h=i[e],this),r.touch0&&r.touch0[2]===h.identifier?r.touch0[0]=d:r.touch1&&r.touch1[2]===h.identifier&&(r.touch1[0]=d);if(h=r.that.__zoom,r.touch1)var a=r.touch0[0],u=r.touch0[1],c=r.touch1[0],l=r.touch1[1],s=(s=c[0]-a[0])*s+(s=c[1]-a[1])*s,f=(f=l[0]-u[0])*f+(f=l[1]-u[1])*f,h=x(h,Math.sqrt(s/f)),d=[(a[0]+c[0])/2,(a[1]+c[1])/2],s=[(u[0]+l[0])/2,(u[1]+l[1])/2];else{if(!r.touch0)return;d=r.touch0[0],s=r.touch0[1]}r.zoom("touch",p(M(h,d,s),r.extent,g))}}function N(t,...n){if(this.__zooming){var e,r,i,o=S(this,n).event(t),a=t.changedTouches,u=a.length;for(Zm(t),c&&clearTimeout(c),c=setTimeout(function(){c=null},m),e=0;e<u;++e)i=a[e],o.touch0&&o.touch0[2]===i.identifier?delete o.touch0:o.touch1&&o.touch1[2]===i.identifier&&delete o.touch1;o.touch1&&!o.touch0&&(o.touch0=o.touch1,delete o.touch1),o.touch0?o.touch0[1]=this.__zoom.invert(o.touch0[0]):(o.end(),2===o.taps&&(i=ue(i,this),Math.hypot(s[0]-i[0],s[1]-i[1])<_)&&(r=H(this).on("dblclick.zoom"))&&r.apply(this,arguments))}}return w.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",Km),t!==i?A(t,n,e,r):i.interrupt().each(function(){S(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},w.scaleBy=function(t,n,e,r){w.scaleTo(t,function(){return this.__zoom.k*("function"==typeof n?n.apply(this,arguments):n)},e,r)},w.scaleTo=function(t,o,a,n){w.transform(t,function(){var t=h.apply(this,arguments),n=this.__zoom,e=null==a?E(t):"function"==typeof a?a.apply(this,arguments):a,r=n.invert(e),i="function"==typeof o?o.apply(this,arguments):o;return p(M(x(n,i),e,r),t,g)},a,n)},w.translateBy=function(t,n,e,r){w.transform(t,function(){return p(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),h.apply(this,arguments),g)},null,r)},w.translateTo=function(t,r,i,o,n){w.transform(t,function(){var t=h.apply(this,arguments),n=this.__zoom,e=null==o?E(t):"function"==typeof o?o.apply(this,arguments):o;return p(Wm.translate(e[0],e[1]).scale(n.k).translate("function"==typeof r?-r.apply(this,arguments):-r,"function"==typeof i?-i.apply(this,arguments):-i),t,g)},o,n)},r.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this).emit("start"),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=H(this.that).datum();e.call(t,this.that,new Xm(t,{sourceEvent:this.sourceEvent,target:w,type:t,transform:this.that.__zoom,dispatch:e}),n)}},w.wheelDelta=function(t){return arguments.length?(a="function"==typeof t?t:Vm(+t),w):a},w.filter=function(t){return arguments.length?(f="function"==typeof t?t:Vm(!!t),w):f},w.touchable=function(t){return arguments.length?(n="function"==typeof t?t:Vm(!!t),w):n},w.extent=function(t){return arguments.length?(h="function"==typeof t?t:Vm([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),w):h},w.scaleExtent=function(t){return arguments.length?(u[0]=+t[0],u[1]=+t[1],w):[u[0],u[1]]},w.translateExtent=function(t){return arguments.length?(g[0][0]=+t[0][0],g[1][0]=+t[1][0],g[0][1]=+t[0][1],g[1][1]=+t[1][1],w):[[g[0][0],g[0][1]],[g[1][0],g[1][1]]]},w.constrain=function(t){return arguments.length?(p=t,w):p},w.duration=function(t){return arguments.length?(d=+t,w):d},w.interpolate=function(t){return arguments.length?(v=t,w):v},w.on=function(){var t=e.on.apply(e,arguments);return t===e?w:t},w.clickDistance=function(t){return arguments.length?(b=(t=+t)*t,w):Math.sqrt(b)},w.tapDistance=function(t){return arguments.length?(_=+t,w):_},w},u.zoomIdentity=Wm,u.zoomTransform=Gm});var QRCode,FingerprintJS=function(t){var i=function(){return(i=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};function g(t,a,u,c){return new(u=u||Promise)(function(e,n){function r(t){try{o(c.next(t))}catch(t){n(t)}}function i(t){try{o(c.throw(t))}catch(t){n(t)}}function o(t){var n;t.done?e(t.value):((n=t.value)instanceof u?n:new u(function(t){t(n)})).then(r,i)}o((c=c.apply(t,a||[])).next())})}function v(r,i){var o,a,u,c={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]},l={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l;function t(e){return function(t){var n=[e,t];if(o)throw new TypeError("Generator is already executing.");for(;c=l&&n[l=0]?0:c;)try{if(o=1,a&&(u=2&n[0]?a.return:n[0]?a.throw||((u=a.return)&&u.call(a),0):a.next)&&!(u=u.call(a,n[1])).done)return u;switch(a=0,(n=u?[2&n[0],u.value]:n)[0]){case 0:case 1:u=n;break;case 4:return c.label++,{value:n[1],done:!1};case 5:c.label++,a=n[1],n=[0];continue;case 7:n=c.ops.pop(),c.trys.pop();continue;default:if(!(u=0<(u=c.trys).length&&u[u.length-1])&&(6===n[0]||2===n[0])){c=0;continue}if(3===n[0]&&(!u||n[1]>u[0]&&n[1]<u[3]))c.label=n[1];else if(6===n[0]&&c.label<u[1])c.label=u[1],u=n;else{if(!(u&&c.label<u[2])){u[2]&&c.ops.pop(),c.trys.pop();continue}c.label=u[2],c.ops.push(n)}}n=i.call(r,c)}catch(t){n=[6,t],a=0}finally{o=u=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}}}function u(t,n,e){if(e||2===arguments.length)for(var r,i=0,o=n.length;i<o;i++)!r&&i in n||((r=r||Array.prototype.slice.call(n,0,i))[i]=n[i]);return t.concat(r||Array.prototype.slice.call(n))}var c="3.4.2";function m(n,e){return new Promise(function(t){return setTimeout(t,n,e)})}function s(t){return t&&"function"==typeof t.then}function l(t,n){try{var e=t();s(e)?e.then(function(t){return n(!0,t)},function(t){return n(!1,t)}):n(!0,e)}catch(t){n(!1,t)}}function f(o,a,u){return void 0===u&&(u=16),g(this,void 0,void 0,function(){var n,e,r,i;return v(this,function(t){switch(t.label){case 0:n=Array(o.length),e=Date.now(),r=0,t.label=1;case 1:return r<o.length?(n[r]=a(o[r],r),i=Date.now(),e+u<=i?(e=i,[4,m(0)]):[3,3]):[3,4];case 2:t.sent(),t.label=3;case 3:return++r,[3,1];case 4:return[2,n]}})})}function h(t){t.then(void 0,function(){})}function d(t,n){t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]],n=[n[0]>>>16,65535&n[0],n[1]>>>16,65535&n[1]];var e=[0,0,0,0];return e[3]+=t[3]+n[3],e[2]+=e[3]>>>16,e[3]&=65535,e[2]+=t[2]+n[2],e[1]+=e[2]>>>16,e[2]&=65535,e[1]+=t[1]+n[1],e[0]+=e[1]>>>16,e[1]&=65535,e[0]+=t[0]+n[0],e[0]&=65535,[e[0]<<16|e[1],e[2]<<16|e[3]]}function p(t,n){t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]],n=[n[0]>>>16,65535&n[0],n[1]>>>16,65535&n[1]];var e=[0,0,0,0];return e[3]+=t[3]*n[3],e[2]+=e[3]>>>16,e[3]&=65535,e[2]+=t[2]*n[3],e[1]+=e[2]>>>16,e[2]&=65535,e[2]+=t[3]*n[2],e[1]+=e[2]>>>16,e[2]&=65535,e[1]+=t[1]*n[3],e[0]+=e[1]>>>16,e[1]&=65535,e[1]+=t[2]*n[2],e[0]+=e[1]>>>16,e[1]&=65535,e[1]+=t[3]*n[1],e[0]+=e[1]>>>16,e[1]&=65535,e[0]+=t[0]*n[3]+t[1]*n[2]+t[2]*n[1]+t[3]*n[0],e[0]&=65535,[e[0]<<16|e[1],e[2]<<16|e[3]]}function y(t,n){return 32===(n%=64)?[t[1],t[0]]:n<32?[t[0]<<n|t[1]>>>32-n,t[1]<<n|t[0]>>>32-n]:[t[1]<<(n-=32)|t[0]>>>32-n,t[0]<<n|t[1]>>>32-n]}function b(t,n){return 0===(n%=64)?t:n<32?[t[0]<<n|t[1]>>>32-n,t[1]<<n]:[t[1]<<n-32,0]}function _(t,n){return[t[0]^n[0],t[1]^n[1]]}function O(t){return t=_(t,[0,t[0]>>>1]),t=_(t=p(t,[4283543511,3981806797]),[0,t[0]>>>1]),t=_(t=p(t,[3301882366,444984403]),[0,t[0]>>>1])}function B(t,n){for(var e=(t=t||"").length%16,r=t.length-e,i=[0,n=n||0],o=[0,n],a=[0,0],u=[0,0],c=[2277735313,289559509],l=[1291169091,658871167],s=0;s<r;s+=16)a=[255&t.charCodeAt(s+4)|(255&t.charCodeAt(s+5))<<8|(255&t.charCodeAt(s+6))<<16|(255&t.charCodeAt(s+7))<<24,255&t.charCodeAt(s)|(255&t.charCodeAt(s+1))<<8|(255&t.charCodeAt(s+2))<<16|(255&t.charCodeAt(s+3))<<24],u=[255&t.charCodeAt(s+12)|(255&t.charCodeAt(s+13))<<8|(255&t.charCodeAt(s+14))<<16|(255&t.charCodeAt(s+15))<<24,255&t.charCodeAt(s+8)|(255&t.charCodeAt(s+9))<<8|(255&t.charCodeAt(s+10))<<16|(255&t.charCodeAt(s+11))<<24],a=y(a=p(a,c),31),i=d(i=y(i=_(i,a=p(a,l)),27),o),i=d(p(i,[0,5]),[0,1390208809]),u=y(u=p(u,l),33),o=d(o=y(o=_(o,u=p(u,c)),31),i),o=d(p(o,[0,5]),[0,944331445]);switch(a=[0,0],u=[0,0],e){case 15:u=_(u,b([0,t.charCodeAt(s+14)],48));case 14:u=_(u,b([0,t.charCodeAt(s+13)],40));case 13:u=_(u,b([0,t.charCodeAt(s+12)],32));case 12:u=_(u,b([0,t.charCodeAt(s+11)],24));case 11:u=_(u,b([0,t.charCodeAt(s+10)],16));case 10:u=_(u,b([0,t.charCodeAt(s+9)],8));case 9:u=p(u=_(u,[0,t.charCodeAt(s+8)]),l),o=_(o,u=p(u=y(u,33),c));case 8:a=_(a,b([0,t.charCodeAt(s+7)],56));case 7:a=_(a,b([0,t.charCodeAt(s+6)],48));case 6:a=_(a,b([0,t.charCodeAt(s+5)],40));case 5:a=_(a,b([0,t.charCodeAt(s+4)],32));case 4:a=_(a,b([0,t.charCodeAt(s+3)],24));case 3:a=_(a,b([0,t.charCodeAt(s+2)],16));case 2:a=_(a,b([0,t.charCodeAt(s+1)],8));case 1:a=p(a=_(a,[0,t.charCodeAt(s)]),c),i=_(i,a=p(a=y(a,31),l))}return i=d(i=_(i,[0,t.length]),o=_(o,[0,t.length])),o=d(o,i),i=d(i=O(i),o=O(o)),o=d(o,i),("00000000"+(i[0]>>>0).toString(16)).slice(-8)+("00000000"+(i[1]>>>0).toString(16)).slice(-8)+("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)}function r(t){return parseInt(t)}function n(t){return parseFloat(t)}function e(t,n){return"number"==typeof t&&isNaN(t)?n:t}function w(t){return t.reduce(function(t,n){return t+(n?1:0)},0)}function F(t,n){var e;return void 0===n&&(n=1),1<=Math.abs(n)?Math.round(t/n)*n:(e=1/n,Math.round(t*e)/e)}function Y(t){return t&&"object"==typeof t&&"message"in t?t:{message:t}}function z(t){return"function"!=typeof t}function H(i,o,a){var u=Object.keys(i).filter(function(t){for(var n=a,e=t,r=0,i=n.length;r<i;++r)if(n[r]===e)return!1;return!0}),c=f(u,function(t){return n=i[t],e=o,h(r=new Promise(function(r){var i=Date.now();l(n.bind(null,e),function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e,o=Date.now()-i;return t[0]?z(e=t[1])?r(function(){return{value:e,duration:o}}):void r(function(){return new Promise(function(r){var i=Date.now();l(e,function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=o+Date.now()-i;if(!t[0])return r({error:Y(t[1]),duration:e});r({value:t[1],duration:e})})})}):r(function(){return{error:Y(t[1]),duration:o}})})})),function(){return r.then(function(t){return t()})};var n,e,r});return h(c),function(){return g(this,void 0,void 0,function(){var n,e,r,i;return v(this,function(t){switch(t.label){case 0:return[4,c];case 1:return[4,f(t.sent(),function(t){var n=t();return h(n),n})];case 2:return n=t.sent(),[4,Promise.all(n)];case 3:for(e=t.sent(),r={},i=0;i<u.length;++i)r[u[i]]=e[i];return[2,r]}})})}}function o(){var t=window,n=navigator;return 4<=w(["MSCSSMatrix"in t,"msSetImmediate"in t,"msIndexedDB"in t,"msMaxTouchPoints"in n,"msPointerEnabled"in n])}function j(){var t=window,n=navigator;return 3<=w(["msWriteProfilerMark"in t,"MSStream"in t,"msLaunchUri"in n,"msSaveBlob"in n])&&!o()}function x(){var t=window,n=navigator;return 5<=w(["webkitPersistentStorage"in n,"webkitTemporaryStorage"in n,0===n.vendor.indexOf("Google"),"webkitResolveLocalFileSystemURL"in t,"BatteryManager"in t,"webkitMediaStream"in t,"webkitSpeechGrammar"in t])}function M(){var t=window,n=navigator;return 4<=w(["ApplePayError"in t,"CSSPrimitiveValue"in t,"Counter"in t,0===n.vendor.indexOf("Apple"),"getStorageUpdates"in n,"WebKitMediaKeys"in t])}function a(){var t=window;return 3<=w(["safari"in t,!("DeviceMotionEvent"in t),!("ongestureend"in t),!("standalone"in navigator)])}function V(){var t,n=window;return 4<=w(["buildID"in navigator,"MozAppearance"in(null!=(t=null==(t=document.documentElement)?void 0:t.style)?t:{}),"onmozfullscreenchange"in n,"mozInnerScreenX"in n,"CSSMozDocumentRule"in n,"CanvasCaptureMediaStream"in n])}function X(){var t=document;return t.fullscreenElement||t.msFullscreenElement||t.mozFullScreenElement||t.webkitFullscreenElement||null}function E(){var t,n=x(),e=V();return!(!n&&!e)&&2<=w(["onorientationchange"in(t=window),"orientation"in t,n&&!("SharedWorker"in t),e&&/android/i.test(navigator.appVersion)])}function U(t){var n=new Error(t);return n.name=t,n}function A(n,c,e){var r;return void 0===e&&(e=50),g(this,void 0,void 0,function(){var a,u;return v(this,function(t){switch(t.label){case 0:a=document,t.label=1;case 1:return a.body?[3,3]:[4,m(e)];case 2:return t.sent(),[3,1];case 3:u=a.createElement("iframe"),t.label=4;case 4:return t.trys.push([4,,10,11]),[4,new Promise(function(t,n){var e=!1,r=function(){e=!0,t()},i=(u.onload=r,u.onerror=function(t){e=!0,n(t)},u.style),o=(i.setProperty("display","block","important"),i.position="absolute",i.top="0",i.left="0",i.visibility="hidden",c&&"srcdoc"in u?u.srcdoc=c:u.src="about:blank",a.body.appendChild(u),function(){var t;e||("complete"===(null==(t=null==(t=u.contentWindow)?void 0:t.document)?void 0:t.readyState)?r():setTimeout(o,10))});o()})];case 5:t.sent(),t.label=6;case 6:return null!=(r=null==(r=u.contentWindow)?void 0:r.document)&&r.body?[3,8]:[4,m(e)];case 7:return t.sent(),[3,6];case 8:return[4,n(u,u.contentWindow)];case 9:return[2,t.sent()];case 10:return null!=(r=u.parentNode)&&r.removeChild(u),[7];case 11:return[2]}})})}function W(t){for(var n=function(t){for(var n,e="Unexpected syntax '".concat(t,"'"),r=/^\s*([a-z-]*)(.*)$/i.exec(t),i=r[1]||void 0,o={},a=/([.:#][\w-]+|\[.+?\])/gi,u=function(t,n){o[t]=o[t]||[],o[t].push(n)};;){var c=a.exec(r[2]);if(!c)break;var l=c[0];switch(l[0]){case".":u("class",l.slice(1));break;case"#":u("id",l.slice(1));break;case"[":var s=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(l);if(!s)throw new Error(e);u(s[1],null!=(n=null!=(n=s[4])?n:s[5])?n:"");break;default:throw new Error(e)}}return[i,o]}(t),e=n[0],r=n[1],i=document.createElement(null!=e?e:"div"),o=0,a=Object.keys(r);o<a.length;o++){var u=a[o],c=r[u].join(" ");if("style"===u){p=d=g=h=f=s=l=void 0;for(var l=i.style,s=c,f=0,h=s.split(";");f<h.length;f++){var d,p,g=h[f],g=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(g);g&&(d=g[1],p=g[2],g=g[4],l.setProperty(d,p,g||""))}}else i.setAttribute(u,c)}return i}var S=["monospace","sans-serif","serif"],G=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function T(t){return t.toDataURL()}var k,Z,q=2500;function $(){var n,t=this;return void 0===Z&&(n=function(){var t=C();Z=N(t)?setTimeout(n,q):void(k=t)})(),function(){return g(t,void 0,void 0,function(){var e;return v(this,function(t){switch(t.label){case 0:return N(e=C())?k?[2,u([],k,!0)]:X()?[4,((n=document).exitFullscreen||n.msExitFullscreen||n.mozCancelFullScreen||n.webkitExitFullscreen).call(n)]:[3,2]:[3,2];case 1:t.sent(),e=C(),t.label=2;case 2:return N(e)||(k=e),[2,e]}var n})})}}function C(){var t=screen;return[e(n(t.availTop),null),e(n(t.width)-n(t.availWidth)-e(n(t.availLeft),0),null),e(n(t.height)-n(t.availHeight)-e(n(t.availTop),0),null),e(n(t.availLeft),null)]}function N(t){for(var n=0;n<4;++n)if(t[n])return;return 1}function J(t){t.style.setProperty("display","block","important")}function K(t){return matchMedia("(inverted-colors: ".concat(t,")")).matches}function Q(t){return matchMedia("(forced-colors: ".concat(t,")")).matches}function D(t){return matchMedia("(prefers-contrast: ".concat(t,")")).matches}function tt(t){return matchMedia("(prefers-reduced-motion: ".concat(t,")")).matches}function nt(t){return matchMedia("(dynamic-range: ".concat(t,")")).matches}function P(){return 0}var L=Math;var I={default:[],apple:[{font:"-apple-system-body"}],serif:[{fontFamily:"serif"}],sans:[{fontFamily:"sans-serif"}],mono:[{fontFamily:"monospace"}],min:[{fontSize:"1px"}],system:[{fontFamily:"system-ui"}]};var et={fonts:function(){return A(function(t,n){var r=n.document,e=r.body,i=(e.style.fontSize="48px",r.createElement("div")),o={},a={},u=function(t){var n=r.createElement("span"),e=n.style;return e.position="absolute",e.top="0",e.left="0",e.fontFamily=t,n.textContent="mmMwWLliI0O&1",i.appendChild(n),n},c=function(t,n){return u("'".concat(t,"',").concat(n))},l=S.map(u),s=function(){for(var t={},n=0,e=G;n<e.length;n++)!function(n){t[n]=S.map(function(t){return c(n,t)})}(e[n]);return t}();e.appendChild(i);for(var f=0;f<S.length;f++)o[S[f]]=l[f].offsetWidth,a[S[f]]=l[f].offsetHeight;return G.filter(function(t){return e=s[t],S.some(function(t,n){return e[n].offsetWidth!==o[t]||e[n].offsetHeight!==a[t]});var e})})},domBlockers:function(t){var p=(void 0===t?{}:t).debug;return g(this,void 0,void 0,function(){var s,f,h,d;return v(this,function(t){switch(t.label){case 0:return M()||E()?(l=atob,s={abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",'[title="ALIENBOLA" i]',l("I0JveC1CYW5uZXItYWRz")],abpvn:[".quangcao","#mobileCatfish",l("LmNsb3NlLWFkcw=="),'[id^="bn_bottom_fixed_"]',"#pmadv"],adBlockFinland:[".mainostila",l("LnNwb25zb3JpdA=="),".ylamainos",l("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),l("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi",l("YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd")],adBlockWarningRemoval:["#adblock-honeypot",".adblocker-root",".wp_adblock_detect",l("LmhlYWRlci1ibG9ja2VkLWFk"),l("I2FkX2Jsb2NrZXI=")],adGuardAnnoyances:[".hs-sosyal","#cookieconsentdiv",'div[class^="app_gdpr"]',".as-oil",'[data-cypress="soft-push-notification-modal"]'],adGuardBase:[".BetterJsPopOverlay",l("I2FkXzMwMFgyNTA="),l("I2Jhbm5lcmZsb2F0MjI="),l("I2NhbXBhaWduLWJhbm5lcg=="),l("I0FkLUNvbnRlbnQ=")],adGuardChinese:[l("LlppX2FkX2FfSA=="),l("YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd"),"#widget-quan",l("YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd"),l("YVtocmVmKj0iLjE5NTZobC5jb20vIl0=")],adGuardFrench:["#pavePub",l("LmFkLWRlc2t0b3AtcmVjdGFuZ2xl"),".mobile_adhesion",".widgetadv",l("LmFkc19iYW4=")],adGuardGerman:['aside[data-portal-id="leaderboard"]'],adGuardJapanese:["#kauli_yad_1",l("YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0="),l("Ll9wb3BJbl9pbmZpbml0ZV9hZA=="),l("LmFkZ29vZ2xl"),l("Ll9faXNib29zdFJldHVybkFk")],adGuardMobile:[l("YW1wLWF1dG8tYWRz"),l("LmFtcF9hZA=="),'amp-embed[type="24smi"]',"#mgid_iframe1",l("I2FkX2ludmlld19hcmVh")],adGuardRussian:[l("YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0="),l("LnJlY2xhbWE="),'div[id^="smi2adblock"]',l("ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd"),"#psyduckpockeball"],adGuardSocial:[l("YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0="),l("YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0="),".etsy-tweet","#inlineShare",".popup-social"],adGuardSpanishPortuguese:["#barraPublicidade","#Publicidade","#publiEspecial","#queTooltip",".cnt-publi"],adGuardTrackingProtection:["#qoo-counter",l("YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=="),l("YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0="),l("YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=="),"#top100counter"],adGuardTurkish:["#backkapat",l("I3Jla2xhbWk="),l("YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0="),l("YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd"),l("YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ==")],bulgarian:[l("dGQjZnJlZW5ldF90YWJsZV9hZHM="),"#ea_intext_div",".lapni-pop-over","#xenium_hot_offers"],easyList:[".yb-floorad",l("LndpZGdldF9wb19hZHNfd2lkZ2V0"),l("LnRyYWZmaWNqdW5reS1hZA=="),".textad_headline",l("LnNwb25zb3JlZC10ZXh0LWxpbmtz")],easyListChina:[l("LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=="),l("LmZyb250cGFnZUFkdk0="),"#taotaole","#aafoot.top_box",".cfa_popup"],easyListCookie:[".ezmob-footer",".cc-CookieWarning","[data-cookie-number]",l("LmF3LWNvb2tpZS1iYW5uZXI="),".sygnal24-gdpr-modal-wrap"],easyListCzechSlovak:["#onlajny-stickers",l("I3Jla2xhbW5pLWJveA=="),l("LnJla2xhbWEtbWVnYWJvYXJk"),".sklik",l("W2lkXj0ic2tsaWtSZWtsYW1hIl0=")],easyListDutch:[l("I2FkdmVydGVudGll"),l("I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=="),".adstekst",l("YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0="),"#semilo-lrectangle"],easyListGermany:["#SSpotIMPopSlider",l("LnNwb25zb3JsaW5rZ3J1ZW4="),l("I3dlcmJ1bmdza3k="),l("I3Jla2xhbWUtcmVjaHRzLW1pdHRl"),l("YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0=")],easyListItaly:[l("LmJveF9hZHZfYW5udW5jaQ=="),".sb-box-pubbliredazionale",l("YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd"),l("YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd"),l("YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ==")],easyListLithuania:[l("LnJla2xhbW9zX3RhcnBhcw=="),l("LnJla2xhbW9zX251b3JvZG9z"),l("aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd"),l("aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd"),l("aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd")],estonian:[l("QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==")],fanboyAnnoyances:["#ac-lre-player",".navigate-to-top","#subscribe_popup",".newsletter_holder","#back-top"],fanboyAntiFacebook:[".util-bar-module-firefly-visible"],fanboyEnhancedTrackers:[".open.pushModal","#issuem-leaky-paywall-articles-zero-remaining-nag","#sovrn_container",'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',".BlockNag__Card"],fanboySocial:["#FollowUs","#meteored_share","#social_follow",".article-sharer",".community__social-desc"],frellwitSwedish:[l("YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=="),l("YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=="),"article.category-samarbete",l("ZGl2LmhvbGlkQWRz"),"ul.adsmodern"],greekAdBlock:[l("QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd"),l("QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=="),l("QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd"),"DIV.agores300","TABLE.advright"],hungarian:["#cemp_doboz",".optimonk-iframe-container",l("LmFkX19tYWlu"),l("W2NsYXNzKj0iR29vZ2xlQWRzIl0="),"#hirdetesek_box"],iDontCareAboutCookies:['.alert-info[data-block-track*="CookieNotice"]',".ModuleTemplateCookieIndicator",".o--cookies--container","#cookies-policy-sticky","#stickyCookieBar"],icelandicAbp:[l("QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==")],latvian:[l("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0OiA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0="),l("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[l("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),l("I2xpdmVyZUFkV3JhcHBlcg=="),l("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),l("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[l("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",l("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),l("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),l("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[l("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),l("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),l("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",l("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),l("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),l("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),l("ZGl2I3NrYXBpZWNfYWQ=")],ro:[l("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),l("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),l("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),l("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd"),'a[href^="/url/"]'],ruAd:[l("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),l("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),l("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",l("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),l("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",l("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]},f=Object.keys(s),[4,function(c){var l;return g(this,void 0,void 0,function(){var n,e,r,i,o,a,u;return v(this,function(t){switch(t.label){case 0:for(n=document,e=n.createElement("div"),r=new Array(c.length),i={},J(e),u=0;u<c.length;++u)"DIALOG"===(o=W(c[u])).tagName&&o.show(),J(a=n.createElement("div")),a.appendChild(o),e.appendChild(a),r[u]=o;t.label=1;case 1:return n.body?[3,3]:[4,m(50)];case 2:return t.sent(),[3,1];case 3:n.body.appendChild(e);try{for(u=0;u<c.length;++u)r[u].offsetParent||(i[c[u]]=!0)}finally{null!=(l=e.parentNode)&&l.removeChild(e)}return[2,i]}})})}((d=[]).concat.apply(d,f.map(function(t){return s[t]})))]):[2,void 0];case 1:if(h=t.sent(),p)for(var n=s,e=h,r=0,i=Object.keys(n);r<i.length;r++){var o=i[r];"\n".concat(o,":");for(var a=0,u=n[o];a<u.length;a++){var c=u[a];"\n  ".concat(e[c]?"🚫":"➡️"," ").concat(c)}}return(d=f.filter(function(t){var n=s[t];return w(n.map(function(t){return h[t]}))>.6*n.length})).sort(),[2,d]}var l})})},fontPreferences:function(){var o=function(t,n){for(var e={},r={},i=0,o=Object.keys(I);i<o.length;i++){var a=o[i],u=I[a],c=u[0],l=void 0===c?{}:c,c=u[1],u=void 0===c?"mmMwWLliI0fiflO&1":c,s=t.createElement("span");s.textContent=u,s.style.whiteSpace="nowrap";for(var f=0,h=Object.keys(l);f<h.length;f++){var d=h[f],p=l[d];void 0!==p&&(s.style[d]=p)}e[a]=s,n.appendChild(t.createElement("br")),n.appendChild(s)}for(var g=0,v=Object.keys(I);g<v.length;g++)r[a=v[g]]=e[a].getBoundingClientRect().width;return r},a=void 0;return void 0===a&&(a=4e3),A(function(t,n){var e=n.document,r=e.body,i=r.style,i=(i.width="".concat(a,"px"),i.webkitTextSizeAdjust=i.textSizeAdjust="none",x()?r.style.zoom="".concat(1/n.devicePixelRatio):M()&&(r.style.zoom="reset"),e.createElement("div"));return i.textContent=u([],Array(a/20<<0),!0).map(function(){return"word"}).join(" "),r.appendChild(i),o(e,r)},'<!doctype html><html><head><meta name="viewport" content="width=device-width, initial-scale=1">')},audio:function(){var t,n,e,c,l,r,i=window;return(i=i.OfflineAudioContext||i.webkitOfflineAudioContext)?!M()||a()||function(){var t=window;return 3<=w(["DOMRectList"in t,"RTCPeerConnectionIceEvent"in t,"SVGGeometryElement"in t,"ontransitioncancel"in t])}()?((t=(i=new i(1,5e3,44100)).createOscillator()).type="triangle",t.frequency.value=1e4,(n=i.createDynamicsCompressor()).threshold.value=-50,n.knee.value=40,n.ratio.value=12,n.attack.value=0,n.release.value=.25,t.connect(n),n.connect(i.destination),t.start(0),c=i,l=function(){},n=[new Promise(function(n,e){var r=!1,i=0,o=0,a=(c.oncomplete=function(t){return n(t.renderedBuffer)},function(){setTimeout(function(){return e(U("timeout"))},Math.min(500,o+5e3-Date.now()))}),u=function(){try{var t=c.startRendering();switch(s(t)&&h(t),c.state){case"running":o=Date.now(),r&&a();break;case"suspended":document.hidden||i++,r&&3<=i?e(U("suspended")):setTimeout(u,500)}}catch(t){e(t)}};u(),l=function(){r||(r=!0,0<o&&a())}}),l],e=n[1],h(r=n[0].then(function(t){for(var n=t.getChannelData(0).subarray(4500),e=0,r=0;r<n.length;++r)e+=Math.abs(n[r]);return e},function(t){if("timeout"===t.name||"suspended"===t.name)return-3;throw t})),function(){return e(),r}):-1:-2},screenFrame:function(){var t=this,r=$();return function(){return g(t,void 0,void 0,function(){var n,e;return v(this,function(t){switch(t.label){case 0:return[4,r()];case 1:return n=t.sent(),[2,[(e=function(t){return null===t?null:F(t,10)})(n[0]),e(n[1]),e(n[2]),e(n[3])]]}})})}},osCpu:function(){return navigator.oscpu},languages:function(){var t=navigator,n=[],e=t.language||t.userLanguage||t.browserLanguage||t.systemLanguage;return void 0!==e&&n.push([e]),Array.isArray(t.languages)?x()&&3<=w([!("MediaSettingsRange"in(e=window)),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])||n.push(t.languages):"string"==typeof t.languages&&(e=t.languages)&&n.push(e.split(",")),n},colorDepth:function(){return window.screen.colorDepth},deviceMemory:function(){return e(n(navigator.deviceMemory),void 0)},screenResolution:function(){function t(t){return e(r(t),null)}var n=screen;return(n=[t(n.width),t(n.height)]).sort().reverse(),n},hardwareConcurrency:function(){return e(r(navigator.hardwareConcurrency),void 0)},timezone:function(){var t=null==(t=window.Intl)?void 0:t.DateTimeFormat;if(t){t=(new t).resolvedOptions().timeZone;if(t)return t}return t=(new Date).getFullYear(),t=-Math.max(n(new Date(t,0,1).getTimezoneOffset()),n(new Date(t,6,1).getTimezoneOffset())),"UTC".concat(0<=t?"+":"").concat(Math.abs(t))},sessionStorage:function(){try{return!!window.sessionStorage}catch(t){return!0}},localStorage:function(){try{return!!window.localStorage}catch(t){return!0}},indexedDB:function(){if(!o()&&!j())try{return!!window.indexedDB}catch(t){return!0}},openDatabase:function(){return!!window.openDatabase},cpuClass:function(){return navigator.cpuClass},platform:function(){var t,n=navigator.platform;return"MacIntel"===n&&M()&&!a()?"iPad"===navigator.platform||(t=(t=screen).width/t.height,2<=w(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,.65<t&&t<1.53]))?"iPad":"iPhone":n},plugins:function(){var t=navigator.plugins;if(t){for(var n=[],e=0;e<t.length;++e){var r=t[e];if(r){for(var i=[],o=0;o<r.length;++o){var a=r[o];i.push({type:a.type,suffixes:a.suffixes})}n.push({name:r.name,description:r.description,mimeTypes:i})}}return n}},canvas:function(){var t,n,e=!1,r=((r=document.createElement("canvas")).width=1,r.height=1,[r,r.getContext("2d")]),i=r[0];if(n=i,(r=r[1])&&n.toDataURL){(n=r).rect(0,0,10,10),n.rect(2,2,6,6),e=!n.isPointInPath(5,5,"evenodd"),n=r,(u=i).width=240,u.height=60,n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(100,1,62,20),n.fillStyle="#069",n.font='11pt "Times New Roman"',o="Cwm fjordbank gly ".concat(String.fromCharCode(55357,56835)),n.fillText(o,2,15),n.fillStyle="rgba(102, 204, 0, 0.2)",n.font="18pt Arial",n.fillText(o,4,45);var o=T(i);if(o!==T(i))t=a="unstable";else{var a=o,u=i,c=r;u.width=122,u.height=110,c.globalCompositeOperation="multiply";for(var l=0,s=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];l<s.length;l++){var f=s[l],h=f[0],d=f[1],f=f[2];c.fillStyle=h,c.beginPath(),c.arc(d,f,40,0,2*Math.PI,!0),c.closePath(),c.fill()}c.fillStyle="#f9c",c.arc(60,60,60,0,2*Math.PI,!0),c.arc(60,60,20,0,2*Math.PI,!0),c.fill("evenodd"),t=T(i)}}else t=a="";return{winding:e,geometry:t,text:a}},touchSupport:function(){var n,t=navigator,e=0;void 0!==t.maxTouchPoints?e=r(t.maxTouchPoints):void 0!==t.msMaxTouchPoints&&(e=t.msMaxTouchPoints);try{document.createEvent("TouchEvent"),n=!0}catch(t){n=!1}return{maxTouchPoints:e,touchEvent:n,touchStart:"ontouchstart"in window}},vendor:function(){return navigator.vendor||""},vendorFlavors:function(){for(var t=[],n=0,e=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];n<e.length;n++){var r=e[n],i=window[r];i&&"object"==typeof i&&t.push(r)}return t.sort()},cookiesEnabled:function(){var t=document;try{t.cookie="cookietest=1; SameSite=Strict;";var n=-1!==t.cookie.indexOf("cookietest=");return t.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",n}catch(t){return!1}},colorGamut:function(){for(var t=0,n=["rec2020","p3","srgb"];t<n.length;t++){var e=n[t];if(matchMedia("(color-gamut: ".concat(e,")")).matches)return e}},invertedColors:function(){return!!K("inverted")||!K("none")&&void 0},forcedColors:function(){return!!Q("active")||!Q("none")&&void 0},monochrome:function(){if(matchMedia("(min-monochrome: 0)").matches){for(var t=0;t<=100;++t)if(matchMedia("(max-monochrome: ".concat(t,")")).matches)return t;throw new Error("Too high value")}},contrast:function(){return D("no-preference")?0:D("high")||D("more")?1:D("low")||D("less")?-1:D("forced")?10:void 0},reducedMotion:function(){return!!tt("reduce")||!tt("no-preference")&&void 0},hdr:function(){return!!nt("high")||!nt("standard")&&void 0},math:function(){var t,n=L.acos||P,e=L.acosh||P,r=L.asin||P,i=L.asinh||P,o=L.atanh||P,a=L.atan||P,u=L.sin||P,c=L.sinh||P,l=L.cos||P,s=L.cosh||P,f=L.tan||P,h=L.tanh||P,d=L.exp||P,p=L.expm1||P,g=L.log1p||P;return{acos:n(.12312423423423424),acosh:e(1e308),acoshPf:(t=1e154,L.log(t+L.sqrt(t*t-1))),asin:r(.12312423423423424),asinh:i(1),asinhPf:(t=1,L.log(t+L.sqrt(t*t+1))),atanh:o(.5),atanhPf:(t=.5,L.log((1+t)/(1-t))/2),atan:a(.5),sin:u(-1e300),sinh:c(1),sinhPf:(t=1,L.exp(t)-1/L.exp(t)/2),cos:l(10.000000000123),cosh:s(1),coshPf:(t=1,(L.exp(t)+1/L.exp(t))/2),tan:f(-1e300),tanh:h(1),tanhPf:(t=1,(L.exp(2*t)-1)/(L.exp(2*t)+1)),exp:d(1),expm1:p(1),expm1Pf:L.exp(1)-1,log1p:g(10),log1pPf:L.log(11),powPI:L.pow(L.PI,-100)}},videoCard:function(){var t=document.createElement("canvas"),n=null!=(n=t.getContext("webgl"))?n:t.getContext("experimental-webgl");if(n&&"getExtension"in n){t=n.getExtension("WEBGL_debug_renderer_info");if(t)return{vendor:(n.getParameter(t.UNMASKED_VENDOR_WEBGL)||"").toString(),renderer:(n.getParameter(t.UNMASKED_RENDERER_WEBGL)||"").toString()}}},pdfViewerEnabled:function(){return navigator.pdfViewerEnabled},architecture:function(){var t=new Float32Array(1),n=new Uint8Array(t.buffer);return t[0]=1/0,t[0]=t[0]-t[0],n[3]}};var rt="$ if upgrade to Pro: https://fpjs.dev/pro";function it(t){var n=function(t){if(E())return.4;if(M())return a()?.5:.3;var n=t.platform.value||"";if(/^Win/.test(n))return.6;if(/^Mac/.test(n))return.5;return.7}(t),e=F(.99+.01*n,1e-4);return{score:n,comment:rt.replace(/\$/g,"".concat(e))}}function ot(t){return JSON.stringify(t,function(t,n){var e,r;return n instanceof Error?i({name:(e=n).name,message:e.message,stack:null==(r=e.stack)?void 0:r.split("\n")},e):n},2)}function R(t){return B(function(t){for(var n="",e=0,r=Object.keys(t).sort();e<r.length;e++){var i=r[e],o=t[i],o=o.error?"error":JSON.stringify(o.value);n+="".concat(n?"|":"").concat(i.replace(/([:|\\])/g,"\\$1"),":").concat(o)}return n}(t))}function at(t){return void 0===(n=2*(t=t=void 0===t?50:t))&&(n=1/0),(e=window.requestIdleCallback)?new Promise(function(t){return e.call(window,function(){return t()},{timeout:n})}):m(Math.min(t,n));var n,e}function ut(a,u){Date.now();return{get:function(o){return g(this,void 0,void 0,function(){var i;return v(this,function(t){switch(t.label){case 0:return Date.now(),[4,a()];case 1:return i=t.sent(),r=it(n=i),i={get visitorId(){return e=void 0===e?R(this.components):e},set visitorId(t){e=t},confidence:r,components:n,version:c},u||null!=o&&o.debug,[2,i]}var n,e,r})})}}}function ct(t){var n=void 0===t?{}:t,e=n.delayFallback,r=n.debug,n=n.monitoring,i=void 0===n||n;return g(this,void 0,void 0,function(){return v(this,function(t){switch(t.label){case 0:if(i&&!(window.__fpjs_d_m||.001<=Math.random()))try{var n=new XMLHttpRequest;n.open("get","https://m1.openfpcdn.io/fingerprintjs/v".concat(c,"/npm-monitoring"),!0),n.send()}catch(t){}return[4,at(e)];case 1:return t.sent(),[2,ut(H(et,{debug:r},[]),r)]}})})}var lt={load:ct,hashComponents:R,componentsToDebugString:ot},st=B;return t.componentsToDebugString=ot,t.default=lt,t.getFullscreenElement=X,t.getScreenFrame=$,t.hashComponents=R,t.isAndroid=E,t.isChromium=x,t.isDesktopSafari=a,t.isEdgeHTML=j,t.isGecko=V,t.isTrident=o,t.isWebKit=M,t.load=ct,t.loadSources=H,t.murmurX64Hash128=st,t.prepareForSources=at,t.sources=et,t.transformSource=function(e,r){function i(n){return z(n)?r(n):function(){var t=n();return s(t)?t.then(r):r(t)}}return function(t){var n=e(t);return s(n)?n.then(i):i(n)}},t.withIframe=A,Object.defineProperty(t,"__esModule",{value:!0}),t}({});!function(){function e(t){this.mode=o.MODE_8BIT_BYTE,this.data=t,this.parsedData=[];for(var n=0,e=this.data.length;n<e;n++){var r=[],i=this.data.charCodeAt(n);65536<i?(r[0]=240|(1835008&i)>>>18,r[1]=128|(258048&i)>>>12,r[2]=128|(4032&i)>>>6,r[3]=128|63&i):2048<i?(r[0]=224|(61440&i)>>>12,r[1]=128|(4032&i)>>>6,r[2]=128|63&i):128<i?(r[0]=192|(1984&i)>>>6,r[1]=128|63&i):r[0]=i,this.parsedData.push(r)}this.parsedData=Array.prototype.concat.apply([],this.parsedData),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}function c(t,n){this.typeNumber=t,this.errorCorrectLevel=n,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}e.prototype={getLength:function(t){return this.parsedData.length},write:function(t){for(var n=0,e=this.parsedData.length;n<e;n++)t.put(this.parsedData[n],8)}},c.prototype={addData:function(t){var n=new e(t);this.dataList.push(n),this.dataCache=null},isDark:function(t,n){if(t<0||this.moduleCount<=t||n<0||this.moduleCount<=n)throw new Error(t+","+n);return this.modules[t][n]},getModuleCount:function(){return this.moduleCount},make:function(){this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,n){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var e=0;e<this.moduleCount;e++){this.modules[e]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[e][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,n),7<=this.typeNumber&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=c.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,n)},setupPositionProbePattern:function(t,n){for(var e=-1;e<=7;e++)if(!(t+e<=-1||this.moduleCount<=t+e))for(var r=-1;r<=7;r++)n+r<=-1||this.moduleCount<=n+r||(this.modules[t+e][n+r]=0<=e&&e<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==e||6==e)||2<=e&&e<=4&&2<=r&&r<=4)},getBestMaskPattern:function(){for(var t=0,n=0,e=0;e<8;e++){this.makeImpl(!0,e);var r=v.getLostPoint(this);(0==e||r<t)&&(t=r,n=e)}return n},createMovieClip:function(t,n,e){var r=t.createEmptyMovieClip(n,e);this.make();for(var i=0;i<this.modules.length;i++)for(var o=+i,a=0;a<this.modules[i].length;a++){var u=+a;this.modules[i][a]&&(r.beginFill(0,100),r.moveTo(u,o),r.lineTo(1+u,o),r.lineTo(1+u,1+o),r.lineTo(u,1+o),r.endFill())}return r},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var n=8;n<this.moduleCount-8;n++)null==this.modules[6][n]&&(this.modules[6][n]=n%2==0)},setupPositionAdjustPattern:function(){for(var t=v.getPatternPosition(this.typeNumber),n=0;n<t.length;n++)for(var e=0;e<t.length;e++){var r=t[n],i=t[e];if(null==this.modules[r][i])for(var o=-2;o<=2;o++)for(var a=-2;a<=2;a++)this.modules[r+o][i+a]=-2==o||2==o||-2==a||2==a||0==o&&0==a}},setupTypeNumber:function(t){for(var n=v.getBCHTypeNumber(this.typeNumber),e=0;e<18;e++){var r=!t&&1==(n>>e&1);this.modules[Math.floor(e/3)][e%3+this.moduleCount-8-3]=r}for(e=0;e<18;e++){r=!t&&1==(n>>e&1);this.modules[e%3+this.moduleCount-8-3][Math.floor(e/3)]=r}},setupTypeInfo:function(t,n){for(var e=this.errorCorrectLevel<<3|n,r=v.getBCHTypeInfo(e),i=0;i<15;i++){var o=!t&&1==(r>>i&1);i<6?this.modules[i][8]=o:i<8?this.modules[i+1][8]=o:this.modules[this.moduleCount-15+i][8]=o}for(i=0;i<15;i++){o=!t&&1==(r>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=o:i<9?this.modules[8][15-i-1+1]=o:this.modules[8][15-i-1]=o}this.modules[this.moduleCount-8][8]=!t},mapData:function(t,n){for(var e=-1,r=this.moduleCount-1,i=7,o=0,a=this.moduleCount-1;0<a;a-=2)for(6==a&&a--;;){for(var u,c,l=0;l<2;l++)null==this.modules[r][a-l]&&(u=!1,o<t.length&&(u=1==(t[o]>>>i&1)),c=v.getMask(n,r,a-l),this.modules[r][a-l]=u=c?!u:u,-1==--i)&&(o++,i=7);if((r+=e)<0||this.moduleCount<=r){r-=e,e=-e;break}}}},c.PAD0=236,c.PAD1=17,c.createData=function(t,n,e){for(var r=g.getRSBlocks(t,n),i=new y,o=0;o<e.length;o++){var a=e[o];i.put(a.mode,4),i.put(a.getLength(),v.getLengthInBits(a.mode,t)),a.write(i)}for(var u=0,o=0;o<r.length;o++)u+=r[o].dataCount;if(i.getLengthInBits()>8*u)throw new Error("code length overflow. ("+i.getLengthInBits()+">"+8*u+")");for(i.getLengthInBits()+4<=8*u&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;;){if(i.getLengthInBits()>=8*u)break;if(i.put(c.PAD0,8),i.getLengthInBits()>=8*u)break;i.put(c.PAD1,8)}return c.createBytes(i,r)},c.createBytes=function(t,n){for(var e=0,r=0,i=0,o=new Array(n.length),a=new Array(n.length),u=0;u<n.length;u++){var c=n[u].dataCount,l=n[u].totalCount-c,r=Math.max(r,c),i=Math.max(i,l);o[u]=new Array(c);for(var s=0;s<o[u].length;s++)o[u][s]=255&t.buffer[s+e];e+=c;var c=v.getErrorCorrectPolynomial(l),f=new m(o[u],c.getLength()-1).mod(c);a[u]=new Array(c.getLength()-1);for(s=0;s<a[u].length;s++){var h=s+f.getLength()-a[u].length;a[u][s]=0<=h?f.get(h):0}}for(var d=0,s=0;s<n.length;s++)d+=n[s].totalCount;for(var p=new Array(d),g=0,s=0;s<r;s++)for(u=0;u<n.length;u++)s<o[u].length&&(p[g++]=o[u][s]);for(s=0;s<i;s++)for(u=0;u<n.length;u++)s<a[u].length&&(p[g++]=a[u][s]);return p};for(var o={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},l={L:1,M:0,Q:3,H:2},r=0,i=1,a=2,u=3,s=4,f=5,h=6,d=7,v={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){for(var n=t<<10;0<=v.getBCHDigit(n)-v.getBCHDigit(v.G15);)n^=v.G15<<v.getBCHDigit(n)-v.getBCHDigit(v.G15);return(t<<10|n)^v.G15_MASK},getBCHTypeNumber:function(t){for(var n=t<<12;0<=v.getBCHDigit(n)-v.getBCHDigit(v.G18);)n^=v.G18<<v.getBCHDigit(n)-v.getBCHDigit(v.G18);return t<<12|n},getBCHDigit:function(t){for(var n=0;0!=t;)n++,t>>>=1;return n},getPatternPosition:function(t){return v.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,n,e){switch(t){case r:return(n+e)%2==0;case i:return n%2==0;case a:return e%3==0;case u:return(n+e)%3==0;case s:return(Math.floor(n/2)+Math.floor(e/3))%2==0;case f:return n*e%2+n*e%3==0;case h:return(n*e%2+n*e%3)%2==0;case d:return(n*e%3+(n+e)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var n=new m([1],0),e=0;e<t;e++)n=n.multiply(new m([1,p.gexp(e)],0));return n},getLengthInBits:function(t,n){if(1<=n&&n<10)switch(t){case o.MODE_NUMBER:return 10;case o.MODE_ALPHA_NUM:return 9;case o.MODE_8BIT_BYTE:case o.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(n<27)switch(t){case o.MODE_NUMBER:return 12;case o.MODE_ALPHA_NUM:return 11;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(n<41))throw new Error("type:"+n);switch(t){case o.MODE_NUMBER:return 14;case o.MODE_ALPHA_NUM:return 13;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var n=t.getModuleCount(),e=0,r=0;r<n;r++)for(var i=0;i<n;i++){for(var o=0,a=t.isDark(r,i),u=-1;u<=1;u++)if(!(r+u<0||n<=r+u))for(var c=-1;c<=1;c++)i+c<0||n<=i+c||0==u&&0==c||a==t.isDark(r+u,i+c)&&o++;5<o&&(e+=3+o-5)}for(r=0;r<n-1;r++)for(i=0;i<n-1;i++){var l=0;t.isDark(r,i)&&l++,t.isDark(r+1,i)&&l++,t.isDark(r,i+1)&&l++,t.isDark(r+1,i+1)&&l++,0!=l&&4!=l||(e+=3)}for(r=0;r<n;r++)for(i=0;i<n-6;i++)t.isDark(r,i)&&!t.isDark(r,i+1)&&t.isDark(r,i+2)&&t.isDark(r,i+3)&&t.isDark(r,i+4)&&!t.isDark(r,i+5)&&t.isDark(r,i+6)&&(e+=40);for(i=0;i<n;i++)for(r=0;r<n-6;r++)t.isDark(r,i)&&!t.isDark(r+1,i)&&t.isDark(r+2,i)&&t.isDark(r+3,i)&&t.isDark(r+4,i)&&!t.isDark(r+5,i)&&t.isDark(r+6,i)&&(e+=40);for(var s=0,i=0;i<n;i++)for(r=0;r<n;r++)t.isDark(r,i)&&s++;return e+=10*(Math.abs(100*s/n/n-50)/5)}},p={glog:function(t){if(t<1)throw new Error("glog("+t+")");return p.LOG_TABLE[t]},gexp:function(t){for(;t<0;)t+=255;for(;256<=t;)t-=255;return p.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},t=0;t<8;t++)p.EXP_TABLE[t]=1<<t;for(t=8;t<256;t++)p.EXP_TABLE[t]=p.EXP_TABLE[t-4]^p.EXP_TABLE[t-5]^p.EXP_TABLE[t-6]^p.EXP_TABLE[t-8];for(t=0;t<255;t++)p.LOG_TABLE[p.EXP_TABLE[t]]=t;function m(t,n){if(null==t.length)throw new Error(t.length+"/"+n);for(var e=0;e<t.length&&0==t[e];)e++;this.num=new Array(t.length-e+n);for(var r=0;r<t.length-e;r++)this.num[r]=t[r+e]}function g(t,n){this.totalCount=t,this.dataCount=n}function y(){this.buffer=[],this.length=0}m.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var n=new Array(this.getLength()+t.getLength()-1),e=0;e<this.getLength();e++)for(var r=0;r<t.getLength();r++)n[e+r]^=p.gexp(p.glog(this.get(e))+p.glog(t.get(r)));return new m(n,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var n=p.glog(this.get(0))-p.glog(t.get(0)),e=new Array(this.getLength()),r=0;r<this.getLength();r++)e[r]=this.get(r);for(r=0;r<t.getLength();r++)e[r]^=p.gexp(p.glog(t.get(r))+n);return new m(e,0).mod(t)}},g.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],g.getRSBlocks=function(t,n){var e=g.getRsBlockTable(t,n);if(null==e)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+n);for(var r=e.length/3,i=[],o=0;o<r;o++)for(var a=e[3*o+0],u=e[3*o+1],c=e[3*o+2],l=0;l<a;l++)i.push(new g(u,c));return i},g.getRsBlockTable=function(t,n){switch(n){case l.L:return g.RS_BLOCK_TABLE[4*(t-1)+0];case l.M:return g.RS_BLOCK_TABLE[4*(t-1)+1];case l.Q:return g.RS_BLOCK_TABLE[4*(t-1)+2];case l.H:return g.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},y.prototype={get:function(t){var n=Math.floor(t/8);return 1==(this.buffer[n]>>>7-t%8&1)},put:function(t,n){for(var e=0;e<n;e++)this.putBit(1==(t>>>n-e-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),t&&(this.buffer[n]|=128>>>this.length%8),this.length++}};var b=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];function _(){var t=!1,n=navigator.userAgent;return t=/android/i.test(n)&&(t=!0,n=n.toString().match(/android ([0-9]\.[0-9])/i))&&n[1]?parseFloat(n[1]):t}n.prototype.draw=function(t){var n=this._htOption,e=this._el,r=t.getModuleCount();Math.floor(n.width/r),Math.floor(n.height/r);function i(t,n){var e,r=document.createElementNS("http://www.w3.org/2000/svg",t);for(e in n)n.hasOwnProperty(e)&&r.setAttribute(e,n[e]);return r}this.clear();var o=i("svg",{viewBox:"0 0 "+String(r)+" "+String(r),width:"100%",height:"100%",fill:n.colorLight});o.setAttributeNS("http://www.w3.org/2000/xmlns/","xmlns:xlink","http://www.w3.org/1999/xlink"),e.appendChild(o),o.appendChild(i("rect",{fill:n.colorLight,width:"100%",height:"100%"})),o.appendChild(i("rect",{fill:n.colorDark,width:"1",height:"1",id:"template"}));for(var a=0;a<r;a++)for(var u,c=0;c<r;c++)t.isDark(a,c)&&((u=i("use",{x:String(c),y:String(a)})).setAttributeNS("http://www.w3.org/1999/xlink","href","#template"),o.appendChild(u))},n.prototype.clear=function(){for(;this._el.hasChildNodes();)this._el.removeChild(this._el.lastChild)};var w=n;function n(t,n){this._el=t,this._htOption=n}var x="svg"===document.documentElement.tagName.toLowerCase()?w:"undefined"==typeof CanvasRenderingContext2D?(M.prototype.draw=function(t){for(var n=this._htOption,e=this._el,r=t.getModuleCount(),i=Math.floor(n.width/r),o=Math.floor(n.height/r),a=['<table style="border:0;border-collapse:collapse;">'],u=0;u<r;u++){a.push("<tr>");for(var c=0;c<r;c++)a.push('<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:'+i+"px;height:"+o+"px;background-color:"+(t.isDark(u,c)?n.colorDark:n.colorLight)+';"></td>');a.push("</tr>")}a.push("</table>"),e.innerHTML=a.join("");var e=e.childNodes[0],l=(n.width-e.offsetWidth)/2,s=(n.height-e.offsetHeight)/2;0<l&&0<s&&(e.style.margin=s+"px "+l+"px")},M.prototype.clear=function(){this._el.innerHTML=""},M):function(){function t(){this._elImage.src=this._elCanvas.toDataURL("image/png"),this._elImage.style.display="block",this._elCanvas.style.display="none"}var s,f;this._android&&this._android<=2.1&&(s=1/window.devicePixelRatio,f=CanvasRenderingContext2D.prototype.drawImage,CanvasRenderingContext2D.prototype.drawImage=function(t,n,e,r,i,o,a,u,c){if("nodeName"in t&&/img/i.test(t.nodeName))for(var l=arguments.length-1;1<=l;l--)arguments[l]=arguments[l]*s;else void 0===u&&(arguments[1]*=s,arguments[2]*=s,arguments[3]*=s,arguments[4]*=s);f.apply(this,arguments)});function n(t,n){this._bIsPainted=!1,this._android=_(),this._htOption=n,this._elCanvas=document.createElement("canvas"),this._elCanvas.width=n.width,this._elCanvas.height=n.height,t.appendChild(this._elCanvas),this._el=t,this._oContext=this._elCanvas.getContext("2d"),this._bIsPainted=!1,this._elImage=document.createElement("img"),this._elImage.alt="Scan me!",this._elImage.style.display="none",this._el.appendChild(this._elImage),this._bSupportDataURI=null}return n.prototype.draw=function(t){var n=this._elImage,e=this._oContext,r=this._htOption,i=t.getModuleCount(),o=r.width/i,a=r.height/i,u=Math.round(o),c=Math.round(a);n.style.display="none",this.clear();for(var l=0;l<i;l++)for(var s=0;s<i;s++){var f=t.isDark(l,s),h=s*o,d=l*a;e.strokeStyle=f?r.colorDark:r.colorLight,e.lineWidth=1,e.fillStyle=f?r.colorDark:r.colorLight,e.fillRect(h,d,o,a),e.strokeRect(Math.floor(h)+.5,Math.floor(d)+.5,u,c),e.strokeRect(Math.ceil(h)-.5,Math.ceil(d)-.5,u,c)}this._bIsPainted=!0},n.prototype.makeImage=function(){this._bIsPainted&&!(function(t,n){var e,r,i=this;i._fFail=n,i._fSuccess=t,null===i._bSupportDataURI?((e=document.createElement("img")).onabort=r=function(){i._bSupportDataURI=!1,i._fFail&&i._fFail.call(i)},e.onerror=r,e.onload=function(){i._bSupportDataURI=!0,i._fSuccess&&i._fSuccess.call(i)},e.src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="):!0===i._bSupportDataURI&&i._fSuccess?i._fSuccess.call(i):!1===i._bSupportDataURI&&i._fFail&&i._fFail.call(i)}).call(this,t)},n.prototype.isPainted=function(){return this._bIsPainted},n.prototype.clear=function(){this._oContext.clearRect(0,0,this._elCanvas.width,this._elCanvas.height),this._bIsPainted=!1},n.prototype.round=function(t){return t&&Math.floor(1e3*t)/1e3},n}();function M(t,n){this._el=t,this._htOption=n}function E(t,n){for(var e,r=1,i=(t=t,(e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a")).length+(e.length!=t?3:0)),o=0,a=b.length;o<=a;o++){var u=0;switch(n){case l.L:u=b[o][0];break;case l.M:u=b[o][1];break;case l.Q:u=b[o][2];break;case l.H:u=b[o][3]}if(i<=u)break;r++}if(b.length<r)throw new Error("Too long data");return r}(QRCode=function(t,n){if(this._htOption={width:256,height:256,typeNumber:4,colorDark:"#000000",colorLight:"#ffffff",correctLevel:l.H},n="string"==typeof n?{text:n}:n)for(var e in n)this._htOption[e]=n[e];"string"==typeof t&&(t=document.getElementById(t)),this._htOption.useSVG&&(x=w),this._android=_(),this._el=t,this._oQRCode=null,this._oDrawing=new x(this._el,this._htOption),this._htOption.text&&this.makeCode(this._htOption.text)}).prototype.makeCode=function(t){this._oQRCode=new c(E(t,this._htOption.correctLevel),this._htOption.correctLevel),this._oQRCode.addData(t),this._oQRCode.make(),this._el.title=t,this._oDrawing.draw(this._oQRCode),this.makeImage()},QRCode.prototype.makeImage=function(){"function"==typeof this._oDrawing.makeImage&&(!this._android||3<=this._android)&&this._oDrawing.makeImage()},QRCode.prototype.clear=function(){this._oDrawing.clear()},QRCode.CorrectLevel=l}(),function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t=t||self).Sortable=n()}(this,function(){function n(n,t){var e,r=Object.keys(n);return Object.getOwnPropertySymbols&&(e=Object.getOwnPropertySymbols(n),t&&(e=e.filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})),r.push.apply(r,e)),r}function L(r){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?n(Object(i),!0).forEach(function(t){var n,e;n=r,e=i[t=t],t in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(t){Object.defineProperty(r,t,Object.getOwnPropertyDescriptor(i,t))})}return r}function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(){return(a=Object.assign||function(t){for(var n=1;n<arguments.length;n++){var e,r=arguments[n];for(e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])}return t}).apply(this,arguments)}function h(t,n){if(null==t)return{};var e,r=function(t,n){if(null==t)return{};for(var e,r={},i=Object.keys(t),o=0;o<i.length;o++)e=i[o],0<=n.indexOf(e)||(r[e]=t[e]);return r}(t,n);if(Object.getOwnPropertySymbols)for(var i=Object.getOwnPropertySymbols(t),o=0;o<i.length;o++)e=i[o],0<=n.indexOf(e)||Object.prototype.propertyIsEnumerable.call(t,e)&&(r[e]=t[e]);return r}function d(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){var e;if(t)return"string"==typeof t?p(t,n):"Map"===(e="Object"===(e=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:e)||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?p(t,n):void 0}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function t(t){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(t)}var _=t(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),E=t(/Edge/i),g=t(/firefox/i),m=t(/safari/i)&&!t(/chrome/i)&&!t(/android/i),e=t(/iP(ad|od|hone)/i),A=t(/chrome/i)&&t(/android/i),S={capture:!1,passive:!1};function c(t,n,e){t.addEventListener(n,e,!_&&S)}function o(t,n,e){t.removeEventListener(n,e,!_&&S)}function T(t,n){if(n&&(">"===n[0]&&(n=n.substring(1)),t))try{if(t.matches)return t.matches(n);if(t.msMatchesSelector)return t.msMatchesSelector(n);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(n)}catch(t){return}}function I(t,n,e,r){if(t){e=e||document;do{if(null!=n&&(">"!==n[0]||t.parentNode===e)&&T(t,n)||r&&t===e)return t}while(t!==e&&(t=(i=t).host&&i!==document&&i.host.nodeType?i.host:i.parentNode))}var i;return null}var D,P=/\s+/g;function R(t,n,e){var r;t&&n&&(t.classList?t.classList[e?"add":"remove"](n):(r=(" "+t.className+" ").replace(P," ").replace(" "+n+" "," "),t.className=(r+(e?" "+n:"")).replace(P," ")))}function O(t,n,e){var r=t&&t.style;if(r){if(void 0===e)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(e=t.currentStyle),void 0===n?e:e[n];r[n=n in r||-1!==n.indexOf("webkit")?n:"-webkit-"+n]=e+("string"==typeof e?"":"px")}}function v(t,n){var e="";if("string"==typeof t)e=t;else do{var r=O(t,"transform")}while(r&&"none"!==r&&(e=r+" "+e),!n&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(e)}function $(t,n,e){if(t){var r=t.getElementsByTagName(n),i=0,o=r.length;if(e)for(;i<o;i++)e(r[i],i);return r}return[]}function k(){var t=document.scrollingElement;return t||document.documentElement}function B(t,n,e,r,i){if(t.getBoundingClientRect||t===window){var o,a,u,c,l,s,f,h,d,p=t!==window&&t.parentNode&&t!==k()?(a=(o=t.getBoundingClientRect()).top,u=o.left,c=o.bottom,l=o.right,s=o.height,o.width):(u=a=0,c=window.innerHeight,l=window.innerWidth,s=window.innerHeight,window.innerWidth);if((n||e)&&t!==window&&(i=i||t.parentNode,!_))do{if(i&&i.getBoundingClientRect&&("none"!==O(i,"transform")||e&&"static"!==O(i,"position"))){var g=i.getBoundingClientRect();a-=g.top+parseInt(O(i,"border-top-width")),u-=g.left+parseInt(O(i,"border-left-width")),c=a+o.height,l=u+o.width;break}}while(i=i.parentNode);return r&&t!==window&&(h=(f=v(i||t))&&f.a,d=f&&f.d,f)&&(c=(a/=d)+(s/=d),l=(u/=h)+(p/=h)),{top:a,left:u,bottom:c,right:l,width:p,height:s}}}function J(t,n,e){for(var r=C(t,!0),i=B(t)[n];r;){var o=B(r)[e];if(!("top"===e||"left"===e?o<=i:i<=o))return r;if(r===k())break;r=C(r,!1)}return!1}function K(t,n,e,r){for(var i=0,o=0,a=t.children;o<a.length;){if("none"!==a[o].style.display&&a[o]!==q.ghost&&(r||a[o]!==q.dragged)&&I(a[o],e.draggable,t,!1)){if(i===n)return a[o];i++}o++}return null}function Q(t,n){for(var e=t.lastElementChild;e&&(e===q.ghost||"none"===O(e,"display")||n&&!T(e,n));)e=e.previousElementSibling;return e||null}function F(t,n){var e=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)"TEMPLATE"===t.nodeName.toUpperCase()||t===q.clone||n&&!T(t,n)||e++;return e}function tt(t){var n=0,e=0,r=k();if(t)do{var i=v(t),o=i.a,i=i.d}while(n+=t.scrollLeft*o,e+=t.scrollTop*i,t!==r&&(t=t.parentNode));return[n,e]}function C(t,n){if(t&&t.getBoundingClientRect){var e=t,r=!1;do{if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var i=O(e);if(e.clientWidth<e.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||e.clientHeight<e.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!e.getBoundingClientRect||e===document.body)return k();if(r||n)return e;r=!0}}}while(e=e.parentNode)}return k()}function nt(t,n){return Math.round(t.top)===Math.round(n.top)&&Math.round(t.left)===Math.round(n.left)&&Math.round(t.height)===Math.round(n.height)&&Math.round(t.width)===Math.round(n.width)}function et(n,e){return function(){var t;D||(1===(t=arguments).length?n.call(this,t[0]):n.apply(this,t),D=setTimeout(function(){D=void 0},e))}}function rt(t,n,e){t.scrollLeft+=n,t.scrollTop+=e}function it(t){var n=window.Polymer,e=window.jQuery||window.Zepto;return n&&n.dom?n.dom(t).cloneNode(!0):e?e(t).clone(!0)[0]:t.cloneNode(!0)}function ot(t,n){O(t,"position","absolute"),O(t,"top",n.top),O(t,"left",n.left),O(t,"width",n.width),O(t,"height",n.height)}function at(t){O(t,"position",""),O(t,"top",""),O(t,"left",""),O(t,"width",""),O(t,"height","")}function ut(r,i,o){var a={};return Array.from(r.children).forEach(function(t){var n,e;I(t,i.draggable,r,!1)&&!t.animated&&t!==o&&(e=B(t),a.left=Math.min(null!=(n=a.left)?n:1/0,e.left),a.top=Math.min(null!=(n=a.top)?n:1/0,e.top),a.right=Math.max(null!=(n=a.right)?n:-1/0,e.right),a.bottom=Math.max(null!=(n=a.bottom)?n:-1/0,e.bottom))}),a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}var Y="Sortable"+(new Date).getTime();function ct(){var n,r=[];return{captureAnimationState:function(){r=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(t){var n,e;"none"!==O(t,"display")&&t!==q.ghost&&(r.push({target:t,rect:B(t)}),n=L({},r[r.length-1].rect),t.thisAnimationDuration&&(e=v(t,!0))&&(n.top-=e.f,n.left-=e.e),t.fromRect=n)})},addAnimationState:function(t){r.push(t)},removeAnimationState:function(t){r.splice(function(t,n){for(var e in t)if(t.hasOwnProperty(e))for(var r in n)if(n.hasOwnProperty(r)&&n[r]===t[e][r])return Number(e);return-1}(r,{target:t}),1)},animateAll:function(t){var h,d,p=this;this.options.animation?(h=!1,d=0,r.forEach(function(t){var n,e,r,i=0,o=t.target,a=o.fromRect,u=B(o),c=o.prevFromRect,l=o.prevToRect,s=t.rect,f=v(o,!0);f&&(u.top-=f.f,u.left-=f.e),o.toRect=u,o.thisAnimationDuration&&nt(c,u)&&!nt(a,u)&&(s.top-u.top)/(s.left-u.left)==(a.top-u.top)/(a.left-u.left)&&(t=s,n=c,e=l,r=p.options,i=Math.sqrt(Math.pow(n.top-t.top,2)+Math.pow(n.left-t.left,2))/Math.sqrt(Math.pow(n.top-e.top,2)+Math.pow(n.left-e.left,2))*r.animation),nt(u,a)||(o.prevFromRect=a,o.prevToRect=u,i=i||p.options.animation,p.animate(o,s,u,i)),i&&(h=!0,d=Math.max(d,i),clearTimeout(o.animationResetTimer),o.animationResetTimer=setTimeout(function(){o.animationTime=0,o.prevFromRect=null,o.fromRect=null,o.prevToRect=null,o.thisAnimationDuration=null},i),o.thisAnimationDuration=i)}),clearTimeout(n),h?n=setTimeout(function(){"function"==typeof t&&t()},d):"function"==typeof t&&t(),r=[]):(clearTimeout(n),"function"==typeof t&&t())},animate:function(t,n,e,r){var i,o;r&&(O(t,"transition",""),O(t,"transform",""),i=(o=v(this.el))&&o.a,o=o&&o.d,i=(n.left-e.left)/(i||1),o=(n.top-e.top)/(o||1),t.animatingX=!!i,t.animatingY=!!o,O(t,"transform","translate3d("+i+"px,"+o+"px,0)"),this.forRepaintDummy=t.offsetWidth,O(t,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),O(t,"transform","translate3d(0,0,0)"),"number"==typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout(function(){O(t,"transition",""),O(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},r))}}}var lt=[],st={initializeByDefault:!0},ft={mount:function(n){for(var t in st)!st.hasOwnProperty(t)||t in n||(n[t]=st[t]);lt.forEach(function(t){if(t.pluginName===n.pluginName)throw"Sortable: Cannot mount plugin ".concat(n.pluginName," more than once")}),lt.push(n)},pluginEvent:function(n,e,r){var t=this,i=(this.eventCanceled=!1,r.cancel=function(){t.eventCanceled=!0},n+"Global");lt.forEach(function(t){e[t.pluginName]&&(e[t.pluginName][i]&&e[t.pluginName][i](L({sortable:e},r)),e.options[t.pluginName])&&e[t.pluginName][n]&&e[t.pluginName][n](L({sortable:e},r))})},initializePlugins:function(r,i,o,t){for(var n in lt.forEach(function(t){var n,e=t.pluginName;(r.options[e]||t.initializeByDefault)&&((n=new t(r,i,r.options)).sortable=r,n.options=r.options,r[e]=n,a(o,n.defaults))}),r.options){var e;r.options.hasOwnProperty(n)&&void 0!==(e=this.modifyOption(r,n,r.options[n]))&&(r.options[n]=e)}},getEventProperties:function(n,e){var r={};return lt.forEach(function(t){"function"==typeof t.eventProperties&&a(r,t.eventProperties.call(e[t.pluginName],n))}),r},modifyOption:function(n,e,r){var i;return lt.forEach(function(t){n[t.pluginName]&&t.optionListeners&&"function"==typeof t.optionListeners[e]&&(i=t.optionListeners[e].call(n[t.pluginName],r))}),i}};function ht(t){var n=t.sortable,e=t.rootEl,r=t.name,i=t.targetEl,o=t.cloneEl,a=t.toEl,u=t.fromEl,c=t.oldIndex,l=t.newIndex,s=t.oldDraggableIndex,f=t.newDraggableIndex,h=t.originalEvent,d=t.putSortable,p=t.extraEventProperties;if(n=n||e&&e[Y]){var g,v,m=n.options,y="on"+r.charAt(0).toUpperCase()+r.substr(1),b=(!window.CustomEvent||_||E?(g=document.createEvent("Event")).initEvent(r,!0,!0):g=new CustomEvent(r,{bubbles:!0,cancelable:!0}),g.to=a||e,g.from=u||e,g.item=i||e,g.clone=o,g.oldIndex=c,g.newIndex=l,g.oldDraggableIndex=s,g.newDraggableIndex=f,g.originalEvent=h,g.pullMode=d?d.lastPutMode:void 0,L(L({},p),ft.getEventProperties(r,n)));for(v in b)g[v]=b[v];e&&e.dispatchEvent(g),m[y]&&m[y].call(n,g)}}function z(t,n){var e=(r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).evt,r=h(r,dt);ft.pluginEvent.bind(q)(t,n,L({dragEl:j,parentEl:V,ghostEl:X,rootEl:U,nextEl:_t,lastDownEl:wt,cloneEl:u,cloneHidden:i,dragStarted:Nt,putSortable:Z,activeSortable:q.active,originalEvent:e,oldIndex:xt,oldDraggableIndex:Mt,newIndex:W,newDraggableIndex:G,hideGhostForTarget:vt,unhideGhostForTarget:mt,cloneNowHidden:function(){i=!0},cloneNowShown:function(){i=!1},dispatchSortableEvent:function(t){H({sortable:n,name:t,originalEvent:e})}},r))}var dt=["evt"];function H(t){ht(L({putSortable:Z,cloneEl:u,targetEl:j,rootEl:U,oldIndex:xt,oldDraggableIndex:Mt,newIndex:W,newDraggableIndex:G},t))}function pt(t,n){var e,r=O(t),i=parseInt(r.width)-parseInt(r.paddingLeft)-parseInt(r.paddingRight)-parseInt(r.borderLeftWidth)-parseInt(r.borderRightWidth),o=K(t,0,n),a=K(t,1,n),u=o&&O(o),c=a&&O(a),l=u&&parseInt(u.marginLeft)+parseInt(u.marginRight)+B(o).width,s=c&&parseInt(c.marginLeft)+parseInt(c.marginRight)+B(a).width;return"flex"===r.display?"column"===r.flexDirection||"column-reverse"===r.flexDirection?"vertical":"horizontal":"grid"===r.display?r.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal":o&&u.float&&"none"!==u.float?(e="left"===u.float?"left":"right",!a||"both"!==c.clear&&c.clear!==e?"horizontal":"vertical"):o&&("block"===u.display||"flex"===u.display||"table"===u.display||"grid"===u.display||i<=l&&"none"===r[Xt]||a&&"none"===r[Xt]&&i<l+s)?"vertical":"horizontal"}function gt(t){function u(o,a){return function(t,n,e,r){var i=t.options.group.name&&n.options.group.name&&t.options.group.name===n.options.group.name;return!(null!=o||!a&&!i)||null!=o&&!1!==o&&(a&&"clone"===o?o:"function"==typeof o?u(o(t,n,e,r),a)(t,n,e,r):(i=(a?t:n).options.group.name,!0===o||"string"==typeof o&&o===i||o.join&&-1<o.indexOf(i)))}}var n={},e=t.group;e&&"object"==r(e)||(e={name:e}),n.name=e.name,n.checkPull=u(e.pull,!0),n.checkPut=u(e.put),n.revertClone=e.revertClone,t.group=n}function vt(){!Wt&&X&&O(X,"display","none")}function mt(){!Wt&&X&&O(X,"display","")}function yt(t){if(j){t=t.touches?t.touches[0]:t;i=t.clientX,o=t.clientY,Ot.some(function(t){var n,e,r=t[Y].options.emptyInsertThreshold;if(r&&!Q(t))return e=B(t),n=i>=e.left-r&&i<=e.right+r,e=o>=e.top-r&&o<=e.bottom+r,n&&e?a=t:void 0});var n=a;if(n){var e,r={};for(e in t)t.hasOwnProperty(e)&&(r[e]=t[e]);r.target=r.rootEl=n,r.preventDefault=void 0,r.stopPropagation=void 0,n[Y]._onDragOver(r)}}var i,o,a}function bt(t){j&&j.parentNode[Y]._isOutsideThisEl(t.target)}var j,V,X,U,_t,wt,u,i,xt,W,Mt,G,Et,Z,At,l,St,Tt,kt,Ct,Nt,Dt,Pt,Lt,s,It=!1,Rt=!1,Ot=[],Bt=!1,Ft=!1,Yt=[],zt=!1,Ht=[],jt="undefined"!=typeof document,Vt=e,Xt=E||_?"cssFloat":"float",Ut=jt&&!A&&!e&&"draggable"in document.createElement("div"),Wt=function(){var t;if(jt)return!_&&((t=document.createElement("x")).style.cssText="pointer-events:auto","auto"===t.style.pointerEvents)}();jt&&!A&&document.addEventListener("click",function(t){if(Rt)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),Rt=!1},!0);function q(t,n){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=n=a({},n),t[Y]=this;var e,r,i={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return pt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,n){t.setData("Text",n.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==q.supportPointer&&"PointerEvent"in window&&!m,emptyInsertThreshold:5};for(e in ft.initializePlugins(this,t,i),i)e in n||(n[e]=i[e]);for(r in gt(n),this)"_"===r.charAt(0)&&"function"==typeof this[r]&&(this[r]=this[r].bind(this));this.nativeDraggable=!n.forceFallback&&Ut,this.nativeDraggable&&(this.options.touchStartThreshold=1),n.supportPointer?c(t,"pointerdown",this._onTapStart):(c(t,"mousedown",this._onTapStart),c(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(c(t,"dragover",this),c(t,"dragenter",this)),Ot.push(this.el),n.store&&n.store.get&&this.sort(n.store.get(this)||[]),a(this,ct())}function Gt(t,n,e,r,i,o,a,u){var c,l,s=t[Y],f=s.options.onMove;return!window.CustomEvent||_||E?(c=document.createEvent("Event")).initEvent("move",!0,!0):c=new CustomEvent("move",{bubbles:!0,cancelable:!0}),c.to=n,c.from=t,c.dragged=e,c.draggedRect=r,c.related=i||n,c.relatedRect=o||B(n),c.willInsertAfter=u,c.originalEvent=a,t.dispatchEvent(c),l=f?f.call(s,c,a):l}function Zt(t){t.draggable=!1}function qt(){zt=!1}function $t(t){return setTimeout(t,0)}function Jt(t){return clearTimeout(t)}q.prototype={constructor:q,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(Dt=null)},_getDirection:function(t,n){return"function"==typeof this.options.direction?this.options.direction.call(this,t,n,j):this.options.direction},_onTapStart:function(n){if(n.cancelable){for(var e=this,r=this.el,t=this.options,i=t.preventOnFilter,o=n.type,a=n.touches&&n.touches[0]||n.pointerType&&"touch"===n.pointerType&&n,u=(a||n).target,c=n.target.shadowRoot&&(n.path&&n.path[0]||n.composedPath&&n.composedPath()[0])||u,l=t.filter,s=r,f=(Ht.length=0,s.getElementsByTagName("input")),h=f.length;h--;){var d=f[h];d.checked&&Ht.push(d)}if(!j&&!(/mousedown|pointerdown/.test(o)&&0!==n.button||t.disabled)&&!c.isContentEditable&&(this.nativeDraggable||!m||!u||"SELECT"!==u.tagName.toUpperCase())&&!((u=I(u,t.draggable,r,!1))&&u.animated||wt===u)){if(xt=F(u),Mt=F(u,t.draggable),"function"==typeof l){if(l.call(this,n,u,this))return H({sortable:e,rootEl:c,name:"filter",targetEl:u,toEl:r,fromEl:r}),z("filter",e,{evt:n}),void(i&&n.cancelable&&n.preventDefault())}else if(l=l&&l.split(",").some(function(t){if(t=I(c,t.trim(),r,!1))return H({sortable:e,rootEl:t,name:"filter",targetEl:u,fromEl:r,toEl:r}),z("filter",e,{evt:n}),!0}))return void(i&&n.cancelable&&n.preventDefault());t.handle&&!I(c,t.handle,r,!1)||this._prepareDragStart(n,a,u)}}},_prepareDragStart:function(t,n,e){var r,i=this,o=i.el,a=i.options,u=o.ownerDocument;e&&!j&&e.parentNode===o&&(r=B(e),U=o,V=(j=e).parentNode,_t=j.nextSibling,wt=e,Et=a.group,At={target:q.dragged=j,clientX:(n||t).clientX,clientY:(n||t).clientY},kt=At.clientX-r.left,Ct=At.clientY-r.top,this._lastX=(n||t).clientX,this._lastY=(n||t).clientY,j.style["will-change"]="all",o=function(){z("delayEnded",i,{evt:t}),q.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!g&&i.nativeDraggable&&(j.draggable=!0),i._triggerDragStart(t,n),H({sortable:i,name:"choose",originalEvent:t}),R(j,a.chosenClass,!0))},a.ignore.split(",").forEach(function(t){$(j,t.trim(),Zt)}),c(u,"dragover",yt),c(u,"mousemove",yt),c(u,"touchmove",yt),c(u,"mouseup",i._onDrop),c(u,"touchend",i._onDrop),c(u,"touchcancel",i._onDrop),g&&this.nativeDraggable&&(this.options.touchStartThreshold=4,j.draggable=!0),z("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!n||this.nativeDraggable&&(E||_)?o():q.eventCanceled?this._onDrop():(c(u,"mouseup",i._disableDelayedDrag),c(u,"touchend",i._disableDelayedDrag),c(u,"touchcancel",i._disableDelayedDrag),c(u,"mousemove",i._delayedDragTouchMoveHandler),c(u,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&c(u,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,a.delay)))},_delayedDragTouchMoveHandler:function(t){var n=t.touches?t.touches[0]:t;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){j&&Zt(j),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;o(t,"mouseup",this._disableDelayedDrag),o(t,"touchend",this._disableDelayedDrag),o(t,"touchcancel",this._disableDelayedDrag),o(t,"mousemove",this._delayedDragTouchMoveHandler),o(t,"touchmove",this._delayedDragTouchMoveHandler),o(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,n){n=n||"touch"==t.pointerType&&t,!this.nativeDraggable||n?this.options.supportPointer?c(document,"pointermove",this._onTouchMove):c(document,n?"touchmove":"mousemove",this._onTouchMove):(c(j,"dragend",this),c(U,"dragstart",this._onDragStart));try{document.selection?$t(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(t,n){var e;It=!1,U&&j?(z("dragStarted",this,{evt:n}),this.nativeDraggable&&c(document,"dragover",bt),e=this.options,t||R(j,e.dragClass,!1),R(j,e.ghostClass,!0),q.active=this,t&&this._appendGhost(),H({sortable:this,name:"start",originalEvent:n})):this._nulling()},_emulateDragOver:function(){if(l){this._lastX=l.clientX,this._lastY=l.clientY,vt();for(var t=document.elementFromPoint(l.clientX,l.clientY),n=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(l.clientX,l.clientY))!==n;)n=t;if(j.parentNode[Y]._isOutsideThisEl(t),n)do{if(n[Y])if(n[Y]._onDragOver({clientX:l.clientX,clientY:l.clientY,target:t,rootEl:n})&&!this.options.dragoverBubble)break}while(n=(t=n).parentNode);mt()}},_onTouchMove:function(t){if(At){var n=this.options,e=n.fallbackTolerance,n=n.fallbackOffset,r=t.touches?t.touches[0]:t,i=X&&v(X,!0),o=X&&i&&i.a,a=X&&i&&i.d,u=Vt&&s&&tt(s),o=(r.clientX-At.clientX+n.x)/(o||1)+(u?u[0]-Yt[0]:0)/(o||1),n=(r.clientY-At.clientY+n.y)/(a||1)+(u?u[1]-Yt[1]:0)/(a||1);if(!q.active&&!It){if(e&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<e)return;this._onDragStart(t,!0)}X&&(i?(i.e+=o-(St||0),i.f+=n-(Tt||0)):i={a:1,b:0,c:0,d:1,e:o,f:n},u="matrix(".concat(i.a,",").concat(i.b,",").concat(i.c,",").concat(i.d,",").concat(i.e,",").concat(i.f,")"),O(X,"webkitTransform",u),O(X,"mozTransform",u),O(X,"msTransform",u),O(X,"transform",u),St=o,Tt=n,l=r),t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!X){var t=this.options.fallbackOnBody?document.body:U,n=B(j,!0,Vt,!0,t),e=this.options;if(Vt){for(s=t;"static"===O(s,"position")&&"none"===O(s,"transform")&&s!==document;)s=s.parentNode;s!==document.body&&s!==document.documentElement?(s===document&&(s=k()),n.top+=s.scrollTop,n.left+=s.scrollLeft):s=k(),Yt=tt(s)}R(X=j.cloneNode(!0),e.ghostClass,!1),R(X,e.fallbackClass,!0),R(X,e.dragClass,!0),O(X,"transition",""),O(X,"transform",""),O(X,"box-sizing","border-box"),O(X,"margin",0),O(X,"top",n.top),O(X,"left",n.left),O(X,"width",n.width),O(X,"height",n.height),O(X,"opacity","0.8"),O(X,"position",Vt?"absolute":"fixed"),O(X,"zIndex","100000"),O(X,"pointerEvents","none"),q.ghost=X,t.appendChild(X),O(X,"transform-origin",kt/parseInt(X.style.width)*100+"% "+Ct/parseInt(X.style.height)*100+"%")}},_onDragStart:function(t,n){var e=this,r=t.dataTransfer,i=e.options;z("dragStart",this,{evt:t}),q.eventCanceled?this._onDrop():(z("setupClone",this),q.eventCanceled||((u=it(j)).removeAttribute("id"),u.draggable=!1,u.style["will-change"]="",this._hideClone(),R(u,this.options.chosenClass,!1),q.clone=u),e.cloneId=$t(function(){z("clone",e),q.eventCanceled||(e.options.removeCloneOnHide||U.insertBefore(u,j),e._hideClone(),H({sortable:e,name:"clone"}))}),n||R(j,i.dragClass,!0),n?(Rt=!0,e._loopId=setInterval(e._emulateDragOver,50)):(o(document,"mouseup",e._onDrop),o(document,"touchend",e._onDrop),o(document,"touchcancel",e._onDrop),r&&(r.effectAllowed="move",i.setData)&&i.setData.call(e,r,j),c(document,"drop",e),O(j,"transform","translateZ(0)")),It=!0,e._dragStartId=$t(e._dragStarted.bind(e,n,t)),c(document,"selectstart",e),Nt=!0,m&&O(document.body,"user-select","none"))},_onDragOver:function(e){var r,i,o,t,n,a,u=this.el,c=e.target,l=this.options,s=l.group,f=q.active,h=Et===s,d=l.sort,p=Z||f,g=this,v=!1;if(!zt){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),c=I(c,l.draggable,u,!0),C("dragOver"),q.eventCanceled)return v;if(j.contains(e.target)||c.animated&&c.animatingX&&c.animatingY||g._ignoreWhileAnimating===c)return D(!1);if(Rt=!1,f&&!l.disabled&&(h?d||(i=V!==U):Z===this||(this.lastPutMode=Et.checkPull(this,f,j,e))&&s.checkPut(this,f,j,e))){if(o="vertical"===this._getDirection(e,c),r=B(j),C("dragOverValid"),q.eventCanceled)return v;if(i)return V=U,N(),this._hideClone(),C("revert"),q.eventCanceled||(_t?U.insertBefore(j,_t):U.appendChild(j)),D(!0);s=Q(u,l.draggable);if(s&&(t=e,n=o,m=B(Q((a=this).el,a.options.draggable)),x=ut(a.el,a.options,X),!(n?t.clientX>x.right+10||t.clientY>m.bottom&&t.clientX>m.left:t.clientY>x.bottom+10||t.clientX>m.right&&t.clientY>m.top)||s.animated)){if(s&&(a=e,n=o,x=B(K((t=this).el,0,t.options,!0)),m=ut(t.el,t.options,X),n?a.clientX<m.left-10||a.clientY<x.top&&a.clientX<x.right:a.clientY<m.top-10||a.clientY<x.bottom&&a.clientX<x.left)){var m=K(u,0,l,!0);if(m===j)return D(!1);if(w=B(c=m),!1!==Gt(U,u,j,r,c,w,e,!1))return N(),u.insertBefore(j,m),V=u,P(),D(!0)}else if(c.parentNode===u){var y,b,_,w=B(c),x=j.parentNode!==u,M=(t=j.animated&&j.toRect||r,n=c.animated&&c.toRect||w,m=(a=o)?t.left:t.top,M=a?t.right:t.bottom,A=a?t.width:t.height,T=a?n.left:n.top,E=a?n.right:n.bottom,k=a?n.width:n.height,!(m===T||M===E||m+A/2===T+k/2)),E=o?"top":"left",m=J(c,"top","top")||J(j,"top","top"),A=m?m.scrollTop:void 0;if(Dt!==c&&(b=w[E],Bt=!1,Ft=!M&&l.invertSwap||x),0!==(y=function(t,n,e,r,i,o,a,u){var c=r?t.clientY:t.clientX,l=r?e.height:e.width,s=r?e.top:e.left,f=r?e.bottom:e.right,h=!1;if(!a)if(u&&Lt<l*i){if(Bt=!Bt&&(1===Pt?s+l*o/2<c:c<f-l*o/2)?!0:Bt)h=!0;else if(1===Pt?c<s+Lt:f-Lt<c)return-Pt}else if(s+l*(1-i)/2<c&&c<f-l*(1-i)/2)return function(t){return F(j)<F(t)?1:-1}(n);if((h=h||a)&&(c<s+l*o/2||f-l*o/2<c))return s+l/2<c?1:-1;return 0}(e,c,w,o,M?1:l.swapThreshold,null==l.invertedSwapThreshold?l.swapThreshold:l.invertedSwapThreshold,Ft,Dt===c)))for(var S=F(j);(_=V.children[S-=y])&&("none"===O(_,"display")||_===X););if(0===y||_===c)return D(!1);Pt=y;var T=(Dt=c).nextElementSibling,k=!1,x=Gt(U,u,j,r,c,w,e,k=1===y);if(!1!==x)return 1!==x&&-1!==x||(k=1===x),zt=!0,setTimeout(qt,30),N(),k&&!T?u.appendChild(j):c.parentNode.insertBefore(j,k?T:c),m&&rt(m,0,A-m.scrollTop),V=j.parentNode,void 0===b||Ft||(Lt=Math.abs(b-B(c)[E])),P(),D(!0)}}else{if(s===j)return D(!1);if((c=s&&u===e.target?s:c)&&(w=B(c)),!1!==Gt(U,u,j,r,c,w,e,!!c))return N(),s&&s.nextSibling?u.insertBefore(j,s.nextSibling):u.appendChild(j),V=u,P(),D(!0)}if(u.contains(j))return D(!1)}return!1}function C(t,n){z(t,g,L({evt:e,isOwner:h,axis:o?"vertical":"horizontal",revert:i,dragRect:r,targetRect:w,canSort:d,fromSortable:p,target:c,completed:D,onMove:function(t,n){return Gt(U,u,j,r,t,B(t),e,n)},changed:P},n))}function N(){C("dragOverAnimationCapture"),g.captureAnimationState(),g!==p&&p.captureAnimationState()}function D(t){return C("dragOverCompleted",{insertion:t}),t&&(h?f._hideClone():f._showClone(g),g!==p&&(R(j,(Z||f).options.ghostClass,!1),R(j,l.ghostClass,!0)),Z!==g&&g!==q.active?Z=g:g===q.active&&(Z=Z&&null),p===g&&(g._ignoreWhileAnimating=c),g.animateAll(function(){C("dragOverAnimationComplete"),g._ignoreWhileAnimating=null}),g!==p)&&(p.animateAll(),p._ignoreWhileAnimating=null),(c===j&&!j.animated||c===u&&!c.animated)&&(Dt=null),l.dragoverBubble||e.rootEl||c===document||(j.parentNode[Y]._isOutsideThisEl(e.target),t)||yt(e),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),v=!0}function P(){W=F(j),G=F(j,l.draggable),H({sortable:g,name:"change",toEl:u,newIndex:W,newDraggableIndex:G,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){o(document,"mousemove",this._onTouchMove),o(document,"touchmove",this._onTouchMove),o(document,"pointermove",this._onTouchMove),o(document,"dragover",yt),o(document,"mousemove",yt),o(document,"touchmove",yt)},_offUpEvents:function(){var t=this.el.ownerDocument;o(t,"mouseup",this._onDrop),o(t,"touchend",this._onDrop),o(t,"pointerup",this._onDrop),o(t,"touchcancel",this._onDrop),o(document,"selectstart",this)},_onDrop:function(t){var n=this.el,e=this.options;W=F(j),G=F(j,e.draggable),z("drop",this,{evt:t}),V=j&&j.parentNode,W=F(j),G=F(j,e.draggable),q.eventCanceled||(Bt=Ft=It=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Jt(this.cloneId),Jt(this._dragStartId),this.nativeDraggable&&(o(document,"drop",this),o(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),m&&O(document.body,"user-select",""),O(j,"transform",""),t&&(Nt&&(t.cancelable&&t.preventDefault(),e.dropBubble||t.stopPropagation()),X&&X.parentNode&&X.parentNode.removeChild(X),(U===V||Z&&"clone"!==Z.lastPutMode)&&u&&u.parentNode&&u.parentNode.removeChild(u),j)&&(this.nativeDraggable&&o(j,"dragend",this),Zt(j),j.style["will-change"]="",Nt&&!It&&R(j,(Z||this).options.ghostClass,!1),R(j,this.options.chosenClass,!1),H({sortable:this,name:"unchoose",toEl:V,newIndex:null,newDraggableIndex:null,originalEvent:t}),U!==V?(0<=W&&(H({rootEl:V,name:"add",toEl:V,fromEl:U,originalEvent:t}),H({sortable:this,name:"remove",toEl:V,originalEvent:t}),H({rootEl:V,name:"sort",toEl:V,fromEl:U,originalEvent:t}),H({sortable:this,name:"sort",toEl:V,originalEvent:t})),Z&&Z.save()):W!==xt&&0<=W&&(H({sortable:this,name:"update",toEl:V,originalEvent:t}),H({sortable:this,name:"sort",toEl:V,originalEvent:t})),q.active)&&(null!=W&&-1!==W||(W=xt,G=Mt),H({sortable:this,name:"end",toEl:V,originalEvent:t}),this.save())),this._nulling()},_nulling:function(){z("nulling",this),U=j=V=X=_t=u=wt=i=At=l=Nt=W=G=xt=Mt=Dt=Pt=Z=Et=q.dragged=q.ghost=q.clone=q.active=null,Ht.forEach(function(t){t.checked=!0}),Ht.length=St=Tt=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":var n;j&&(this._onDragOver(t),(n=t).dataTransfer&&(n.dataTransfer.dropEffect="move"),n.cancelable)&&n.preventDefault();break;case"selectstart":t.preventDefault()}},toArray:function(){for(var t,n=[],e=this.el.children,r=0,i=e.length,o=this.options;r<i;r++)I(t=e[r],o.draggable,this.el,!1)&&n.push(t.getAttribute(o.dataIdAttr)||function(t){var n=t.tagName+t.className+t.src+t.href+t.textContent,e=n.length,r=0;for(;e--;)r+=n.charCodeAt(e);return r.toString(36)}(t));return n},sort:function(t,n){var r={},i=this.el;this.toArray().forEach(function(t,n){var e=i.children[n];I(e,this.options.draggable,i,!1)&&(r[t]=e)},this),n&&this.captureAnimationState(),t.forEach(function(t){r[t]&&(i.removeChild(r[t]),i.appendChild(r[t]))}),n&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,n){return I(t,n||this.options.draggable,this.el,!1)},option:function(t,n){var e=this.options;if(void 0===n)return e[t];var r=ft.modifyOption(this,t,n);e[t]=void 0!==r?r:n,"group"===t&&gt(e)},destroy:function(){z("destroy",this);var t=this.el;t[Y]=null,o(t,"mousedown",this._onTapStart),o(t,"touchstart",this._onTapStart),o(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(o(t,"dragover",this),o(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ot.splice(Ot.indexOf(this.el),1),this.el=t=null},_hideClone:function(){i||(z("hideClone",this),q.eventCanceled)||(O(u,"display","none"),this.options.removeCloneOnHide&&u.parentNode&&u.parentNode.removeChild(u),i=!0)},_showClone:function(t){"clone"!==t.lastPutMode?this._hideClone():i&&(z("showClone",this),q.eventCanceled||(j.parentNode!=U||this.options.group.revertClone?_t?U.insertBefore(u,_t):U.appendChild(u):U.insertBefore(u,j),this.options.group.revertClone&&this.animate(j,u),O(u,"display",""),i=!1))}},jt&&c(document,"touchmove",function(t){(q.active||It)&&t.cancelable&&t.preventDefault()}),q.utils={on:c,off:o,css:O,find:$,is:function(t,n){return!!I(t,n,t,!1)},extend:function(t,n){if(t&&n)for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e]);return t},throttle:et,closest:I,toggleClass:R,clone:it,index:F,nextTick:$t,cancelNextTick:Jt,detectDirection:pt,getChild:K},q.get=function(t){return t[Y]},q.mount=function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];(n=n[0].constructor===Array?n[0]:n).forEach(function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(q.utils=L(L({},q.utils),t.utils)),ft.mount(t)})},q.create=function(t,n){return new q(t,n)};var Kt,Qt,tn,nn,en,rn,N=[],on=!(q.version="1.15.2");function an(){N.forEach(function(t){clearInterval(t.pid)}),N=[]}function un(){clearInterval(rn)}function cn(t){var n=t.originalEvent,e=t.putSortable,r=t.dragEl,i=t.activeSortable,o=t.dispatchSortableEvent,a=t.hideGhostForTarget,u=t.unhideGhostForTarget;n&&(i=e||i,a(),a=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,n=document.elementFromPoint(a.clientX,a.clientY),u(),i)&&!i.el.contains(n)&&(o("spill"),this.onSpill({dragEl:r,putSortable:e}))}var f,ln=et(function(e,t,n,r){if(t.scroll){var i,o=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,u=t.scrollSensitivity,c=t.scrollSpeed,l=k(),s=!1,f=0,h=Kt=Qt!==n&&(Qt=n,an(),Kt=t.scroll,i=t.scrollFn,!0===Kt)?C(n,!0):Kt;do{var d=h,p=B(d),g=p.top,v=p.bottom,m=p.left,y=p.right,b=p.width,p=p.height,_=void 0,w=void 0,x=d.scrollWidth,M=d.scrollHeight,E=O(d),A=d.scrollLeft,S=d.scrollTop,w=d===l?(_=b<x&&("auto"===E.overflowX||"scroll"===E.overflowX||"visible"===E.overflowX),p<M&&("auto"===E.overflowY||"scroll"===E.overflowY||"visible"===E.overflowY)):(_=b<x&&("auto"===E.overflowX||"scroll"===E.overflowX),p<M&&("auto"===E.overflowY||"scroll"===E.overflowY)),E=_&&(Math.abs(y-o)<=u&&A+b<x)-(Math.abs(m-o)<=u&&!!A),_=w&&(Math.abs(v-a)<=u&&S+p<M)-(Math.abs(g-a)<=u&&!!S);if(!N[f])for(var T=0;T<=f;T++)N[T]||(N[T]={});N[f].vx==E&&N[f].vy==_&&N[f].el===d||(N[f].el=d,N[f].vx=E,N[f].vy=_,clearInterval(N[f].pid),0==E&&0==_)||(s=!0,N[f].pid=setInterval((function(){r&&0===this.layer&&q.active._onTouchMove(en);var t=N[this.layer].vy?N[this.layer].vy*c:0,n=N[this.layer].vx?N[this.layer].vx*c:0;"function"==typeof i&&"continue"!==i.call(q.dragged.parentNode[Y],n,t,e,en,N[this.layer].el)||rt(N[this.layer].el,n,t)}).bind({layer:f}),24)),f++}while(t.bubbleScroll&&h!==l&&(h=C(h,!1)));on=s}},30);function sn(){}function fn(){}sn.prototype={startIndex:null,dragStart:function(t){var n=t.oldDraggableIndex;this.startIndex=n},onSpill:function(t){var n=t.dragEl,e=t.putSortable,r=(this.sortable.captureAnimationState(),e&&e.captureAnimationState(),K(this.sortable.el,this.startIndex,this.options));r?this.sortable.el.insertBefore(n,r):this.sortable.el.appendChild(n),this.sortable.animateAll(),e&&e.animateAll()},drop:cn},a(sn,{pluginName:"revertOnSpill"}),fn.prototype={onSpill:function(t){var n=t.dragEl,e=t.putSortable||this.sortable;e.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),e.animateAll()},drop:cn},a(fn,{pluginName:"removeOnSpill"});var hn,y,b,dn,pn,w=[],x=[],gn=!1,M=!1,vn=!1;function mn(r,i){x.forEach(function(t,n){var e=i.children[t.sortableIndex+(r?Number(n):0)];e?i.insertBefore(t,e):i.appendChild(t)})}function yn(){w.forEach(function(t){t!==b&&t.parentNode&&t.parentNode.removeChild(t)})}return q.mount(new function(){function t(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var n=t.originalEvent;this.sortable.nativeDraggable?c(document,"dragover",this._handleAutoScroll):this.options.supportPointer?c(document,"pointermove",this._handleFallbackAutoScroll):n.touches?c(document,"touchmove",this._handleFallbackAutoScroll):c(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var n=t.originalEvent;this.options.dragOverBubble||n.rootEl||this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?o(document,"dragover",this._handleAutoScroll):(o(document,"pointermove",this._handleFallbackAutoScroll),o(document,"touchmove",this._handleFallbackAutoScroll),o(document,"mousemove",this._handleFallbackAutoScroll)),un(),an(),clearTimeout(D),D=void 0},nulling:function(){en=Qt=Kt=on=rn=tn=nn=null,N.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(n,e){var r,i=this,o=(n.touches?n.touches[0]:n).clientX,a=(n.touches?n.touches[0]:n).clientY,t=document.elementFromPoint(o,a);en=n,e||this.options.forceAutoScrollFallback||E||_||m?(ln(n,this.options,t,e),r=C(t,!0),!on||rn&&o===tn&&a===nn||(rn&&un(),rn=setInterval(function(){var t=C(document.elementFromPoint(o,a),!0);t!==r&&(r=t,an()),ln(n,i.options,t,e)},10),tn=o,nn=a)):this.options.bubbleScroll&&C(t,!0)!==k()?ln(n,this.options,C(t,!1),!1):an()}},a(t,{pluginName:"scroll",initializeByDefault:!0})}),q.mount(fn,sn),q.mount(new function(){function t(){this.defaults={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){var n=t.dragEl;f=n},dragOverValid:function(t){var n,e=t.completed,r=t.target,i=t.onMove,o=t.activeSortable,a=t.changed,u=t.cancel;o.options.swap&&(o=this.sortable.el,n=this.options,r&&r!==o&&(o=f,f=!1!==i(r)?(R(r,n.swapClass,!0),r):null,o)&&o!==f&&R(o,n.swapClass,!1),a(),e(!0),u())},drop:function(t){var n,e,r=t.activeSortable,i=t.putSortable,o=t.dragEl,a=i||this.sortable,u=this.options;f&&R(f,u.swapClass,!1),f&&(u.swap||i&&i.options.swap)&&o!==f&&(a.captureAnimationState(),a!==r&&r.captureAnimationState(),t=f,u=(n=o).parentNode,i=t.parentNode,u&&i&&!u.isEqualNode(t)&&!i.isEqualNode(n)&&(o=F(n),e=F(t),u.isEqualNode(i)&&o<e&&e++,u.insertBefore(t,u.children[o]),i.insertBefore(n,i.children[e])),a.animateAll(),a!==r)&&r.animateAll()},nulling:function(){f=null}},a(t,{pluginName:"swap",eventProperties:function(){return{swapItem:f}}})}),q.mount(new function(){function t(r){for(var t in this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this));r.options.avoidImplicitDeselect||(r.options.supportPointer?c(document,"pointerup",this._deselectMultiDrag):(c(document,"mouseup",this._deselectMultiDrag),c(document,"touchend",this._deselectMultiDrag))),c(document,"keydown",this._checkKeyDown),c(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,avoidImplicitDeselect:!1,setData:function(t,n){var e="";w.length&&y===r?w.forEach(function(t,n){e+=(n?", ":"")+t.textContent}):e=n.textContent,t.setData("Text",e)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var n=t.dragEl;b=n},delayEnded:function(){this.isMultiDrag=~w.indexOf(b)},setupClone:function(t){var n=t.sortable,e=t.cancel;if(this.isMultiDrag){for(var r=0;r<w.length;r++)x.push(it(w[r])),x[r].sortableIndex=w[r].sortableIndex,x[r].draggable=!1,x[r].style["will-change"]="",R(x[r],this.options.selectedClass,!1),w[r]===b&&R(x[r],this.options.chosenClass,!1);n._hideClone(),e()}},clone:function(t){var n=t.sortable,e=t.rootEl,r=t.dispatchSortableEvent,i=t.cancel;this.isMultiDrag&&!this.options.removeCloneOnHide&&w.length&&y===n&&(mn(!0,e),r("clone"),i())},showClone:function(t){var n=t.cloneNowShown,e=t.rootEl,r=t.cancel;this.isMultiDrag&&(mn(!1,e),x.forEach(function(t){O(t,"display","")}),n(),pn=!1,r())},hideClone:function(t){var n=this,e=(t.sortable,t.cloneNowHidden),r=t.cancel;this.isMultiDrag&&(x.forEach(function(t){O(t,"display","none"),n.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)}),e(),pn=!0,r())},dragStartGlobal:function(t){t.sortable;!this.isMultiDrag&&y&&y.multiDrag._deselectMultiDrag(),w.forEach(function(t){t.sortableIndex=F(t)}),w=w.sort(function(t,n){return t.sortableIndex-n.sortableIndex}),vn=!0},dragStarted:function(t){var n,e=this,r=t.sortable;this.isMultiDrag&&(this.options.sort&&(r.captureAnimationState(),this.options.animation)&&(w.forEach(function(t){t!==b&&O(t,"position","absolute")}),n=B(b,!1,!0,!0),w.forEach(function(t){t!==b&&ot(t,n)}),gn=M=!0),r.animateAll(function(){gn=M=!1,e.options.animation&&w.forEach(function(t){at(t)}),e.options.sort&&yn()}))},dragOver:function(t){var n=t.target,e=t.completed,r=t.cancel;M&&~w.indexOf(n)&&(e(!1),r())},revert:function(t){var r,i,n=t.fromSortable,e=t.rootEl,o=t.sortable,a=t.dragRect;1<w.length&&(w.forEach(function(t){o.addAnimationState({target:t,rect:M?B(t):a}),at(t),t.fromRect=a,n.removeAnimationState(t)}),M=!1,r=!this.options.removeCloneOnHide,i=e,w.forEach(function(t,n){var e=i.children[t.sortableIndex+(r?Number(n):0)];e?i.insertBefore(t,e):i.appendChild(t)}))},dragOverCompleted:function(t){var n,e=t.sortable,r=t.isOwner,i=t.insertion,o=t.activeSortable,a=t.parentEl,u=t.putSortable,c=this.options;i&&(r&&o._hideClone(),gn=!1,c.animation&&1<w.length&&(M||!r&&!o.options.sort&&!u)&&(n=B(b,!1,!0,!0),w.forEach(function(t){t!==b&&(ot(t,n),a.appendChild(t))}),M=!0),r||(M||yn(),1<w.length?(i=pn,o._showClone(e),o.options.animation&&!pn&&i&&x.forEach(function(t){o.addAnimationState({target:t,rect:dn}),t.fromRect=dn,t.thisAnimationDuration=null})):o._showClone(e)))},dragOverAnimationCapture:function(t){var n=t.dragRect,e=t.isOwner,r=t.activeSortable;w.forEach(function(t){t.thisAnimationDuration=null}),r.options.animation&&!e&&r.multiDrag.isMultiDrag&&(dn=a({},n),e=v(b,!0),dn.top-=e.f,dn.left-=e.e)},dragOverAnimationComplete:function(){M&&(M=!1,yn())},drop:function(t){var n=t.originalEvent,e=t.rootEl,r=t.parentEl,i=t.sortable,o=t.dispatchSortableEvent,a=t.oldIndex,u=t.putSortable,c=u||this.sortable;if(n){var l,s,f,h=this.options,d=r.children;if(!vn)if(h.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),R(b,h.selectedClass,!~w.indexOf(b)),~w.indexOf(b))w.splice(w.indexOf(b),1),hn=null,ht({sortable:i,rootEl:e,name:"deselect",targetEl:b,originalEvent:n});else{if(w.push(b),ht({sortable:i,rootEl:e,name:"select",targetEl:b,originalEvent:n}),n.shiftKey&&hn&&i.el.contains(hn)){var p=F(hn),g=F(b);if(~p&&~g&&p!==g)for(var v,m=p<g?(v=p,g):(v=g,p+1);v<m;v++)~w.indexOf(d[v])||(R(d[v],h.selectedClass,!0),w.push(d[v]),ht({sortable:i,rootEl:e,name:"select",targetEl:d[v],originalEvent:n}))}else hn=b;y=c}vn&&this.isMultiDrag&&(M=!1,(r[Y].options.sort||r!==e)&&1<w.length&&(l=B(b),s=F(b,":not(."+this.options.selectedClass+")"),!gn&&h.animation&&(b.thisAnimationDuration=null),c.captureAnimationState(),gn||(h.animation&&(b.fromRect=l,w.forEach(function(t){var n;t.thisAnimationDuration=null,t!==b&&(n=M?B(t):l,t.fromRect=n,c.addAnimationState({target:t,rect:n}))})),yn(),w.forEach(function(t){d[s]?r.insertBefore(t,d[s]):r.appendChild(t),s++}),a===F(b)&&(f=!1,w.forEach(function(t){t.sortableIndex!==F(t)&&(f=!0)}),f)&&(o("update"),o("sort"))),w.forEach(function(t){at(t)}),c.animateAll()),y=c),(e===r||u&&"clone"!==u.lastPutMode)&&x.forEach(function(t){t.parentNode&&t.parentNode.removeChild(t)})}},nullingGlobal:function(){this.isMultiDrag=vn=!1,x.length=0},destroyGlobal:function(){this._deselectMultiDrag(),o(document,"pointerup",this._deselectMultiDrag),o(document,"mouseup",this._deselectMultiDrag),o(document,"touchend",this._deselectMultiDrag),o(document,"keydown",this._checkKeyDown),o(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!(void 0!==vn&&vn||y!==this.sortable||t&&I(t.target,this.options.draggable,this.sortable.el,!1)||t&&0!==t.button))for(;w.length;){var n=w[0];R(n,this.options.selectedClass,!1),w.shift(),ht({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:n,originalEvent:t})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},a(t,{pluginName:"multiDrag",utils:{select:function(t){var n=t.parentNode[Y];n&&n.options.multiDrag&&!~w.indexOf(t)&&(y&&y!==n&&(y.multiDrag._deselectMultiDrag(),y=n),R(t,n.options.selectedClass,!0),w.push(t))},deselect:function(t){var n=t.parentNode[Y],e=w.indexOf(t);n&&n.options.multiDrag&&~e&&(R(t,n.options.selectedClass,!1),w.splice(e,1))}},eventProperties:function(){var e=this,r=[],i=[];return w.forEach(function(t){var n;r.push({multiDragElement:t,index:t.sortableIndex}),n=M&&t!==b?-1:M?F(t,":not(."+e.options.selectedClass+")"):F(t),i.push({multiDragElement:t,index:n})}),{items:d(w),clones:[].concat(x),oldIndicies:r,newIndicies:i}},optionListeners:{multiDragKey:function(t){return"ctrl"===(t=t.toLowerCase())?t="Control":1<t.length&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}),q});